<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:fitsSystemWindows="true"
    android:background="@color/black">

    <ImageView
        android:id="@+id/iv_back"
        android:src="@drawable/ic_arrow_back"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:scaleType="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <ImageView
        android:id="@+id/iv_delete"
        android:src="@drawable/ic_purple_delete"
        android:scaleType="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="56dp"
        android:layout_height="56dp"
        app:tint="@color/white" />

    <com.canhub.cropper.CropImageView
        android:id="@+id/crop_image_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="14dp"
        app:layout_constraintTop_toBottomOf="@id/iv_back"
        app:layout_constraintBottom_toTopOf="@id/compose_view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <!-- 继续按钮 -->
    <androidx.compose.ui.platform.ComposeView
        android:id="@+id/compose_view"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginHorizontal="24dp"
        android:layout_marginBottom="66dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>