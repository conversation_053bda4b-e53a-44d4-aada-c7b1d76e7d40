<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="53dp"
    android:height="54dp"
    android:viewportWidth="53"
    android:viewportHeight="54">
  <path
      android:pathData="M14,0.5L39,0.5A14,14 0,0 1,53 14.5L53,39.5A14,14 0,0 1,39 53.5L14,53.5A14,14 0,0 1,0 39.5L0,14.5A14,14 0,0 1,14 0.5z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="26.5"
          android:startY="0.5"
          android:endX="26.5"
          android:endY="53.5"
          android:type="linear">
        <item android:offset="0" android:color="#FFCC6DE3"/>
        <item android:offset="1" android:color="#FFEDC9FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17,15.21L36,15.21A5.789,5.789 0,0 1,41.789 21L41.789,35A5.789,5.789 0,0 1,36 40.789L17,40.789A5.789,5.789 0,0 1,11.21 35L11.21,21A5.789,5.789 0,0 1,17 15.21z"
      android:strokeWidth="0.421053"
      android:strokeColor="#ffffff">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="26.5"
          android:startY="15"
          android:endX="26.5"
          android:endY="41"
          android:type="linear">
        <item android:offset="0" android:color="#7FFFFFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M18,22L25.807,27.081C26.662,27.637 27.768,27.625 28.611,27.049L36,22"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeLineCap="round">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="26.78"
          android:startY="11.2"
          android:endX="27.145"
          android:endY="27.202"
          android:type="linear">
        <item android:offset="0" android:color="#00FFFFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
