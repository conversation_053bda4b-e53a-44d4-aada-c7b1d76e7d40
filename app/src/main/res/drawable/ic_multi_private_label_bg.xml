<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="13dp"
    android:viewportWidth="24"
    android:viewportHeight="13">
  <path
      android:pathData="M2.902,2.22C3.255,0.91 4.443,0 5.799,0L20.085,0C22.059,0 23.496,1.874 22.982,3.78L21.098,10.78C20.745,12.089 19.557,13 18.201,13L3.915,13C1.941,13 0.504,11.126 1.018,9.22L2.902,2.22Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="31.827"
          android:startY="2.499"
          android:endX="-0.673"
          android:endY="15.499"
          android:type="linear">
        <item android:offset="0" android:color="#FFB038EC"/>
        <item android:offset="0.354" android:color="#FF700EDB"/>
        <item android:offset="1" android:color="#FF8C3DE2"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M2.902,2.22C3.255,0.91 4.443,0 5.799,0L20.085,0C22.059,0 23.496,1.874 22.982,3.78L21.098,10.78C20.745,12.089 19.557,13 18.201,13L3.915,13C1.941,13 0.504,11.126 1.018,9.22L2.902,2.22Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="1.827"
          android:centerY="14.499"
          android:gradientRadius="11.5"
          android:type="radial">
        <item android:offset="0" android:color="#FFDA73FF"/>
        <item android:offset="1" android:color="#00FFFDFF"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
