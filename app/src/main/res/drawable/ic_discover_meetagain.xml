<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="138dp"
    android:height="138dp"
    android:viewportWidth="138"
    android:viewportHeight="138">
  <path
      android:pathData="M39.94,38.6C42.75,31.11 50.58,26.78 58.41,28.37C62.18,29.13 66.09,28.69 69.59,27.1C76.87,23.8 85.47,26.27 89.88,32.93C92.01,36.13 95.09,38.59 98.69,39.94C106.17,42.75 110.5,50.58 108.92,58.41C108.15,62.18 108.6,66.09 110.18,69.59C113.49,76.87 111.02,85.47 104.36,89.88C101.15,92.01 98.7,95.09 97.35,98.69C94.54,106.17 86.71,110.5 78.88,108.92C75.11,108.15 71.19,108.6 67.69,110.18C60.41,113.49 51.82,111.02 47.4,104.36C45.28,101.15 42.2,98.7 38.6,97.35C31.11,94.54 26.78,86.71 28.37,78.88C29.13,75.11 28.69,71.19 27.1,67.69C23.8,60.41 26.27,51.82 32.93,47.4C36.13,45.28 38.59,42.2 39.94,38.6Z"
      android:strokeWidth="2"
      android:strokeColor="#ffffff">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="44.18"
          android:startY="24.46"
          android:endX="93.11"
          android:endY="112.82"
          android:type="linear">
        <item android:offset="0" android:color="#FFEBC1A1"/>
        <item android:offset="1" android:color="#FFFCEEE1"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M67.96,68.45m-30.4,16.83a34.75,34.75 0,1 1,60.8 -33.67a34.75,34.75 0,1 1,-60.8 33.67"
      android:strokeWidth="0.5"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="77.38"
          android:startY="85.46"
          android:endX="84.92"
          android:endY="99.07"
          android:type="linear">
        <item android:offset="0" android:color="#00926340"/>
        <item android:offset="1" android:color="#FF926340"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M98.58,51.49C107.95,68.4 101.83,89.7 84.92,99.07C68.01,108.43 46.71,102.31 37.34,85.4C28.94,70.23 33,51.52 46.14,41.07"
      android:strokeWidth="0.5"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="58.54"
          android:startY="51.44"
          android:endX="51.01"
          android:endY="37.83"
          android:type="linear">
        <item android:offset="0" android:color="#00926340"/>
        <item android:offset="1" android:color="#FF926340"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M37.11,84.96C46.23,102 67.44,108.42 84.48,99.3C101.52,90.18 107.94,68.97 98.82,51.93C90.64,36.64 72.72,29.9 56.82,35.26"
      android:strokeWidth="0.5"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="58.79"
          android:startY="51.3"
          android:endX="51.45"
          android:endY="37.59"
          android:type="linear">
        <item android:offset="0" android:color="#00926340"/>
        <item android:offset="1" android:color="#FF926340"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M52.62,51.39L53.64,57.14C53.71,57.55 54.14,57.73 54.41,57.47C56.32,55.65 62.15,49.27 63.11,38.25C63.16,37.75 62.61,37.44 62.31,37.8C60.59,39.8 56.78,44.01 54.1,45.31C54.1,45.31 55.59,43.59 56.18,41.38C56.24,41.16 56.17,40.92 56,40.78L50.59,36.09C50.27,35.81 49.82,36.07 49.85,36.52L50.22,43.11L45.73,49.63C45.5,49.96 45.69,50.45 46.06,50.5L52.62,51.39Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="52.18"
          android:startY="35.48"
          android:endX="57.6"
          android:endY="56.72"
          android:type="linear">
        <item android:offset="0" android:color="#FF926340"/>
        <item android:offset="0.47" android:color="#FFE1B595"/>
        <item android:offset="0.94" android:color="#FF926340"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M62.28,46.55C62.04,47.15 61.52,48.41 60.96,49.23C60.84,49.4 60.92,49.61 61.11,49.67L65.58,51.1C65.85,51.19 66.11,50.94 66.01,50.7C65.54,49.59 64.41,47.31 62.78,46.38C62.6,46.28 62.35,46.36 62.28,46.55Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="62.51"
          android:startY="46.35"
          android:endX="63.85"
          android:endY="51.6"
          android:type="linear">
        <item android:offset="0" android:color="#FF926340"/>
        <item android:offset="0.47" android:color="#FFE1B595"/>
        <item android:offset="0.94" android:color="#FF926340"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M46.51,95.76L102.93,64.51"
      android:strokeWidth="0.5"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="46.75"
          android:startY="96.2"
          android:endX="103.18"
          android:endY="64.95"
          android:type="linear">
        <item android:offset="0" android:color="#00926340"/>
        <item android:offset="0.51" android:color="#FF926340"/>
        <item android:offset="1" android:color="#00926340"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M34.15,73.45L90.58,42.21"
      android:strokeWidth="0.5"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="34.4"
          android:startY="73.89"
          android:endX="90.82"
          android:endY="42.64"
          android:type="linear">
        <item android:offset="0" android:color="#00926340"/>
        <item android:offset="0.51" android:color="#FF926340"/>
        <item android:offset="1" android:color="#00926340"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M38.38,78.68L40.08,77.74L44.42,84.53L43.42,75.89L45.22,74.89L46.3,87.3L44.88,88.09L38.38,78.68ZM37.34,79.26L39.12,78.27L42.32,86.34L43.24,88.99L41.13,90.16L37.34,79.26ZM44.42,75.34L46.21,74.35L49.99,85.26L47.86,86.43L46.89,83.57L44.42,75.34ZM54.52,82.93C54.02,83.19 53.55,83.34 53.09,83.36C52.64,83.38 52.21,83.3 51.82,83.11C51.43,82.92 51.08,82.65 50.77,82.3C50.46,81.95 50.2,81.52 49.98,81.04L49.69,80.29C49.48,79.77 49.33,79.25 49.25,78.72C49.17,78.19 49.17,77.68 49.27,77.19C49.35,76.7 49.54,76.25 49.84,75.84C50.14,75.43 50.56,75.08 51.11,74.79C51.62,74.52 52.09,74.4 52.52,74.43C52.96,74.46 53.36,74.59 53.71,74.84C54.07,75.08 54.39,75.4 54.67,75.79C54.95,76.18 55.18,76.6 55.37,77.07L55.76,78.12L50.86,80.84L50.33,79.3L53.19,77.73L53.1,77.48C53.03,77.29 52.94,77.1 52.84,76.92C52.73,76.73 52.6,76.6 52.45,76.52C52.29,76.44 52.1,76.45 51.86,76.56C51.6,76.69 51.42,76.87 51.33,77.07C51.25,77.28 51.23,77.51 51.26,77.76C51.3,78 51.36,78.24 51.45,78.49C51.54,78.73 51.63,78.96 51.72,79.17L52.02,79.92C52.11,80.13 52.21,80.32 52.31,80.52C52.42,80.71 52.54,80.87 52.68,81.01C52.82,81.14 52.98,81.22 53.17,81.26C53.35,81.29 53.57,81.24 53.81,81.1C54.12,80.95 54.37,80.74 54.57,80.48C54.77,80.22 54.92,79.93 55.03,79.61L56.46,80.5C56.42,80.86 56.29,81.19 56.1,81.5C55.9,81.8 55.67,82.08 55.38,82.32C55.11,82.57 54.82,82.77 54.52,82.93ZM61.06,79.31C60.56,79.57 60.08,79.72 59.63,79.74C59.17,79.76 58.75,79.68 58.35,79.49C57.96,79.3 57.61,79.03 57.3,78.68C57,78.33 56.73,77.91 56.52,77.42L56.22,76.67C56.01,76.15 55.86,75.63 55.78,75.1C55.7,74.58 55.71,74.07 55.8,73.57C55.89,73.08 56.08,72.63 56.37,72.22C56.67,71.81 57.09,71.46 57.65,71.17C58.15,70.9 58.63,70.79 59.06,70.82C59.49,70.84 59.89,70.98 60.25,71.22C60.61,71.46 60.92,71.78 61.2,72.17C61.48,72.56 61.71,72.98 61.9,73.45L62.3,74.5L57.39,77.22L56.86,75.68L59.72,74.11L59.64,73.86C59.57,73.67 59.48,73.48 59.37,73.3C59.27,73.12 59.14,72.98 58.98,72.9C58.83,72.82 58.63,72.83 58.4,72.94C58.13,73.08 57.95,73.25 57.87,73.46C57.79,73.66 57.76,73.89 57.8,74.14C57.83,74.38 57.89,74.63 57.98,74.87C58.08,75.12 58.16,75.34 58.25,75.55L58.56,76.3C58.64,76.51 58.74,76.71 58.85,76.9C58.95,77.09 59.07,77.25 59.22,77.39C59.35,77.52 59.52,77.6 59.7,77.64C59.88,77.67 60.1,77.62 60.35,77.49C60.65,77.33 60.91,77.12 61.1,76.86C61.3,76.6 61.46,76.31 61.57,75.99L63,76.88C62.95,77.24 62.83,77.57 62.63,77.88C62.44,78.19 62.2,78.46 61.92,78.71C61.64,78.95 61.35,79.15 61.06,79.31ZM64.88,67.35L65.45,69.03L61.71,71.1L61.14,69.42L64.88,67.35ZM61.21,66.99L63.24,65.87L65.89,73.43C65.94,73.56 65.99,73.67 66.05,73.78C66.11,73.89 66.18,73.96 66.27,74.01C66.37,74.05 66.49,74.03 66.64,73.96C66.72,73.92 66.79,73.87 66.86,73.82C66.94,73.76 67,73.71 67.07,73.66L67.79,75.35C67.66,75.5 67.52,75.63 67.36,75.76C67.2,75.88 67.03,75.99 66.85,76.09C66.41,76.32 66.01,76.41 65.64,76.35C65.27,76.29 64.95,76.12 64.66,75.84C64.38,75.55 64.15,75.2 63.96,74.79L61.21,66.99ZM74.24,69.5L72.92,65.85C72.86,65.73 72.8,65.6 72.73,65.47C72.67,65.34 72.58,65.24 72.48,65.18C72.37,65.12 72.24,65.13 72.07,65.23C71.9,65.32 71.79,65.43 71.75,65.56C71.7,65.68 71.7,65.82 71.73,65.97C71.76,66.12 71.8,66.27 71.86,66.42L69.83,67.55C69.64,67.16 69.54,66.76 69.54,66.38C69.54,65.98 69.62,65.61 69.78,65.26C69.93,64.9 70.15,64.58 70.43,64.29C70.71,64 71.03,63.75 71.4,63.55C71.89,63.29 72.37,63.17 72.83,63.17C73.3,63.18 73.71,63.31 74.08,63.58C74.46,63.85 74.76,64.24 74.98,64.77L76.27,68.32C76.38,68.62 76.51,68.92 76.65,69.23C76.79,69.53 76.97,69.8 77.2,70.02L77.27,70.15L75.29,71.26C75.04,71.02 74.84,70.75 74.67,70.44C74.51,70.13 74.36,69.82 74.24,69.5ZM73.45,66.37L73.91,67.67L73.36,67.96C73.19,68.06 73.06,68.18 72.96,68.32C72.86,68.46 72.79,68.61 72.76,68.78C72.72,68.95 72.72,69.12 72.74,69.3C72.76,69.47 72.81,69.64 72.87,69.82C72.92,69.93 72.98,70.05 73.04,70.15C73.1,70.26 73.18,70.33 73.27,70.37C73.36,70.41 73.48,70.39 73.63,70.32C73.77,70.24 73.89,70.13 73.99,69.99C74.09,69.85 74.15,69.7 74.18,69.54C74.21,69.38 74.2,69.22 74.13,69.06L74.84,69.77C74.86,70.01 74.88,70.25 74.87,70.48C74.87,70.71 74.84,70.93 74.78,71.15C74.72,71.36 74.62,71.56 74.49,71.74C74.36,71.91 74.17,72.07 73.93,72.2C73.52,72.42 73.12,72.51 72.73,72.47C72.33,72.43 71.97,72.29 71.64,72.04C71.32,71.8 71.06,71.48 70.86,71.08C70.64,70.63 70.53,70.19 70.53,69.77C70.54,69.35 70.64,68.95 70.83,68.59C71.01,68.21 71.27,67.88 71.59,67.57C71.91,67.27 72.28,67 72.68,66.78L73.45,66.37ZM79.12,59.46L80.98,58.44L83.81,66.49C84.08,67.17 84.18,67.81 84.12,68.43C84.06,69.05 83.85,69.61 83.48,70.11C83.12,70.61 82.61,71.03 81.95,71.37C81.69,71.51 81.42,71.62 81.12,71.69C80.82,71.77 80.52,71.82 80.21,71.83C79.9,71.84 79.6,71.82 79.31,71.75L79.39,69.91C79.67,69.99 79.95,70.02 80.25,69.99C80.54,69.96 80.82,69.88 81.07,69.74C81.37,69.59 81.59,69.41 81.72,69.19C81.86,68.96 81.92,68.72 81.92,68.45C81.92,68.19 81.87,67.91 81.77,67.61L79.56,61.34L79.12,59.46ZM76.73,66.05L76.48,65.44C76.29,64.99 76.14,64.52 76.03,64.03C75.92,63.53 75.87,63.04 75.9,62.56C75.93,62.07 76.06,61.63 76.3,61.23C76.53,60.82 76.9,60.49 77.42,60.23C77.86,60 78.28,59.94 78.66,60.06C79.04,60.17 79.38,60.38 79.68,60.7C79.99,61.01 80.26,61.37 80.5,61.77C80.74,62.16 80.94,62.53 81.1,62.87L81.35,63.51C81.47,63.87 81.59,64.29 81.69,64.77C81.79,65.24 81.84,65.72 81.84,66.2C81.84,66.68 81.75,67.12 81.57,67.53C81.4,67.93 81.09,68.24 80.66,68.47C80.19,68.72 79.75,68.81 79.34,68.74C78.94,68.68 78.57,68.51 78.24,68.23C77.9,67.95 77.61,67.62 77.36,67.24C77.11,66.85 76.89,66.45 76.73,66.05ZM78.49,64.3L78.75,64.91C78.82,65.08 78.9,65.26 79,65.48C79.1,65.69 79.22,65.89 79.35,66.07C79.49,66.25 79.64,66.37 79.82,66.44C80,66.52 80.2,66.5 80.43,66.38C80.74,66.23 80.94,66.03 81.03,65.77C81.12,65.51 81.14,65.23 81.09,64.94C81.04,64.64 80.96,64.36 80.86,64.1L80.4,62.94C80.33,62.78 80.24,62.6 80.15,62.41C80.06,62.21 79.95,62.04 79.82,61.89C79.69,61.74 79.53,61.64 79.36,61.59C79.19,61.54 78.99,61.57 78.77,61.68C78.51,61.8 78.33,61.98 78.23,62.19C78.14,62.4 78.1,62.62 78.11,62.87C78.13,63.11 78.17,63.36 78.24,63.61C78.32,63.86 78.4,64.09 78.49,64.3ZM87.47,62.17L86.15,58.53C86.09,58.4 86.03,58.27 85.96,58.14C85.9,58.01 85.81,57.91 85.71,57.85C85.6,57.79 85.47,57.81 85.3,57.9C85.13,57.99 85.02,58.1 84.98,58.23C84.94,58.36 84.93,58.49 84.96,58.64C84.99,58.79 85.04,58.94 85.09,59.09L83.07,60.23C82.87,59.83 82.77,59.44 82.77,59.05C82.77,58.66 82.85,58.28 83.01,57.93C83.16,57.57 83.38,57.25 83.66,56.96C83.94,56.67 84.26,56.42 84.63,56.23C85.12,55.97 85.6,55.84 86.06,55.85C86.53,55.85 86.95,55.99 87.32,56.26C87.69,56.52 87.99,56.92 88.21,57.44L89.5,60.99C89.61,61.29 89.74,61.59 89.88,61.9C90.02,62.21 90.2,62.47 90.43,62.69L90.5,62.83L88.52,63.93C88.27,63.69 88.07,63.42 87.91,63.11C87.74,62.8 87.6,62.49 87.47,62.17ZM86.68,59.04L87.14,60.34L86.59,60.63C86.42,60.73 86.29,60.85 86.19,61C86.09,61.13 86.03,61.29 85.99,61.46C85.95,61.62 85.95,61.79 85.97,61.97C85.99,62.14 86.04,62.31 86.11,62.49C86.15,62.61 86.21,62.72 86.27,62.83C86.34,62.93 86.41,63 86.5,63.05C86.6,63.09 86.71,63.07 86.86,62.99C87,62.91 87.13,62.8 87.22,62.67C87.32,62.52 87.39,62.37 87.42,62.21C87.45,62.05 87.43,61.89 87.36,61.73L88.07,62.45C88.1,62.68 88.11,62.92 88.11,63.15C88.1,63.38 88.07,63.61 88.01,63.82C87.95,64.03 87.85,64.23 87.72,64.41C87.59,64.59 87.41,64.74 87.16,64.87C86.75,65.09 86.35,65.18 85.96,65.14C85.56,65.1 85.2,64.96 84.87,64.72C84.55,64.47 84.29,64.15 84.09,63.76C83.87,63.3 83.76,62.86 83.76,62.44C83.77,62.02 83.87,61.63 84.06,61.26C84.24,60.89 84.5,60.55 84.82,60.25C85.14,59.94 85.51,59.68 85.92,59.45L86.68,59.04ZM90.74,53.03L93.56,61.13L91.52,62.26L88.71,54.15L90.74,53.03ZM87.91,52.14C87.74,51.82 87.69,51.5 87.79,51.17C87.89,50.85 88.09,50.59 88.4,50.41C88.71,50.24 89.02,50.19 89.33,50.27C89.66,50.35 89.9,50.55 90.08,50.86C90.25,51.18 90.29,51.5 90.2,51.82C90.11,52.15 89.9,52.4 89.58,52.58C89.28,52.76 88.98,52.81 88.66,52.73C88.34,52.65 88.09,52.45 87.91,52.14ZM94.73,53.03L96.9,59.28L94.87,60.41L92.06,52.3L93.96,51.25L94.73,53.03ZM95.16,55.1L94.75,55.35C94.57,54.95 94.41,54.5 94.28,54.02C94.15,53.53 94.09,53.05 94.09,52.57C94.09,52.08 94.2,51.64 94.4,51.24C94.61,50.82 94.96,50.49 95.45,50.24C95.84,50.04 96.2,49.96 96.53,50.02C96.85,50.08 97.14,50.23 97.4,50.46C97.65,50.69 97.88,50.96 98.07,51.28C98.27,51.6 98.43,51.93 98.57,52.25L100.38,57.35L98.35,58.48L96.52,53.36C96.45,53.17 96.36,53 96.26,52.83C96.16,52.65 96.03,52.52 95.88,52.44C95.74,52.36 95.55,52.38 95.33,52.5C95.09,52.62 94.93,52.79 94.85,53.01C94.77,53.22 94.75,53.46 94.77,53.71C94.8,53.96 94.86,54.2 94.93,54.45C95.02,54.7 95.09,54.91 95.16,55.1Z"
      android:fillColor="#926340"/>
</vector>
