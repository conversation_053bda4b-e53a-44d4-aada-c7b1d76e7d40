<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M8.265,14.662L13.304,16.064C13.364,16.081 13.419,16.113 13.463,16.158C13.508,16.202 13.54,16.257 13.557,16.317L14.959,21.356C14.981,21.432 15.027,21.499 15.09,21.547C15.154,21.595 15.231,21.621 15.311,21.621C15.39,21.621 15.467,21.595 15.531,21.547C15.594,21.499 15.64,21.432 15.662,21.356L17.064,16.317C17.081,16.257 17.113,16.202 17.158,16.158C17.202,16.113 17.257,16.081 17.317,16.064L22.356,14.662C22.432,14.64 22.499,14.594 22.547,14.531C22.595,14.467 22.621,14.39 22.621,14.311C22.621,14.231 22.595,14.154 22.547,14.09C22.499,14.027 22.432,13.981 22.356,13.959L17.317,12.557C17.257,12.54 17.202,12.508 17.158,12.463C17.113,12.419 17.081,12.364 17.064,12.304L15.662,7.265C15.64,7.189 15.594,7.122 15.531,7.074C15.467,7.026 15.39,7 15.311,7C15.231,7 15.154,7.026 15.09,7.074C15.027,7.122 14.981,7.189 14.959,7.265L13.557,12.304C13.54,12.364 13.508,12.419 13.463,12.463C13.419,12.508 13.364,12.54 13.304,12.557L8.265,13.959C8.189,13.981 8.122,14.027 8.074,14.09C8.026,14.154 8,14.231 8,14.311C8,14.39 8.026,14.467 8.074,14.531C8.122,14.594 8.189,14.64 8.265,14.662Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.125"
      android:strokeColor="#FCD34D"
      android:strokeLineCap="round">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="12.118"
          android:startY="8.784"
          android:endX="18.503"
          android:endY="19.837"
          android:type="linear">
        <item android:offset="0" android:color="#FFFCD966"/>
        <item android:offset="0.5" android:color="#FFFCD966"/>
        <item android:offset="1" android:color="#FFFCCD34"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M3.145,9.192L5.902,9.959C5.935,9.969 5.965,9.986 5.989,10.011C6.014,10.035 6.031,10.065 6.041,10.098L6.808,12.855C6.82,12.897 6.845,12.934 6.88,12.96C6.914,12.986 6.957,13 7,13C7.043,13 7.086,12.986 7.12,12.96C7.155,12.934 7.18,12.897 7.192,12.855L7.959,10.098C7.969,10.065 7.986,10.035 8.011,10.011C8.035,9.986 8.065,9.969 8.098,9.959L10.855,9.192C10.897,9.18 10.934,9.155 10.96,9.12C10.986,9.086 11,9.043 11,9C11,8.957 10.986,8.914 10.96,8.88C10.934,8.845 10.897,8.82 10.855,8.808L8.098,8.041C8.065,8.031 8.035,8.014 8.011,7.989C7.986,7.965 7.969,7.935 7.959,7.902L7.192,5.145C7.18,5.103 7.155,5.067 7.12,5.04C7.086,5.014 7.043,5 7,5C6.957,5 6.914,5.014 6.88,5.04C6.845,5.067 6.82,5.103 6.808,5.145L6.041,7.902C6.031,7.935 6.014,7.965 5.989,7.989C5.965,8.014 5.935,8.031 5.902,8.041L3.145,8.808C3.103,8.82 3.067,8.845 3.04,8.88C3.014,8.914 3,8.957 3,9C3,9.043 3.014,9.086 3.04,9.12C3.067,9.155 3.103,9.18 3.145,9.192Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.125"
      android:strokeColor="#FCD34D"
      android:strokeLineCap="round">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="5.253"
          android:startY="5.976"
          android:endX="8.747"
          android:endY="12.024"
          android:type="linear">
        <item android:offset="0" android:color="#FFFCD966"/>
        <item android:offset="0.5" android:color="#FFFCD966"/>
        <item android:offset="1" android:color="#FFFCCD34"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
