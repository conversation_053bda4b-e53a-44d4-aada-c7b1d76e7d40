<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="37dp"
    android:height="13dp"
    android:viewportWidth="37"
    android:viewportHeight="13">
  <path
      android:pathData="M2.902,2.22C3.255,0.91 4.443,0 5.799,0H33.585C35.56,0 36.995,1.874 36.482,3.78L34.598,10.78C34.245,12.089 33.057,13 31.701,13H3.915C1.94,13 0.504,11.126 1.018,9.22L2.902,2.22Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="31.827"
          android:startY="2.499"
          android:endX="-0.673"
          android:endY="15.499"
          android:type="linear">
        <item android:offset="0" android:color="#FF84A9FF"/>
        <item android:offset="0.354" android:color="#FF0035B2"/>
        <item android:offset="1" android:color="#FF6795FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M2.902,2.22C3.255,0.91 4.443,0 5.799,0H33.585C35.56,0 36.995,1.874 36.482,3.78L34.598,10.78C34.245,12.089 33.057,13 31.701,13H3.915C1.94,13 0.504,11.126 1.018,9.22L2.902,2.22Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="1.827"
          android:centerY="14.499"
          android:gradientRadius="11.5"
          android:type="radial">
        <item android:offset="0" android:color="#FF7AA2FF"/>
        <item android:offset="1" android:color="#00FFFDFF"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
