<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="203dp"
    android:height="203dp"
    android:viewportWidth="203"
    android:viewportHeight="203">
  <path
      android:pathData="M19,134.5a79.5,21.5 0,1 0,159 0a79.5,21.5 0,1 0,-159 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="19"
          android:startY="134.5"
          android:endX="180.5"
          android:endY="135"
          android:type="linear">
        <item android:offset="0" android:color="#FFEFE0FF"/>
        <item android:offset="1" android:color="#FFE4E0FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M26,134a73,17 0,1 0,146 0a73,17 0,1 0,-146 0z"/>
    <path
        android:pathData="M21,134.5a75.5,27.5 0,1 0,151 0a75.5,27.5 0,1 0,-151 0z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="21"
            android:startY="134.5"
            android:endX="172"
            android:endY="134.5"
            android:type="linear">
          <item android:offset="0" android:color="#FFD9D1FF"/>
          <item android:offset="1" android:color="#FFA094FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M27,142.5a71.5,13.5 0,1 0,143 0a71.5,13.5 0,1 0,-143 0z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="27"
            android:startY="142.5"
            android:endX="170"
            android:endY="142.5"
            android:type="linear">
          <item android:offset="0" android:color="#FFC3B8F7"/>
          <item android:offset="1" android:color="#FF7C70E2"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M82.5,37.5m-7.5,0a7.5,7.5 0,1 1,15 0a7.5,7.5 0,1 1,-15 0">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="90"
          android:startY="43"
          android:endX="70.5"
          android:endY="28.5"
          android:type="linear">
        <item android:offset="0" android:color="#FFC2CCFF"/>
        <item android:offset="1" android:color="#FFF7CEFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M107,86m-3,0a3,3 0,1 1,6 0a3,3 0,1 1,-6 0">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="107"
          android:startY="83"
          android:endX="110"
          android:endY="88"
          android:type="linear">
        <item android:offset="0" android:color="#FFE9D1FF"/>
        <item android:offset="1" android:color="#FFC9CBFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M124.11,96.75C107.14,96.34 93.07,108.61 92.69,124.17C92.4,135.9 99.98,146.15 111,150.66C111,150.66 122.5,150.5 127.5,150C132.5,149.5 142.97,147.72 142.97,147.72C143.24,146.77 144.25,145.79 145.41,144.71C150.67,139.8 153.98,133.11 154.16,125.68C154.54,110.12 141.09,97.17 124.11,96.75Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="98"
          android:startY="114.5"
          android:endX="153"
          android:endY="150.5"
          android:type="linear">
        <item android:offset="0" android:color="#FFF8D3FF"/>
        <item android:offset="0.81" android:color="#FFB3C8FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M124.11,96.75C107.14,96.34 93.07,108.61 92.69,124.17C92.4,135.9 99.98,146.15 111,150.66C111,150.66 122.5,150.5 127.5,150C132.5,149.5 142.97,147.72 142.97,147.72C143.24,146.77 144.25,145.79 145.41,144.71C150.67,139.8 153.98,133.11 154.16,125.68C154.54,110.12 141.09,97.17 124.11,96.75Z"
      android:fillAlpha="0.53">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="123"
          android:startY="127"
          android:endX="121.5"
          android:endY="155.5"
          android:type="linear">
        <item android:offset="0" android:color="#00E7C7FF"/>
        <item android:offset="1" android:color="#FF280FC6"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M106,125m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M123,125m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M140,125m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M138.15,50.68C139.85,53.61 140.15,56.98 139.27,60.13C139.08,60.83 138.92,61.45 139.04,61.88C139.07,62 139.13,62.1 139.19,62.2C139.73,62.98 140.08,64.36 139.26,64.84C136.83,66.24 133.39,68.23 132.46,68.77C125.78,72.63 117.49,70.8 113.95,64.67C110.41,58.54 112.95,50.44 119.64,46.58C126.32,42.72 134.61,44.55 138.15,50.68Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="119.64"
          android:startY="46.58"
          android:endX="145.2"
          android:endY="61.47"
          android:type="linear">
        <item android:offset="0" android:color="#FFF6C5FF"/>
        <item android:offset="1" android:color="#FFA1C6FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M119.31,61.83m-1.55,0.95a1.82,1.82 103.57,1 1,3.1 -1.9a1.82,1.82 103.57,1 1,-3.1 1.9"
      android:fillColor="#ffffff"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M125.9,57.8m-1.55,0.95a1.82,1.82 103.57,1 1,3.1 -1.9a1.82,1.82 103.57,1 1,-3.1 1.9"
      android:fillColor="#ffffff"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M132.5,53.77m-1.55,0.95a1.82,1.82 103.57,1 1,3.1 -1.9a1.82,1.82 103.57,1 1,-3.1 1.9"
      android:fillColor="#ffffff"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M29.2,79.12C27.63,85.48 28.89,91.94 32.21,97.33C32.95,98.53 33.59,99.6 33.59,100.46C33.59,101.52 32.73,102.49 31.78,103.23C31.04,103.82 31.2,105.21 32.12,105.43C37.1,106.67 47.16,109.16 49.55,109.75C64.08,113.34 78.53,105.46 81.82,92.14C85.12,78.82 76.01,65.11 61.48,61.51C46.95,57.92 32.5,65.8 29.2,79.12Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="61.48"
          android:startY="61.51"
          android:endX="79.09"
          android:endY="104.94"
          android:type="linear">
        <item android:offset="0" android:color="#FFF6C5FF"/>
        <item android:offset="1" android:color="#FFA1C6FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M40.68,81.64m-3.4,-0.93a3.53,3.53 60.3,1 1,6.8 1.86a3.53,3.53 60.3,1 1,-6.8 -1.86"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M55.14,85.6m-3.4,-0.93a3.53,3.53 60.3,1 1,6.8 1.86a3.53,3.53 60.3,1 1,-6.8 -1.86"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M69.59,89.55m-3.4,-0.93a3.53,3.53 60.3,1 1,6.8 1.86a3.53,3.53 60.3,1 1,-6.8 -1.86"
      android:fillColor="#ffffff"/>
</vector>
