<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="32"
    android:viewportHeight="32">
  <path
      android:strokeWidth="1"
      android:pathData="M16,16m-15.5,0a15.5,15.5 0,1 1,31 0a15.5,15.5 0,1 1,-31 0"
      android:strokeColor="#ffffff">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16"
          android:startY="0"
          android:endX="3.5"
          android:endY="25.5"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFBB01"/>
        <item android:offset="1" android:color="#FFFFE690"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
