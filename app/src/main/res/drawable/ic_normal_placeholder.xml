<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="203dp"
    android:height="203dp"
    android:viewportWidth="203"
    android:viewportHeight="203">
  <path
      android:pathData="M19,134.5a79.5,21.5 0,1 0,159 0a79.5,21.5 0,1 0,-159 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="19"
          android:startY="134.5"
          android:endX="180.5"
          android:endY="135"
          android:type="linear">
        <item android:offset="0" android:color="#FFEFE0FF"/>
        <item android:offset="1" android:color="#FFE4E0FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M26,134a73,17 0,1 0,146 0a73,17 0,1 0,-146 0z"/>
    <path
        android:pathData="M21,134.5a75.5,27.5 0,1 0,151 0a75.5,27.5 0,1 0,-151 0z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="21"
            android:startY="134.5"
            android:endX="172"
            android:endY="134.5"
            android:type="linear">
          <item android:offset="0" android:color="#FFD9D1FF"/>
          <item android:offset="1" android:color="#FFA094FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M27,142.5a71.5,13.5 0,1 0,143 0a71.5,13.5 0,1 0,-143 0z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="27"
            android:startY="142.5"
            android:endX="170"
            android:endY="142.5"
            android:type="linear">
          <item android:offset="0" android:color="#FFC3B8F7"/>
          <item android:offset="1" android:color="#FF7C70E2"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M122.5,69C123.88,69 125,70.12 125,71.5V72.5C125,76.9 126.33,78 127,78L127,78.01C143.41,78.28 159.5,91.88 159.5,114.5C159.5,129.88 150.23,139.36 140.16,144.51C130.44,149.49 119.16,151 111.5,151V133C117.34,133 125.56,131.76 131.96,128.49C138.02,125.39 141.5,121.12 141.5,114.5C141.5,101.36 132.91,96 126.5,96L112.69,95.11C111.74,95.05 111,94.26 111,93.31V93C111,92.17 111.67,91.5 112.5,91.5L111.3,91.3C110.26,91.13 109.5,90.23 109.5,89.18C109.5,88.19 110.17,87.33 111.13,87.09L111.5,87L109.78,86.75C108.76,86.61 108,85.73 108,84.7C108,83.71 108.7,82.86 109.67,82.67L110.5,82.5L108.65,81.97C107.68,81.69 107,80.8 107,79.78C107,78.52 108.02,77.5 109.28,77.5H120.5C120.5,77.5 120,75.5 120,74V71.5C120,70.12 121.12,69 122.5,69Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="133.25"
          android:startY="69"
          android:endX="133.25"
          android:endY="151"
          android:type="linear">
        <item android:offset="0" android:color="#FFF4D6FE"/>
        <item android:offset="0.44" android:color="#FFCEC4FA"/>
        <item android:offset="1" android:color="#FFC2CAFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M122.5,69C123.88,69 125,70.12 125,71.5V72.5C125,76.9 126.33,78 127,78L127,78.01C143.41,78.28 159.5,91.88 159.5,114.5C159.5,129.88 150.23,139.36 140.16,144.51C130.44,149.49 119.16,151 111.5,151V133C117.34,133 125.56,131.76 131.96,128.49C138.02,125.39 141.5,121.12 141.5,114.5C141.5,101.36 132.91,96 126.5,96L112.69,95.11C111.74,95.05 111,94.26 111,93.31V93C111,92.17 111.67,91.5 112.5,91.5L111.3,91.3C110.26,91.13 109.5,90.23 109.5,89.18C109.5,88.19 110.17,87.33 111.13,87.09L111.5,87L109.78,86.75C108.76,86.61 108,85.73 108,84.7C108,83.71 108.7,82.86 109.67,82.67L110.5,82.5L108.65,81.97C107.68,81.69 107,80.8 107,79.78C107,78.52 108.02,77.5 109.28,77.5H120.5C120.5,77.5 120,75.5 120,74V71.5C120,70.12 121.12,69 122.5,69Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="150"
          android:startY="134"
          android:endX="133.25"
          android:endY="151"
          android:type="linear">
        <item android:offset="0" android:color="#00B3ACF4"/>
        <item android:offset="1" android:color="#FFB3ACF4"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M129.77,59.8L125.74,66.17C125.49,66.57 125.55,67.09 125.89,67.42L126.39,67.91C126.74,68.25 127.28,68.29 127.68,68L133.91,63.5C134.43,63.12 134.46,62.36 133.98,61.94L131.27,59.58C130.81,59.18 130.1,59.28 129.77,59.8Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="127.75"
          android:startY="60.17"
          android:endX="130.97"
          android:endY="66.46"
          android:type="linear">
        <item android:offset="0" android:color="#FFDBCAFB"/>
        <item android:offset="1" android:color="#FFB6B0F6"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M121.58,51.63L122.47,64.3C122.51,64.76 122.21,65.19 121.76,65.32L120.35,65.75C119.87,65.89 119.35,65.65 119.15,65.18L114.11,53.4C113.86,52.83 114.19,52.18 114.8,52.03L120.36,50.73C120.96,50.58 121.54,51.01 121.58,51.63Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="123.8"
          android:startY="53.73"
          android:endX="116.02"
          android:endY="59.19"
          android:type="linear">
        <item android:offset="0" android:color="#FFDBCAFB"/>
        <item android:offset="1" android:color="#FFB6B0F6"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M54,123.5C54,105.55 68.55,91 86.5,91C104.45,91 119,105.55 119,123.5V150.5C119,150.5 101,151.52 86.5,151C72,150.48 54,147.5 54,147.5V123.5Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="54"
          android:startY="121"
          android:endX="119"
          android:endY="135.5"
          android:type="linear">
        <item android:offset="0" android:color="#FFF6D6FE"/>
        <item android:offset="1" android:color="#FFBCC9FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M54,123.5C54,105.55 68.55,91 86.5,91C104.45,91 119,105.55 119,123.5V150.5C119,150.5 101,151.52 86.5,151C72,150.48 54,147.5 54,147.5V123.5Z"
      android:fillAlpha="0.2">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="96"
          android:startY="127"
          android:endX="119"
          android:endY="127"
          android:type="linear">
        <item android:offset="0" android:color="#00F0D5FE"/>
        <item android:offset="1" android:color="#FF7BF4FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M54,123.5C54,105.55 68.55,91 86.5,91C104.45,91 119,105.55 119,123.5V150.5C119,150.5 101,151.52 86.5,151C72,150.48 54,147.5 54,147.5V123.5Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="87"
          android:startY="121"
          android:endX="92"
          android:endY="186.5"
          android:type="linear">
        <item android:offset="0" android:color="#008579E4"/>
        <item android:offset="1" android:color="#FF8579E4"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M104.94,116.01L102.36,118.94L104.65,122.25"
      android:strokeLineJoin="round"
      android:strokeWidth="4"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M81,117V122"
      android:strokeWidth="4"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
</vector>
