package com.flutterup.app.startup

import android.content.Context
import androidx.startup.Initializer
import com.flutterup.app.network.environment.AppEnvManager
import com.flutterup.base.di.MMKVInitializer
import com.flutterup.chat.ChatSDKInitializer

class ChatInitializer : Initializer<ChatSDKInitializer> {
    override fun create(context: Context): ChatSDKInitializer {
        val env = AppEnvManager.getEnvironment()
        ChatSDKInitializer.init(context, env.chatKey)
        return ChatSDKInitializer
    }

    override fun dependencies(): List<Class<out Initializer<*>?>?> = listOf(MMKVInitializer::class.java)
}