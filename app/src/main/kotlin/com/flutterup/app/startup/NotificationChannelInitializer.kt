package com.flutterup.app.startup

import android.content.Context
import androidx.startup.Initializer
import com.flutterup.app.utils.NotificationUtils

class NotificationChannelInitializer : Initializer<NotificationUtils> {
    override fun create(context: Context): NotificationUtils {
        NotificationUtils.createNotificationChannel()
        return NotificationUtils
    }

    override fun dependencies(): List<Class<out Initializer<*>?>> = emptyList()
}