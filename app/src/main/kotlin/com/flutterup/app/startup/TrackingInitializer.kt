package com.flutterup.app.startup

import android.content.Context
import android.os.Build
import androidx.startup.Initializer
import com.flutterup.app.BuildConfig
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.di.MMKVInitializer
import com.flutterup.base.utils.DeviceUtils
import com.flutterup.base.utils.applicationEntryPoint
import com.flutterup.tracking.AdjustInitializer
import com.flutterup.tracking.TrackingManager
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

class TrackingInitializer : Initializer<TrackingManager> {


    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface TrackingEntryPoint {
        fun userRepository(): UserMonitor
    }

    private val userMonitor: UserMonitor by lazy {
        applicationEntryPoint<TrackingEntryPoint>().userRepository()
    }

    override fun create(context: Context): TrackingManager {
        initProperties()
        return TrackingManager
    }

    private fun initProperties() {
        TrackingManager.setGlobalPresetProperties(mapOf(
            "os-version" to Build.VERSION.RELEASE.orEmpty(),
            "app_version" to BuildConfig.VERSION_NAME,
            "os-version" to Build.VERSION.RELEASE.orEmpty(),
            "app_version" to BuildConfig.VERSION_NAME,
            "app_id" to BuildConfig.ALIAS,
        ))

        TrackingManager.setGlobalDynamicPresetPropertiesProvider {
            mapOf(
                "device_id" to DeviceUtils.getDeviceId(),
            )
        }
    }

    override fun dependencies(): List<Class<out Initializer<*>?>?> = listOf(AdjustInitializer::class.java, MMKVInitializer::class.java)
}