package com.flutterup.app.design.component.cardstack

import androidx.annotation.IntRange
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlin.math.pow

/**
 * A composable that create cards stack
 * @param modifier Modifier for the this composable adding padding if required in modifier
 * @param cardStackState use this state to programmatically swipe and rewind
 * @param stackDirection card stack direction
 * @param visibleCount no of cards to be visible below the top card (@IntRange(from = 0, to = maxSize )
 * @param cardElevation determine how much portion of underline cards will be displayed
 * @param scaleRatio determine how much underline cards be scale down value fo 0.95f means 90% of original size
 * @param alphaDegree determine how much underline cards be transparent
 * @param displacementThreshold thresh hold value to make the card swipe
 * @param animationDuration duration of the card animation
 * @param rotationMaxDegree maximum degree at which card can rotate
 * @param swipeDirection direction where card can move
 * @param swipeMethod set swipe methods either manual(through code) or automatic(through swipe)
 * @param onSwiped callback to card state change (@Position)
 * <AUTHOR>
 */
@Composable
fun <T> CardStack(
    modifier: Modifier = Modifier,
    enable: Boolean = true,
    cardStackState: CardStackState = rememberCardStackState(),
    stackDirection: Direction = Direction.Bottom,
    @IntRange(from = 1) visibleCount: Int = 3,
    cardElevation: Dp = 10.dp,
    scaleRatio: Float = 0.95f,
    alphaDegree: Float = 0f,
    displacementThreshold: Dp = 60.dp,
    animationDuration: Duration = Duration.Normal,
    @IntRange(from = 0, to = 360) rotationMaxDegree: Int = 20,
    swipeDirection: SwipeDirection = SwipeDirection.Freedom,
    swipeMethod: SwipeMethod = SwipeMethod.All,
    onDrag: (Direction, Float) -> Unit = { _, _ -> },
    onSwiped: (Direction, Int, Boolean) -> Unit = { _, _, _ -> },
    items: List<T>,
    content: @Composable (index: Int, item: T) -> Unit
) {
    require(cardElevation >= 0.dp) {
        "cardElevation must not be negative"
    }
    require(scaleRatio in 0.0f..1.0f) {
        "scaleRatio must be between 0.0 and 1.0 (inclusive)"
    }
    require(rotationMaxDegree in 0..360) {
        "rotationMaxDegree must be between 0 and 360 (inclusive)"
    }
    val density = LocalDensity.current

    val onCardSwiped: (Direction, Int, Boolean) -> Unit = { direction, index, isFromAutomatic ->
        onSwiped.invoke(direction, index, isFromAutomatic)
    }

    //Initialize stack state
    cardStackState.init(
        direction = stackDirection,
        visibleCount = visibleCount,
        stackElevationPx = with(density) { cardElevation.toPx() },
        scaleInterval = scaleRatio,
        alphaInterval = alphaDegree,
        displacementThresholdPx = with(density) { displacementThreshold.toPx() },
        animationDuration = animationDuration,
        rotationMaxDegree = rotationMaxDegree,
        swipeDirection = swipeDirection,
        swipeMethod = swipeMethod,
        onDrag = onDrag,
        onSwiped = onCardSwiped,
    )

    val start = cardStackState.currentCardPage
    val size = cardStackState.size

    //calculate stack padding based on number of card visible
    val scalePadding =
        (1..visibleCount).sumOf { cardElevation.times((1 - scaleRatio).pow(it)).value.toDouble() }.dp
    val stackPadding = cardElevation.times(visibleCount - 1)

    val paddingValues: PaddingValues = when(stackDirection) {
        Direction.None -> PaddingValues()
        Direction.Top -> PaddingValues(top = stackPadding.minus(scalePadding))
        Direction.Bottom -> PaddingValues(bottom = stackPadding.minus(scalePadding))
        Direction.Left -> PaddingValues(start = stackPadding.minus(scalePadding))
        Direction.Right -> PaddingValues(end = stackPadding.minus(scalePadding))
        Direction.TopAndLeft -> PaddingValues(start = stackPadding.minus(scalePadding), top = stackPadding.minus(scalePadding))
        Direction.TopAndRight -> PaddingValues(end = stackPadding.minus(scalePadding), top = stackPadding.minus(scalePadding))
        Direction.BottomAndLeft -> PaddingValues(start = stackPadding.minus(scalePadding), bottom = stackPadding.minus(scalePadding))
        Direction.BottomAndRight -> PaddingValues(end = stackPadding.minus(scalePadding), bottom = stackPadding.minus(scalePadding))
    }

    // 当items发生变化时重新初始化cardQueue
    LaunchedEffect(items) {
        cardStackState.initCardQueue(items)
    }

    Box(
        modifier = modifier
            .padding(paddingValues)
            .then(
                if (enable) {
                    Modifier.pointerInput(key1 = Unit) {
                        detectDragGestures(
                            onDrag = cardStackState::onDrag,
                            onDragEnd = cardStackState::onDragEnd
                        )
                    }
                } else Modifier
            )
    ) {
        for (i in cardStackState.cardQueue.indices) {
            val state = cardStackState.cardQueue[i]
            val item = items.getOrNull(i) ?: continue

            CardContainer(cardState = state) {
                content(i, item)
            }
        }
    }
}