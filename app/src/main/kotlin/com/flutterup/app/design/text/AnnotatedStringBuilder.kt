package com.flutterup.app.design.text

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.LinkInteractionListener
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle


/**
 * AnnotatedString 构建器
 * 提供便捷的方法来构建富文本
 */
class AppAnnotatedStringBuilder {

    private val builder = AnnotatedString.Builder()

    /**
     * 添加普通文本
     */
    fun append(text: String, spanStyle: SpanStyle? = null): AppAnnotatedStringBuilder {
        return append(StyledAnnotatedString(text, spanStyle))
    }


    /**
     * 添加换行
     */
    fun appendLine(): AppAnnotatedStringBuilder {
        return append("\n")
    }

    /**
     * 添加主要颜色文本
     */
    @Composable
    fun appendPrimary(text: String): AppAnnotatedStringBuilder {
        return append(
            text = text,
            spanStyle = SpanStyle(
                color = MaterialTheme.colorScheme.primary
            )
        )
    }

    /**
     * 添加次要颜色文本
     */
    @Composable
    fun appendSecondary(text: String): AppAnnotatedStringBuilder {
        return append(
            text = text,
            spanStyle = SpanStyle(
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        )
    }

    /**
     * 添加错误颜色文本
     */
    @Composable
    fun appendError(text: String): AppAnnotatedStringBuilder {
        return append(
            text = text,
            spanStyle = SpanStyle(
                color = MaterialTheme.colorScheme.error
            )
        )
    }

    /**
     * 添加链接文本
     */
    fun appendUrl(
        text: String,
        url: String,
        spanStyle: SpanStyle? = null,
        focusedStyle: SpanStyle? = null,
        hoveredStyle: SpanStyle? = null,
        pressedStyle: SpanStyle? = null,
        clickable: LinkInteractionListener? = null
    ): AppAnnotatedStringBuilder {
        return append(LinkUrlString(
            text = text,
            url = url,
            clickable = clickable,
            spanStyle = spanStyle,
            focusedStyle = focusedStyle,
            hoveredStyle = hoveredStyle,
            pressedStyle = pressedStyle,
        ))
    }

    /**
     * 添加链接文本
     */
    fun appendUrlUnderline(
        text: String,
        url: String,
        spanStyle: SpanStyle? = null,
        focusedStyle: SpanStyle? = null,
        hoveredStyle: SpanStyle? = null,
        pressedStyle: SpanStyle? = null,
        clickable: LinkInteractionListener? = null
    ): AppAnnotatedStringBuilder {
        val underlineStyle = spanStyle?.copy(
            textDecoration = TextDecoration.Underline
        ) ?: SpanStyle(
            textDecoration = TextDecoration.Underline
        )
        return appendUrl(
            text = text,
            url = url,
            spanStyle = underlineStyle,
            focusedStyle = focusedStyle,
            hoveredStyle = hoveredStyle,
            pressedStyle = pressedStyle,
            clickable = clickable
        )
    }

    fun appendLink(
        text: String,
        tag: String,
        spanStyle: SpanStyle? = null,
        focusedStyle: SpanStyle? = null,
        hoveredStyle: SpanStyle? = null,
        pressedStyle: SpanStyle? = null,
        clickable: LinkInteractionListener,
    ): AppAnnotatedStringBuilder {
        return append(LinkClickableString(
            text = text,
            tag = tag,
            clickable = clickable,
            spanStyle = spanStyle,
            focusedStyle = focusedStyle,
            hoveredStyle = hoveredStyle,
            pressedStyle = pressedStyle,
        ))
    }

    fun appendLinkUnderline(
        text: String,
        tag: String,
        spanStyle: SpanStyle? = null,
        focusedStyle: SpanStyle? = null,
        hoveredStyle: SpanStyle? = null,
        pressedStyle: SpanStyle? = null,
        clickable: LinkInteractionListener,
    ): AppAnnotatedStringBuilder {
        val underlineStyle = spanStyle?.copy(
            textDecoration = TextDecoration.Underline
        ) ?: SpanStyle(
            textDecoration = TextDecoration.Underline
        )
        return appendLink(
            text = text,
            tag = tag,
            spanStyle = underlineStyle,
            focusedStyle = focusedStyle,
            hoveredStyle = hoveredStyle,
            pressedStyle = pressedStyle,
            clickable = clickable
        )
    }

    /**
     * 添加粗体文本
     */
    fun appendBold(text: String): AppAnnotatedStringBuilder {
        return append(
            text = text,
            spanStyle = SpanStyle(
                fontWeight = FontWeight.Bold
            )
        )
    }

    /**
     * 添加斜体文本
     */
    fun appendItalic(text: String): AppAnnotatedStringBuilder {
        return append(
            text = text,
            spanStyle = SpanStyle(
                fontStyle = FontStyle.Italic
            )
        )
    }

    /**
     * 添加下划线文本
     */
    fun appendUnderline(text: String): AppAnnotatedStringBuilder {
        return append(
            text = text,
            spanStyle = SpanStyle(
                textDecoration = TextDecoration.Underline
            )
        )
    }

    /**
     * 添加删除线文本
     */
    fun appendStrikethrough(text: String, tag: String? = null): AppAnnotatedStringBuilder {
        return append(
            text = text,
            spanStyle = SpanStyle(
                textDecoration = TextDecoration.LineThrough
            )
        )
    }

    private fun append(string: IAnnotatedString): AppAnnotatedStringBuilder {
        when(string) {
            is LinkClickableString -> {
                builder.append(string.text)

                builder.addLink(
                    start = builder.length - string.text.length,
                    end = builder.length,
                    clickable = string.linkAnnotation
                )
            }
            is LinkUrlString -> {
                builder.append(string.text)

                builder.addLink(
                    start = builder.length - string.text.length,
                    end = builder.length,
                    url = string.linkAnnotation
                )
            }

            else -> {
                val spanStyle = string.spanStyle
                if (spanStyle != null) {
                    builder.withStyle(spanStyle) {
                        append(string.text)
                    }
                } else {
                    builder.append(string.text)
                }
            }
        }

        return this
    }

    /**
     * 构建 AnnotatedString
     */
    fun build(): AnnotatedString {
        return builder.toAnnotatedString()
    }
}

/**
 * 便捷的 DSL 构建器
 */
fun buildAppAnnotatedString(
    builder: AppAnnotatedStringBuilder.() -> Unit
): AnnotatedString {
    return AppAnnotatedStringBuilder().apply(builder).build()
}

/**
 * Composable 版本的构建器，可以访问主题颜色
 */
@Composable
fun buildAppThemeAnnotatedString(
    builder: @Composable AppAnnotatedStringBuilder.() -> Unit
): AnnotatedString {
    return AppAnnotatedStringBuilder().apply { builder() }.build()
}


private sealed interface IAnnotatedString {
    val text: String

    val spanStyle: SpanStyle?
}

private sealed interface AnnotatedLinkState <T : LinkAnnotation> {
    val clickable: LinkInteractionListener?

    val linkAnnotation: T

    val focusedStyle: SpanStyle?

    val hoveredStyle: SpanStyle?

    val pressedStyle: SpanStyle?
}

private sealed interface AnnotatedLinkUrlState : AnnotatedLinkState<LinkAnnotation.Url> {
    val url: String
}

private sealed interface AnnotatedLinkClickableState : AnnotatedLinkState<LinkAnnotation.Clickable> {
    val tag: String
}

private open class StyledAnnotatedString(
    override val text: String,

    override val spanStyle: SpanStyle? = null
) : IAnnotatedString

private data class LinkUrlString(
    override val text: String,
    override val url: String,
    override val clickable: LinkInteractionListener? = null,
    override val spanStyle: SpanStyle? = null,
    override val focusedStyle: SpanStyle? = null,
    override val hoveredStyle: SpanStyle? = null,
    override val pressedStyle: SpanStyle? = null
) : StyledAnnotatedString(text, spanStyle), AnnotatedLinkUrlState {
    override val linkAnnotation: LinkAnnotation.Url = LinkAnnotation.Url(
        url = url,
        styles = TextLinkStyles(
            style = spanStyle,
            focusedStyle = focusedStyle,
            hoveredStyle = hoveredStyle,
            pressedStyle = pressedStyle,
        ),
        linkInteractionListener = clickable
    )
}

private data class LinkClickableString(
    override val text: String,
    override val tag: String,
    override val clickable: LinkInteractionListener,
    override val spanStyle: SpanStyle? = null,
    override val focusedStyle: SpanStyle? = null,
    override val hoveredStyle: SpanStyle? = null,
    override val pressedStyle: SpanStyle? = null
) : StyledAnnotatedString(text, spanStyle), AnnotatedLinkClickableState {
    override val linkAnnotation: LinkAnnotation.Clickable = LinkAnnotation.Clickable(
        tag = tag,
        styles = TextLinkStyles(
            style = spanStyle,
            focusedStyle = focusedStyle,
            hoveredStyle = hoveredStyle,
            pressedStyle = pressedStyle,
        ),
        linkInteractionListener = clickable
    )
}