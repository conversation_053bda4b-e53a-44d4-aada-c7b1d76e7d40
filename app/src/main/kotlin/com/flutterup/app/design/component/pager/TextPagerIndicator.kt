package com.flutterup.app.design.component.pager

import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle


@Composable
fun TextPagerIndicator(
    pageCount: Int,
    selectedPage: Int,
    modifier: Modifier = Modifier,
    format: String = "%d/%d",
    textStyle: TextStyle = MaterialTheme.typography.bodyMedium,
    selectedTextStyle: TextStyle = MaterialTheme.typography.bodyMedium,
) {
    Text(
        text = format.format(selectedPage + 1, pageCount),
        style = if (selectedPage == 0) selectedTextStyle else textStyle,
        modifier = modifier
    )
}