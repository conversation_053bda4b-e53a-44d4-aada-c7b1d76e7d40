package com.flutterup.app.design.icons

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.TileMode
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val PacksPingChat: ImageVector
    get() {
        if (_packsPingChat != null) return _packsPingChat!!
        
        _packsPingChat = ImageVector.Builder(
            name = "packsPingChat",
            defaultWidth = 21.dp,
            defaultHeight = 21.dp,
            viewportWidth = 21f,
            viewportHeight = 21f
        ).apply {
            path {
            }
            group {
                path(
                    fill = Brush.linearGradient(
                        colors = listOf(Color(0xFFC37066), Color(0xFFF2C9B3)),
                        start = Offset(16.589f, -3.7336e-7f),
                        end = Offset(-7.77509e-7f, 19.7389f),
                        tileMode = TileMode.Clamp
                    )
                ) {
                    moveTo(20.9988f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 10.4994f, 20.9988f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 0f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 20.9988f, 10.4994f)
                    close()
                }
            }
            group {
                path {
                    moveTo(20.9988f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 10.4994f, 20.9988f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 0f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 20.9988f, 10.4994f)
                    close()
                }
            }
            group {
                path(
                    fill = Brush.linearGradient(
                        colors = listOf(Color(0xFFffffff), Color(0xFFFFD4CD)),
                        start = Offset(10.4994f, 2.81616e-7f),
                        end = Offset(1.04994f, 17.219f),
                        tileMode = TileMode.Clamp
                    )
                ) {
                    moveTo(20.9988f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 10.4994f, 20.9988f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 0f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 20.9988f, 10.4994f)
                    close()
                }
            }
            group {
                path(
                    fill = SolidColor(Color(0xFF0000FF))
                ) {
                    moveTo(20.9988f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 10.4994f, 20.9988f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 0f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 20.9988f, 10.4994f)
                    close()
                }
            }
            path(
                fill = SolidColor(Color(0xFFFFFFFF))
            ) {
            }
            path(
                fill = Brush.linearGradient(
                    colors = listOf(Color(0xFFffffff), Color(0xFFffffff)),
                    start = Offset(10.5005f, 4f),
                    end = Offset(10.5005f, 17.001f),
                    tileMode = TileMode.Clamp
                )
            ) {
                moveTo(10.501f, 4f)
                curveTo(14.0908f, 4.00016f, 17.0008f, 6.91022f, 17.001f, 10.5f)
                curveTo(17.001f, 14.0899f, 14.0909f, 17.0008f, 10.501f, 17.001f)
                curveTo(6.91096f, 17.001f, 4f, 14.09f, 4f, 10.5f)
                curveTo(4.00016f, 6.91012f, 6.91106f, 4f, 10.501f, 4f)
                close()
                moveTo(11.499f, 7.37695f)
                curveTo(11.5324f, 7.04225f, 11.1218f, 6.86567f, 10.9111f, 7.12402f)
                lineTo(7.57812f, 11.2139f)
                curveTo(7.53759f, 11.2636f, 7.51103f, 11.3244f, 7.50293f, 11.3887f)
                curveTo(7.4949f, 11.4527f, 7.50548f, 11.5177f, 7.53223f, 11.5762f)
                curveTo(7.55909f, 11.6348f, 7.60183f, 11.685f, 7.65527f, 11.7197f)
                curveTo(7.70865f, 11.7544f, 7.77076f, 11.7724f, 7.83398f, 11.7725f)
                horizontalLineTo(9.7373f)
                lineTo(9.50195f, 14.124f)
                curveTo(9.46868f, 14.4586f, 9.87915f, 14.6352f, 10.0898f, 14.377f)
                lineTo(13.4238f, 10.2871f)
                curveTo(13.4644f, 10.2374f, 13.4899f, 10.1765f, 13.498f, 10.1123f)
                curveTo(13.5061f, 10.0481f, 13.4966f, 9.98248f, 13.4697f, 9.92383f)
                curveTo(13.4429f, 9.86532f, 13.4f, 9.81599f, 13.3467f, 9.78125f)
                curveTo(13.2934f, 9.74657f, 13.2312f, 9.72765f, 13.168f, 9.72754f)
                horizontalLineTo(11.2637f)
                lineTo(11.499f, 7.37695f)
                close()
            }
            path(
                fill = SolidColor(Color(0xFFFFFFFF))
            ) {
                moveTo(10.501f, 4f)
                lineTo(10.501f, 3.8f)
                horizontalLineTo(10.501f)
                verticalLineTo(4f)
                close()
                moveTo(17.001f, 10.5f)
                horizontalLineTo(17.201f)
                verticalLineTo(10.5f)
                lineTo(17.001f, 10.5f)
                close()
                moveTo(10.501f, 17.001f)
                verticalLineTo(17.201f)
                horizontalLineTo(10.501f)
                lineTo(10.501f, 17.001f)
                close()
                moveTo(4f, 10.5f)
                lineTo(3.8f, 10.5f)
                verticalLineTo(10.5f)
                horizontalLineTo(4f)
                close()
                moveTo(11.499f, 7.37695f)
                lineTo(11.698f, 7.39688f)
                lineTo(11.698f, 7.39677f)
                lineTo(11.499f, 7.37695f)
                close()
                moveTo(10.9111f, 7.12402f)
                lineTo(10.7561f, 6.99763f)
                lineTo(10.7561f, 6.99768f)
                lineTo(10.9111f, 7.12402f)
                close()
                moveTo(7.57812f, 11.2139f)
                lineTo(7.42309f, 11.0875f)
                lineTo(7.42307f, 11.0875f)
                lineTo(7.57812f, 11.2139f)
                close()
                moveTo(7.50293f, 11.3887f)
                lineTo(7.3045f, 11.3636f)
                lineTo(7.30448f, 11.3638f)
                lineTo(7.50293f, 11.3887f)
                close()
                moveTo(7.53223f, 11.5762f)
                lineTo(7.35033f, 11.6593f)
                lineTo(7.35039f, 11.6595f)
                lineTo(7.53223f, 11.5762f)
                close()
                moveTo(7.65527f, 11.7197f)
                lineTo(7.54622f, 11.8874f)
                lineTo(7.5464f, 11.8875f)
                lineTo(7.65527f, 11.7197f)
                close()
                moveTo(7.83398f, 11.7725f)
                lineTo(7.83391f, 11.9725f)
                horizontalLineTo(7.83398f)
                verticalLineTo(11.7725f)
                close()
                moveTo(9.7373f, 11.7725f)
                lineTo(9.93631f, 11.7924f)
                lineTo(9.95832f, 11.5725f)
                horizontalLineTo(9.7373f)
                verticalLineTo(11.7725f)
                close()
                moveTo(9.50195f, 14.124f)
                lineTo(9.30295f, 14.1041f)
                lineTo(9.30293f, 14.1042f)
                lineTo(9.50195f, 14.124f)
                close()
                moveTo(10.0898f, 14.377f)
                lineTo(10.2448f, 14.5034f)
                lineTo(10.2449f, 14.5033f)
                lineTo(10.0898f, 14.377f)
                close()
                moveTo(13.4238f, 10.2871f)
                lineTo(13.5788f, 10.4135f)
                lineTo(13.5789f, 10.4134f)
                lineTo(13.4238f, 10.2871f)
                close()
                moveTo(13.498f, 10.1123f)
                lineTo(13.6965f, 10.1374f)
                lineTo(13.6965f, 10.1373f)
                lineTo(13.498f, 10.1123f)
                close()
                moveTo(13.4697f, 9.92383f)
                lineTo(13.6516f, 9.84054f)
                lineTo(13.6515f, 9.84049f)
                lineTo(13.4697f, 9.92383f)
                close()
                moveTo(13.3467f, 9.78125f)
                lineTo(13.4559f, 9.61371f)
                lineTo(13.4557f, 9.6136f)
                lineTo(13.3467f, 9.78125f)
                close()
                moveTo(13.168f, 9.72754f)
                lineTo(13.1683f, 9.52754f)
                horizontalLineTo(13.168f)
                verticalLineTo(9.72754f)
                close()
                moveTo(11.2637f, 9.72754f)
                lineTo(11.0647f, 9.70761f)
                lineTo(11.0426f, 9.92754f)
                horizontalLineTo(11.2637f)
                verticalLineTo(9.72754f)
                close()
                moveTo(10.501f, 4f)
                lineTo(10.501f, 4.2f)
                curveTo(13.9803f, 4.20015f, 16.8008f, 7.02068f, 16.801f, 10.5f)
                lineTo(17.001f, 10.5f)
                lineTo(17.201f, 10.5f)
                curveTo(17.2008f, 6.79976f, 14.2012f, 3.80016f, 10.501f, 3.8f)
                lineTo(10.501f, 4f)
                close()
                moveTo(17.001f, 10.5f)
                horizontalLineTo(16.801f)
                curveTo(16.801f, 13.9795f, 13.9804f, 16.8008f, 10.501f, 16.801f)
                lineTo(10.501f, 17.001f)
                lineTo(10.501f, 17.201f)
                curveTo(14.2013f, 17.2008f, 17.201f, 14.2003f, 17.201f, 10.5f)
                horizontalLineTo(17.001f)
                close()
                moveTo(10.501f, 17.001f)
                verticalLineTo(16.801f)
                curveTo(7.02142f, 16.801f, 4.2f, 13.9796f, 4.2f, 10.5f)
                horizontalLineTo(4f)
                horizontalLineTo(3.8f)
                curveTo(3.8f, 14.2005f, 6.80051f, 17.201f, 10.501f, 17.201f)
                verticalLineTo(17.001f)
                close()
                moveTo(4f, 10.5f)
                lineTo(4.2f, 10.5f)
                curveTo(4.20015f, 7.02061f, 7.02149f, 4.2f, 10.501f, 4.2f)
                verticalLineTo(4f)
                verticalLineTo(3.8f)
                curveTo(6.80063f, 3.8f, 3.80016f, 6.79963f, 3.8f, 10.5f)
                lineTo(4f, 10.5f)
                close()
                moveTo(11.499f, 7.37695f)
                lineTo(11.698f, 7.39677f)
                curveTo(11.7497f, 6.87827f, 11.1006f, 6.57524f, 10.7561f, 6.99763f)
                lineTo(10.9111f, 7.12402f)
                lineTo(11.0661f, 7.25042f)
                curveTo(11.1431f, 7.15609f, 11.315f, 7.20623f, 11.3f, 7.35713f)
                lineTo(11.499f, 7.37695f)
                close()
                moveTo(10.9111f, 7.12402f)
                lineTo(10.7561f, 6.99768f)
                lineTo(7.42309f, 11.0875f)
                lineTo(7.57812f, 11.2139f)
                lineTo(7.73316f, 11.3402f)
                lineTo(11.0662f, 7.25037f)
                lineTo(10.9111f, 7.12402f)
                close()
                moveTo(7.57812f, 11.2139f)
                lineTo(7.42307f, 11.0875f)
                curveTo(7.35942f, 11.1657f, 7.3174f, 11.2614f, 7.3045f, 11.3636f)
                lineTo(7.50293f, 11.3887f)
                lineTo(7.70136f, 11.4137f)
                curveTo(7.70467f, 11.3875f, 7.71575f, 11.3616f, 7.73318f, 11.3402f)
                lineTo(7.57812f, 11.2139f)
                close()
                moveTo(7.50293f, 11.3887f)
                lineTo(7.30448f, 11.3638f)
                curveTo(7.29168f, 11.466f, 7.30869f, 11.5682f, 7.35033f, 11.6593f)
                lineTo(7.53223f, 11.5762f)
                lineTo(7.71412f, 11.493f)
                curveTo(7.70228f, 11.4671f, 7.69813f, 11.4395f, 7.70138f, 11.4135f)
                lineTo(7.50293f, 11.3887f)
                close()
                moveTo(7.53223f, 11.5762f)
                lineTo(7.35039f, 11.6595f)
                curveTo(7.39258f, 11.7516f, 7.46012f, 11.8314f, 7.54622f, 11.8874f)
                lineTo(7.65527f, 11.7197f)
                lineTo(7.76433f, 11.5521f)
                curveTo(7.74354f, 11.5386f, 7.7256f, 11.5181f, 7.71406f, 11.4929f)
                lineTo(7.53223f, 11.5762f)
                close()
                moveTo(7.65527f, 11.7197f)
                lineTo(7.5464f, 11.8875f)
                curveTo(7.63265f, 11.9435f, 7.73264f, 11.9724f, 7.83391f, 11.9725f)
                lineTo(7.83398f, 11.7725f)
                lineTo(7.83406f, 11.5725f)
                curveTo(7.80888f, 11.5725f, 7.78465f, 11.5653f, 7.76414f, 11.552f)
                lineTo(7.65527f, 11.7197f)
                close()
                moveTo(7.83398f, 11.7725f)
                verticalLineTo(11.9725f)
                horizontalLineTo(9.7373f)
                verticalLineTo(11.7725f)
                verticalLineTo(11.5725f)
                horizontalLineTo(7.83398f)
                verticalLineTo(11.7725f)
                close()
                moveTo(9.7373f, 11.7725f)
                lineTo(9.5383f, 11.7525f)
                lineTo(9.30295f, 14.1041f)
                lineTo(9.50195f, 14.124f)
                lineTo(9.70096f, 14.1439f)
                lineTo(9.93631f, 11.7924f)
                lineTo(9.7373f, 11.7725f)
                close()
                moveTo(9.50195f, 14.124f)
                lineTo(9.30293f, 14.1042f)
                curveTo(9.25139f, 14.6227f, 9.90039f, 14.9255f, 10.2448f, 14.5034f)
                lineTo(10.0898f, 14.377f)
                lineTo(9.93488f, 14.2505f)
                curveTo(9.85791f, 14.3448f, 9.68598f, 14.2946f, 9.70097f, 14.1438f)
                lineTo(9.50195f, 14.124f)
                close()
                moveTo(10.0898f, 14.377f)
                lineTo(10.2449f, 14.5033f)
                lineTo(13.5788f, 10.4135f)
                lineTo(13.4238f, 10.2871f)
                lineTo(13.2688f, 10.1607f)
                lineTo(9.93482f, 14.2506f)
                lineTo(10.0898f, 14.377f)
                close()
                moveTo(13.4238f, 10.2871f)
                lineTo(13.5789f, 10.4134f)
                curveTo(13.6438f, 10.3338f, 13.6838f, 10.2376f, 13.6965f, 10.1374f)
                lineTo(13.498f, 10.1123f)
                lineTo(13.2996f, 10.0873f)
                curveTo(13.2961f, 10.1154f, 13.285f, 10.1409f, 13.2688f, 10.1608f)
                lineTo(13.4238f, 10.2871f)
                close()
                moveTo(13.498f, 10.1123f)
                lineTo(13.6965f, 10.1373f)
                curveTo(13.7091f, 10.0373f, 13.6944f, 9.93406f, 13.6516f, 9.84054f)
                lineTo(13.4697f, 9.92383f)
                lineTo(13.2879f, 10.0071f)
                curveTo(13.2988f, 10.0309f, 13.3032f, 10.0589f, 13.2996f, 10.0873f)
                lineTo(13.498f, 10.1123f)
                close()
                moveTo(13.4697f, 9.92383f)
                lineTo(13.6515f, 9.84049f)
                curveTo(13.6088f, 9.7472f, 13.5405f, 9.66883f, 13.4559f, 9.61371f)
                lineTo(13.3467f, 9.78125f)
                lineTo(13.2374f, 9.94879f)
                curveTo(13.2595f, 9.96314f, 13.277f, 9.98345f, 13.2879f, 10.0072f)
                lineTo(13.4697f, 9.92383f)
                close()
                moveTo(13.3467f, 9.78125f)
                lineTo(13.4557f, 9.6136f)
                curveTo(13.3709f, 9.55842f, 13.2711f, 9.52772f, 13.1683f, 9.52754f)
                lineTo(13.168f, 9.72754f)
                lineTo(13.1676f, 9.92754f)
                curveTo(13.1912f, 9.92758f, 13.2158f, 9.93471f, 13.2376f, 9.9489f)
                lineTo(13.3467f, 9.78125f)
                close()
                moveTo(13.168f, 9.72754f)
                verticalLineTo(9.52754f)
                horizontalLineTo(11.2637f)
                verticalLineTo(9.72754f)
                verticalLineTo(9.92754f)
                horizontalLineTo(13.168f)
                verticalLineTo(9.72754f)
                close()
                moveTo(11.2637f, 9.72754f)
                lineTo(11.4627f, 9.74746f)
                lineTo(11.698f, 7.39688f)
                lineTo(11.499f, 7.37695f)
                lineTo(11.3f, 7.35703f)
                lineTo(11.0647f, 9.70761f)
                lineTo(11.2637f, 9.72754f)
                close()
            }
        }.build()
        
        return _packsPingChat!!
    }

private var _packsPingChat: ImageVector? = null

