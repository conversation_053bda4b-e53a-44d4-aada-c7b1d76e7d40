package com.flutterup.app.design.component.pager

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp


@Composable
fun LinearPagerIndicator(
    pageCount: Int,
    selectedPage: Int,
    modifier: Modifier = Modifier,
    config: LinearPagerIndicatorConfig = LinearPagerIndicatorDefaults.defaults
) {
    OrientationContent(
        modifier = modifier,
        config = config
    ) {
        repeat(pageCount) { iteration ->
            val color = if (iteration == selectedPage) config.selectedColor else config.unselectedColor
            val width = if (iteration == selectedPage) config.selectedIndicatorWidth else config.indicatorWidth
            val height = if (iteration == selectedPage) config.selectedIndicatorHeight else config.indicatorHeight

            Box(
                modifier = Modifier
                    .then(if (config.shape != null) Modifier.clip(config.shape) else Modifier)
                    .size(width, height)
                    .background(color)
            )
        }
    }
}

data class LinearPagerIndicatorConfig(
    val orientation: Orientation = Orientation.Horizontal,

    val spaceBetween: Dp = 10.dp,

    val indicatorWidth: Dp = 2.dp,

    val indicatorHeight: Dp = 20.dp,

    val selectedIndicatorWidth: Dp = 2.dp,

    val selectedIndicatorHeight: Dp = 40.dp,

    val selectedColor: Color = Color.White,

    val unselectedColor: Color = Color.White.copy(0.4f),

    val shape: Shape? = null,
)

object LinearPagerIndicatorDefaults {

    val defaults = LinearPagerIndicatorConfig()

    fun copy(
        orientation: Orientation = defaults.orientation,
        spaceBetween: Dp = defaults.spaceBetween,
        indicatorWidth: Dp = defaults.indicatorWidth,
        indicatorHeight: Dp = defaults.indicatorHeight,
        selectedIndicatorWidth: Dp = defaults.selectedIndicatorWidth,
        selectedIndicatorHeight: Dp = defaults.selectedIndicatorHeight,
        selectedColor: Color = defaults.selectedColor,
        unselectedColor: Color = defaults.unselectedColor,
        shape: Shape? = defaults.shape,
    ) = defaults.copy(
        orientation = orientation,
        spaceBetween = spaceBetween,
        indicatorWidth = indicatorWidth,
        indicatorHeight = indicatorHeight,
        selectedIndicatorWidth = selectedIndicatorWidth,
        selectedIndicatorHeight = selectedIndicatorHeight,
        selectedColor = selectedColor,
        unselectedColor = unselectedColor,
        shape = shape,
    )
}


@Composable
private fun OrientationContent(
    modifier: Modifier = Modifier,
    config: LinearPagerIndicatorConfig,
    content: @Composable () -> Unit
) {
    when(config.orientation) {
        Orientation.Horizontal -> {
            Row(
                modifier = modifier,
                horizontalArrangement = Arrangement.spacedBy(config.spaceBetween),
                verticalAlignment = Alignment.CenterVertically
            ) {
                content()
            }
        }
        Orientation.Vertical -> {
            Column(
                modifier = modifier,
                verticalArrangement = Arrangement.spacedBy(config.spaceBetween),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                content()
            }
        }
    }
}