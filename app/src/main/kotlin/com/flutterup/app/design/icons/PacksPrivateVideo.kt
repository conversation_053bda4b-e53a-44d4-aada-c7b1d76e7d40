package com.flutterup.app.design.icons

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.TileMode
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val PacksPrivateVideo: ImageVector
    get() {
        if (_packsPrivateVideo != null) return _packsPrivateVideo!!
        
        _packsPrivateVideo = ImageVector.Builder(
            name = "packsPrivateVideo",
            defaultWidth = 21.dp,
            defaultHeight = 21.dp,
            viewportWidth = 21f,
            viewportHeight = 21f
        ).apply {
            path {
            }
            group {
                path(
                    fill = Brush.linearGradient(
                        colors = listOf(Color(0xFFC37066), Color(0xFFF2C9B3)),
                        start = Offset(16.589f, -3.7336e-7f),
                        end = Offset(-7.77509e-7f, 19.7389f),
                        tileMode = TileMode.Clamp
                    )
                ) {
                    moveTo(20.9988f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 10.4994f, 20.9988f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 0f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 20.9988f, 10.4994f)
                    close()
                }
            }
            group {
                path {
                    moveTo(20.9988f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 10.4994f, 20.9988f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 0f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 20.9988f, 10.4994f)
                    close()
                }
            }
            group {
                path(
                    fill = Brush.linearGradient(
                        colors = listOf(Color(0xFFffffff), Color(0xFFFFD4CD)),
                        start = Offset(10.4994f, 2.81616e-7f),
                        end = Offset(1.04994f, 17.219f),
                        tileMode = TileMode.Clamp
                    )
                ) {
                    moveTo(20.9988f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 10.4994f, 20.9988f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 0f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 20.9988f, 10.4994f)
                    close()
                }
            }
            group {
                path(
                    fill = SolidColor(Color(0xFFAF47DE))
                ) {
                    moveTo(20.9988f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 10.4994f, 20.9988f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 0f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 20.9988f, 10.4994f)
                    close()
                }
            }
            path(
                fill = Brush.linearGradient(
                    colors = listOf(Color(0xFFffffff), Color(0xFFffffff)),
                    start = Offset(10.5f, 6f),
                    end = Offset(10.5f, 15f),
                    tileMode = TileMode.Clamp
                ),
                stroke = SolidColor(Color(0xFFFFFFFF)),
                strokeLineWidth = 0.4f
            ) {
                moveTo(5.94922f, 6.2002f)
                horizontalLineTo(11.7959f)
                curveTo(12.2604f, 6.2002f, 12.7054f, 6.38275f, 13.0332f, 6.70703f)
                curveTo(13.361f, 7.03137f, 13.5449f, 7.47095f, 13.5449f, 7.92871f)
                verticalLineTo(9.31641f)
                lineTo(13.8809f, 9.00781f)
                lineTo(15.2764f, 7.72266f)
                curveTo(15.4066f, 7.6039f, 15.5698f, 7.52523f, 15.7451f, 7.49707f)
                curveTo(15.9205f, 7.46892f, 16.1008f, 7.49233f, 16.2627f, 7.56445f)
                verticalLineTo(7.56543f)
                lineTo(16.2705f, 7.56836f)
                curveTo(16.4265f, 7.62958f, 16.5601f, 7.73552f, 16.6543f, 7.87207f)
                curveTo(16.7249f, 7.97449f, 16.7711f, 8.09065f, 16.79f, 8.21191f)
                lineTo(16.7998f, 8.33496f)
                verticalLineTo(12.666f)
                curveTo(16.7995f, 12.8303f, 16.7494f, 12.9905f, 16.6562f, 13.127f)
                curveTo(16.5631f, 13.2633f, 16.4312f, 13.3696f, 16.2764f, 13.4316f)
                lineTo(16.2686f, 13.4355f)
                curveTo(16.1532f, 13.4869f, 16.028f, 13.513f, 15.9014f, 13.5137f)
                curveTo(15.7007f, 13.5125f, 15.5066f, 13.4481f, 15.3477f, 13.3301f)
                lineTo(15.2822f, 13.2764f)
                lineTo(13.8799f, 11.9922f)
                lineTo(13.5449f, 11.6846f)
                verticalLineTo(13.0713f)
                curveTo(13.5449f, 13.5291f, 13.361f, 13.9686f, 13.0332f, 14.293f)
                curveTo(12.7054f, 14.6173f, 12.2604f, 14.7998f, 11.7959f, 14.7998f)
                horizontalLineTo(5.94922f)
                curveTo(5.48476f, 14.7998f, 5.03974f, 14.6173f, 4.71191f, 14.293f)
                curveTo(4.38414f, 13.9686f, 4.2002f, 13.529f, 4.2002f, 13.0713f)
                verticalLineTo(7.92871f)
                curveTo(4.2002f, 7.47095f, 4.38414f, 7.03137f, 4.71191f, 6.70703f)
                curveTo(5.03974f, 6.38274f, 5.48476f, 6.2002f, 5.94922f, 6.2002f)
                close()
            }
        }.build()
        
        return _packsPrivateVideo!!
    }

private var _packsPrivateVideo: ImageVector? = null

