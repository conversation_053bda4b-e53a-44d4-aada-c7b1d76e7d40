package com.flutterup.app.design.extension

import android.net.Uri
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import com.flutterup.app.utils.MediaSizeCheckUtils


/**
 *
 */
@Composable
fun rememberPickVisualMediaRequest(
    contract: ActivityResultContracts.PickVisualMedia = ActivityResultContracts.PickVisualMedia(),
    onFailure: (Uri?) -> Unit = {},
    onResult: (Uri?) -> Unit
): ManagedActivityResultLauncher<PickVisualMediaRequest, Uri?> {
    val context = LocalContext.current

    return rememberLauncherForActivityResult(contract) {
        if (MediaSizeCheckUtils.sizeCheck(context, it)) {
            onResult(it)
        } else {
            onFailure(it)
        }
    }
}