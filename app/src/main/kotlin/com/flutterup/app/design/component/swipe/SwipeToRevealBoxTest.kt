package com.flutterup.app.design.component.swipe

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch

/**
 * SwipeToRevealBox 简单测试组件
 */
@Composable
fun SwipeToRevealBoxTest() {
    val state = rememberSwipeToRevealBoxState(
        confirmValueChange = { newValue ->
            // 可以在这里添加状态变化的确认逻辑
            println("状态变化: $newValue")
            true
        }
    )
    val scope = rememberCoroutineScope()
    
    SwipeToRevealBox(
        state = state,
        modifier = Modifier
            .fillMaxWidth()
            .height(80.dp)
            .padding(16.dp),
        revealSize = 80.dp,
        backgroundContent = {
            // 背景内容
            when (state.dismissDirection) {
                SwipeToRevealBoxValue.StartToEnd -> {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color.Green.copy(alpha = 0.3f)),
                        contentAlignment = Alignment.CenterStart
                    ) {
                        Text(
                            text = "向右滑动 ${(state.progress * 100).toInt()}%",
                            modifier = Modifier.padding(start = 16.dp),
                            color = Color.Green
                        )
                    }
                }
                SwipeToRevealBoxValue.EndToStart -> {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color.Red.copy(alpha = 0.3f)),
                        contentAlignment = Alignment.CenterEnd
                    ) {
                        Text(
                            text = "向左滑动 ${(state.progress * 100).toInt()}%",
                            modifier = Modifier.padding(end = 16.dp),
                            color = Color.Red
                        )
                    }
                }
                SwipeToRevealBoxValue.Settled -> {
                    // 静止状态
                }
            }
        }
    ) {
        // 主要内容
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.surface),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "滑动测试 - 当前: ${state.currentValue}",
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}
