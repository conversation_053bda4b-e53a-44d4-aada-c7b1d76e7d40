@file:OptIn(ExperimentalHazeMaterialsApi::class)

package com.flutterup.app.design.haze

import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.dp
import dev.chrisbanes.haze.HazeStyle
import dev.chrisbanes.haze.materials.ExperimentalHazeMaterialsApi
import dev.chrisbanes.haze.materials.HazeMaterials

@get:Composable
val DefaultHazeStyle: HazeStyle
    get() = HazeMaterials.ultraThin().copy(blurRadius = 10.dp)