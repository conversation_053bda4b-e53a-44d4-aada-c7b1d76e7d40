package com.flutterup.app.design.modifiers

import androidx.compose.runtime.Composable
import com.flutterup.app.design.theme.ShimmerOnPrimary
import com.flutterup.app.design.theme.ShimmerPrimary
import com.valentinilk.shimmer.LocalShimmerTheme
import com.valentinilk.shimmer.Shimmer
import com.valentinilk.shimmer.ShimmerBounds
import com.valentinilk.shimmer.rememberShimmer


@Composable
fun rememberShimmerPrimary(bounds: ShimmerBounds = ShimmerBounds.View): Shimmer {
    val theme = LocalShimmerTheme.current

    return rememberShimmer(
        shimmerBounds = bounds,
        theme = theme.copy(
            shaderColors = listOf(
                ShimmerPrimary.copy(0.25f),
                ShimmerPrimary.copy(1f),
                ShimmerPrimary.copy(0.25f),
            )
        )
    )
}

@Composable
fun rememberShimmerOnPrimary(bounds: ShimmerBounds = ShimmerBounds.View): Shimmer {
    val theme = LocalShimmerTheme.current

    return rememberShimmer(
        shimmerBounds = bounds,
        theme = theme.copy(
            shaderColors = listOf(
                ShimmerOnPrimary.copy(0.5f),
                ShimmerOnPrimary.copy(1f),
                ShimmerOnPrimary.copy(0.5f),
            )
        )
    )
}