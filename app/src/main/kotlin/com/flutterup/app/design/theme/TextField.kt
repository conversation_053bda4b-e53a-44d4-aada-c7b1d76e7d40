package com.flutterup.app.design.theme

import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.TextFieldColors
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color


val TransparentIndicatorTextFieldColors: TextFieldColors @Composable get() {
    return TextFieldDefaults.colors(
        errorIndicatorColor = Color.Transparent,
        focusedIndicatorColor = Color.Transparent,
        unfocusedIndicatorColor = Color.Transparent,
        disabledIndicatorColor = Color.Transparent
    )
}

internal val TransparentTextFieldColors: TextFieldColors @Composable get() {
    return TransparentIndicatorTextFieldColors.copy(
        errorContainerColor = Color.Transparent,
        disabledContainerColor = Color.Transparent,
        focusedContainerColor = Color.Transparent,
        unfocusedContainerColor = Color.Transparent
    )
}

internal val PurpleTextFieldColors: TextFieldColors @Composable get() {
    return TransparentIndicatorTextFieldColors.copy(
        errorContainerColor = PurpleTertiaryContainer,
        disabledContainerColor = PurpleTertiaryContainer,
        focusedContainerColor = PurpleTertiaryContainer,
        unfocusedContainerColor = PurpleTertiaryContainer,

        errorLeadingIconColor = PurpleSecondary,
        disabledLeadingIconColor = PurpleSecondary,
        focusedLeadingIconColor = PurpleSecondary,
        unfocusedLeadingIconColor = PurpleSecondary,

        errorTrailingIconColor = PurpleSecondary,
        disabledTrailingIconColor = PurpleSecondary,
        focusedTrailingIconColor = PurpleSecondary,
        unfocusedTrailingIconColor = PurpleSecondary,

        errorTextColor = MaterialTheme.colorScheme.error,
        disabledTextColor = TextBlack333,
        focusedTextColor = TextBlack333,
        unfocusedTextColor = TextBlack333,

        errorSupportingTextColor = PurpleSecondary,
        disabledSupportingTextColor = PurpleSecondary,
        focusedSupportingTextColor = PurpleSecondary,
        unfocusedSupportingTextColor = PurpleSecondary,

        errorPlaceholderColor = PurpleSecondary,
        disabledPlaceholderColor = PurpleSecondary,
        focusedPlaceholderColor = PurpleSecondary,
        unfocusedPlaceholderColor = PurpleSecondary,

        cursorColor = PurpleSecondary,
        errorCursorColor = MaterialTheme.colorScheme.error
    )
}

internal val WhiteTextFieldColors: TextFieldColors @Composable get() {
    return TransparentIndicatorTextFieldColors.copy(
        errorContainerColor = Color.White,
        disabledContainerColor = Color.White,
        focusedContainerColor = Color.White,
        unfocusedContainerColor = Color.White,

        errorLeadingIconColor = PurpleSecondary,
        disabledLeadingIconColor = PurpleSecondary,
        focusedLeadingIconColor = PurpleSecondary,
        unfocusedLeadingIconColor = PurpleSecondary,

        errorTrailingIconColor = PurpleSecondary,
        disabledTrailingIconColor = PurpleSecondary,
        focusedTrailingIconColor = PurpleSecondary,
        unfocusedTrailingIconColor = PurpleSecondary,

        errorTextColor = MaterialTheme.colorScheme.error,
        disabledTextColor = TextBlack333,
        focusedTextColor = TextBlack333,
        unfocusedTextColor = TextBlack333,

        errorSupportingTextColor = TextGray999,
        disabledSupportingTextColor = TextGray999,
        focusedSupportingTextColor = TextGray999,
        unfocusedSupportingTextColor = TextGray999,

        errorPlaceholderColor = TextGray999,
        disabledPlaceholderColor = TextGray999,
        focusedPlaceholderColor = TextGray999,
        unfocusedPlaceholderColor = TextGray999,

        cursorColor = TextBlack333,
        errorCursorColor = MaterialTheme.colorScheme.error
    )
}


internal val IndicatorTransparentTextFieldColors: TextFieldColors @Composable get() {
    return TransparentTextFieldColors.copy(
        errorTextColor = MaterialTheme.colorScheme.error,
        disabledTextColor = TextBlack26,
        focusedTextColor = TextBlack26,
        unfocusedTextColor = TextBlack26,

        errorPlaceholderColor = TextBlack3b.copy(0.5f),
        disabledPlaceholderColor = TextBlack3b.copy(0.5f),
        focusedPlaceholderColor = TextBlack3b.copy(0.5f),
        unfocusedPlaceholderColor = TextBlack3b.copy(0.5f),

        errorIndicatorColor = MaterialTheme.colorScheme.error,
        focusedIndicatorColor = LinePrimary,
        unfocusedIndicatorColor = LinePrimary,
        disabledIndicatorColor = LinePrimary,

        errorSupportingTextColor = MaterialTheme.colorScheme.error,
        disabledSupportingTextColor = TextBlack3b,
        focusedSupportingTextColor = TextBlack3b,
        unfocusedSupportingTextColor = TextBlack3b,

        errorLabelColor = MaterialTheme.colorScheme.error,
        disabledLabelColor = TextBlack666,
        focusedLabelColor = TextBlack666,
        unfocusedLabelColor = TextBlack666,

        cursorColor = TextBlack26,
        errorCursorColor = MaterialTheme.colorScheme.error
    )
}