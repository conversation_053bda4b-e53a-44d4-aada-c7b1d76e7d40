package com.flutterup.app.design.component

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.flutterup.app.design.theme.AppTheme
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 最终版本的雷达效果组件
 */
@Composable
fun FinalRadarEffect(
    modifier: Modifier = Modifier,
    isActive: Boolean = true,
    color: Color = Color.Blue,
    borderColor: Color = Color.Unspecified,
    strokeWidth: Dp = 3.dp,
    animationDuration: Int = 2000,
    waveInterval: Long = 600L,
    maxWaveCount: Int = 3,
    useGradient: Boolean = false
) {
    val density = LocalDensity.current
    val strokeWidthPx = with(density) { strokeWidth.toPx() }
    
    // 创建动画状态 - 使用简单的Animatable列表
    val waveScales = remember { 
        List(maxWaveCount) { Animatable(0f) }
    }
    
    // 启动动画 - 使用验证可工作的逻辑
    LaunchedEffect(isActive) {
        if (isActive) {
            while (true) {
                waveScales.forEachIndexed { index, scale ->
                    launch {
                        delay(index * waveInterval) // 交错启动
                        
                        scale.snapTo(0f)
                        scale.animateTo(
                            targetValue = 1f,
                            animationSpec = tween(
                                durationMillis = animationDuration,
                                easing = LinearEasing
                            )
                        )
                    }
                }
                
                // 等待所有动画完成后再开始下一轮
                delay(animationDuration + waveInterval * maxWaveCount)
            }
        } else {
            // 停止所有动画
            waveScales.forEach { scale ->
                scale.stop()
                scale.snapTo(0f)
            }
        }
    }
    
    Box(modifier = modifier) {
        Canvas(
            modifier = Modifier.matchParentSize()
        ) {
            val center = Offset(size.width / 2f, size.height / 2f)
            val maxRadius = minOf(size.width, size.height) / 2f - strokeWidthPx
            
            // 绘制所有波浪
            waveScales.forEach { scale ->
                val currentRadius = maxRadius * scale.value
                val alpha = (1f - scale.value).coerceAtLeast(0f)
                
                if (currentRadius > 0f && alpha > 0f) {
                    if (useGradient) {
                        // 渐变版本
                        val gradient = Brush.radialGradient(
                            colors = listOf(
                                color.copy(alpha = alpha),
                                color.copy(alpha = alpha * 0.5f),
                                Color.Transparent
                            ),
                            center = center,
                            radius = currentRadius + strokeWidthPx
                        )

                        if (borderColor != Color.Unspecified) {
                            drawCircle(
                                color = borderColor,
                                radius = currentRadius,
                                center = center,
                                style = Stroke(width = strokeWidthPx)
                            )
                        }
                        
                        drawCircle(
                            brush = gradient,
                            radius = currentRadius + strokeWidthPx / 2f,
                            center = center
                        )
                    } else {
                        if (borderColor != Color.Unspecified) {
                            drawCircle(
                                color = borderColor,
                                radius = currentRadius,
                                center = center,
                                style = Stroke(width = strokeWidthPx)
                            )
                        }

                        // 纯色版本
                        drawCircle(
                            color = color.copy(alpha = alpha),
                            radius = currentRadius,
                            center = center,
                            style = Stroke(width = strokeWidthPx)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 匹配页面雷达背景 - 最终版本
 */
@Composable
fun FinalMatchedRadarBackground(
    modifier: Modifier = Modifier,
    isActive: Boolean = true,
    primaryColor: Color = Color(0xFFFF6B9D), // 粉色
    secondaryColor: Color = Color(0xFF9C88FF), // 紫色
    tertiaryColor: Color = Color(0xFFE91E63), // 深粉色
    borderPrimaryColor: Color = Color(0xFFE91E63), // 深粉色
    borderSecondaryColor: Color = Color(0xFF9C88FF), // 紫色
    borderTertiaryColor: Color = Color(0xFFFF6B9D) // 粉色
) {
    Box(
        modifier = modifier
            .fillMaxSize()
    ) {
        // 第一层 - 最大最慢
        FinalRadarEffect(
            modifier = Modifier.fillMaxSize(),
            isActive = isActive,
            color = primaryColor,
            borderColor = borderPrimaryColor,
            strokeWidth = 2.dp,
            animationDuration = 5000,
            waveInterval = 5000,
            maxWaveCount = 3,
            useGradient = true
        )
        
        // 第二层 - 中等
        FinalRadarEffect(
            modifier = Modifier.fillMaxSize(),
            isActive = isActive,
            color = secondaryColor,
            borderColor = borderSecondaryColor,
            strokeWidth = 2.dp,
            animationDuration = 5000,
            waveInterval = 5000,
            maxWaveCount = 3,
            useGradient = true
        )
        
        // 第三层 - 最小最快
        FinalRadarEffect(
            modifier = Modifier.fillMaxSize(),
            isActive = isActive,
            color = tertiaryColor,
            borderColor = borderTertiaryColor,
            strokeWidth = 2.dp,
            animationDuration = 5000,
            waveInterval = 2000,
            maxWaveCount = 3,
            useGradient = true
        )
    }
}

/**
 * 简化版雷达效果
 */
@Composable
fun SimpleFinalRadar(
    modifier: Modifier = Modifier,
    isActive: Boolean = true,
    color: Color = Color.Blue.copy(alpha = 0.6f)
) {
    FinalRadarEffect(
        modifier = modifier,
        isActive = isActive,
        color = color,
        strokeWidth = 4.dp,
        animationDuration = 2500,
        waveInterval = 800L,
        maxWaveCount = 3,
        useGradient = true
    )
}

@Preview(showBackground = true)
@Composable
private fun FinalRadarEffectPreview() {
    AppTheme {
        Box(modifier = Modifier.fillMaxSize()) {
            FinalRadarEffect(
                modifier = Modifier.fillMaxSize(),
                color = Color.Blue,
                strokeWidth = 4.dp,
                useGradient = false
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun FinalMatchedRadarBackgroundPreview() {
    AppTheme {
        FinalMatchedRadarBackground(
            modifier = Modifier.fillMaxSize()
        )
    }
}
