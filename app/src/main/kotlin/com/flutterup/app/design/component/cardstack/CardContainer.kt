package com.flutterup.app.design.component.cardstack

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.zIndex

@Composable
fun CardContainer(cardState: CardSate, content: @Composable () -> Unit) {

    Box(
        modifier = Modifier
            .fillMaxSize()
            .zIndex(cardState.zIndex)
            .graphicsLayer {
                translationX = cardState.offsetX.value
                translationY = cardState.offsetY.value
                scaleX = cardState.scaleX.value
                scaleY = cardState.scaleY.value
                rotationZ = cardState.rotation.value
                alpha = cardState.alpha.value
            }
    ) {
        content()
    }
}