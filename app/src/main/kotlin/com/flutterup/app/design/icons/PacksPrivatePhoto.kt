package com.flutterup.app.design.icons

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.TileMode
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val PacksPrivatePhoto: ImageVector
    get() {
        if (_packsPrivatePhoto != null) return _packsPrivatePhoto!!
        
        _packsPrivatePhoto = ImageVector.Builder(
            name = "PacksPrivatePhoto",
            defaultWidth = 21.dp,
            defaultHeight = 21.dp,
            viewportWidth = 21f,
            viewportHeight = 21f
        ).apply {
            path {
            }
            group {
                path(
                    fill = Brush.linearGradient(
                        colors = listOf(Color(0xFFC37066), Color(0xFFF2C9B3)),
                        start = Offset(16.589f, -3.7336e-7f),
                        end = Offset(-7.77509e-7f, 19.7389f),
                        tileMode = TileMode.Clamp
                    )
                ) {
                    moveTo(20.9988f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 10.4994f, 20.9988f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 0f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 20.9988f, 10.4994f)
                    close()
                }
            }
            group {
                path {
                    moveTo(20.9988f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 10.4994f, 20.9988f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 0f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 20.9988f, 10.4994f)
                    close()
                }
            }
            group {
                path(
                    fill = Brush.linearGradient(
                        colors = listOf(Color(0xFFffffff), Color(0xFFFFD4CD)),
                        start = Offset(10.4994f, 2.81616e-7f),
                        end = Offset(1.04994f, 17.219f),
                        tileMode = TileMode.Clamp
                    )
                ) {
                    moveTo(20.9988f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 10.4994f, 20.9988f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 0f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 20.9988f, 10.4994f)
                    close()
                }
            }
            group {
                path(
                    fill = SolidColor(Color(0xFFFF8400))
                ) {
                    moveTo(20.9988f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 10.4994f, 20.9988f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 0f, 10.4994f)
                    arcTo(10.4994f, 10.4994f, 0f, false, true, 20.9988f, 10.4994f)
                    close()
                }
            }
            path(
                fill = SolidColor(Color(0xFFFFFFFF))
            ) {
            }
            path(
                fill = SolidColor(Color(0xFFFFFFFF))
            ) {
                moveTo(10.4141f, 5f)
                curveTo(10.5218f, 5f, 10.6251f, 5.04311f, 10.7012f, 5.11914f)
                curveTo(10.7774f, 5.19528f, 10.8203f, 5.29858f, 10.8204f, 5.40625f)
                curveTo(10.8204f, 5.51399f, 10.7774f, 5.61717f, 10.7012f, 5.69336f)
                curveTo(10.6251f, 5.76951f, 10.5219f, 5.8125f, 10.4141f, 5.8125f)
                curveTo(9.33478f, 5.81367f, 8.53228f, 5.82288f, 7.90437f, 5.90723f)
                curveTo(7.21234f, 6.00035f, 6.79488f, 6.17786f, 6.4864f, 6.48633f)
                curveTo(6.17798f, 6.79478f, 6.00139f, 7.21275f, 5.90828f, 7.90527f)
                curveTo(5.81345f, 8.60947f, 5.81258f, 9.53406f, 5.81258f, 10.8213f)
                verticalLineTo(11.2773f)
                lineTo(6.35457f, 10.8037f)
                curveTo(6.59225f, 10.5958f, 6.90026f, 10.4857f, 7.2159f, 10.4961f)
                curveTo(7.53161f, 10.5066f, 7.8323f, 10.6361f, 8.05574f, 10.8594f)
                lineTo(10.378f, 13.1816f)
                curveTo(10.5582f, 13.3619f, 10.796f, 13.4731f, 11.0499f, 13.4951f)
                curveTo(11.3037f, 13.5171f, 11.5572f, 13.4492f, 11.7657f, 13.3027f)
                lineTo(11.9278f, 13.1895f)
                curveTo(12.2286f, 12.9781f, 12.5921f, 12.8744f, 12.9591f, 12.8965f)
                curveTo(13.326f, 12.9186f, 13.6751f, 13.0646f, 13.9483f, 13.3105f)
                lineTo(15.4805f, 14.6904f)
                curveTo(15.6348f, 14.3663f, 15.7266f, 13.9404f, 15.7755f, 13.3398f)
                curveTo(15.8214f, 12.7742f, 15.8276f, 12.0875f, 15.8282f, 11.2275f)
                curveTo(15.8282f, 11.1198f, 15.8712f, 11.0156f, 15.9473f, 10.9395f)
                curveTo(16.0235f, 10.8634f, 16.1269f, 10.8214f, 16.2345f, 10.8213f)
                curveTo(16.342f, 10.8213f, 16.4454f, 10.8635f, 16.5216f, 10.9395f)
                curveTo(16.5977f, 11.0156f, 16.6407f, 11.1198f, 16.6407f, 11.2275f)
                curveTo(16.6395f, 12.0828f, 16.6333f, 12.8089f, 16.585f, 13.4053f)
                curveTo(16.5286f, 14.0979f, 16.4128f, 14.6771f, 16.1544f, 15.1572f)
                curveTo(16.0415f, 15.3674f, 15.8982f, 15.5597f, 15.7296f, 15.7285f)
                curveTo(15.2448f, 16.2133f, 14.6279f, 16.4333f, 13.8458f, 16.5381f)
                curveTo(13.0816f, 16.6411f, 12.1012f, 16.6406f, 10.8516f, 16.6406f)
                horizontalLineTo(10.7901f)
                curveTo(9.53998f, 16.6406f, 8.56074f, 16.6411f, 7.79597f, 16.5381f)
                curveTo(7.01431f, 16.4333f, 6.39643f, 16.2133f, 5.91218f, 15.7285f)
                curveTo(5.48292f, 15.2991f, 5.26021f, 14.7652f, 5.14265f, 14.1025f)
                curveTo(5.02625f, 13.4507f, 5.0061f, 12.6401f, 5.00203f, 11.6338f)
                curveTo(5.00087f, 11.3779f, 4.99969f, 11.107f, 5.00008f, 10.8213f)
                verticalLineTo(10.7891f)
                curveTo(5.00008f, 9.53924f, 5.00062f, 8.56054f, 5.10359f, 7.7959f)
                curveTo(5.20835f, 7.01431f, 5.42741f, 6.39633f, 5.91218f, 5.91211f)
                curveTo(6.39701f, 5.42788f, 7.01431f, 5.20828f, 7.79597f, 5.10352f)
                curveTo(8.47571f, 5.01216f, 9.34977f, 5.00117f, 10.4141f, 5f)
                close()
                moveTo(14.0792f, 5.00098f)
                curveTo(15.2862f, 5.00098f, 15.8907f, 5.00095f, 16.2657f, 5.37598f)
                curveTo(16.6406f, 5.75099f, 16.6407f, 6.35487f, 16.6407f, 7.56152f)
                curveTo(16.6407f, 8.76856f, 16.6407f, 9.37301f, 16.2657f, 9.74805f)
                curveTo(15.8907f, 10.1231f, 15.2862f, 10.123f, 14.0792f, 10.123f)
                curveTo(12.8725f, 10.123f, 12.2686f, 10.1229f, 11.8936f, 9.74805f)
                curveTo(11.5186f, 9.37301f, 11.5186f, 8.76858f, 11.5186f, 7.56152f)
                curveTo(11.5186f, 6.35471f, 11.5186f, 5.75098f, 11.8936f, 5.37598f)
                curveTo(12.2686f, 5.001f, 12.8724f, 5.00098f, 14.0792f, 5.00098f)
                close()
                moveTo(14.0792f, 5.93262f)
                curveTo(13.4365f, 5.93284f, 12.9151f, 6.45393f, 12.9151f, 7.09668f)
                curveTo(12.9152f, 7.21166f, 12.9319f, 7.32284f, 12.963f, 7.42773f)
                curveTo(12.6586f, 7.58089f, 12.4493f, 7.89674f, 12.4493f, 8.26074f)
                curveTo(12.4496f, 8.77466f, 12.867f, 9.19118f, 13.3809f, 9.19141f)
                horizontalLineTo(14.7774f)
                curveTo(15.2915f, 9.1914f, 15.7087f, 8.77479f, 15.7091f, 8.26074f)
                curveTo(15.7091f, 7.89607f, 15.4988f, 7.58058f, 15.1934f, 7.42773f)
                curveTo(15.2246f, 7.32274f, 15.2432f, 7.21178f, 15.2432f, 7.09668f)
                curveTo(15.2432f, 6.45392f, 14.7219f, 5.93282f, 14.0792f, 5.93262f)
                close()
                moveTo(14.0792f, 6.39844f)
                curveTo(14.4647f, 6.39864f, 14.7774f, 6.71108f, 14.7774f, 7.09668f)
                curveTo(14.7774f, 7.17846f, 14.7603f, 7.25618f, 14.7345f, 7.3291f)
                horizontalLineTo(13.4229f)
                curveTo(13.3971f, 7.2562f, 13.38f, 7.17843f, 13.38f, 7.09668f)
                curveTo(13.38f, 6.71109f, 13.6936f, 6.39866f, 14.0792f, 6.39844f)
                close()
            }
            path(
                fill = SolidColor(Color(0xFFFFFFFF))
            ) {
                moveTo(10.4141f, 5f)
                verticalLineTo(4.9f)
                lineTo(10.414f, 4.9f)
                lineTo(10.4141f, 5f)
                close()
                moveTo(10.7012f, 5.11914f)
                lineTo(10.772f, 5.04843f)
                lineTo(10.7719f, 5.04837f)
                lineTo(10.7012f, 5.11914f)
                close()
                moveTo(10.8204f, 5.40625f)
                horizontalLineTo(10.9204f)
                verticalLineTo(5.4062f)
                lineTo(10.8204f, 5.40625f)
                close()
                moveTo(10.7012f, 5.69336f)
                lineTo(10.7719f, 5.76408f)
                lineTo(10.772f, 5.76407f)
                lineTo(10.7012f, 5.69336f)
                close()
                moveTo(10.4141f, 5.8125f)
                verticalLineTo(5.7125f)
                lineTo(10.414f, 5.7125f)
                lineTo(10.4141f, 5.8125f)
                close()
                moveTo(7.90437f, 5.90723f)
                lineTo(7.89106f, 5.80812f)
                lineTo(7.89104f, 5.80812f)
                lineTo(7.90437f, 5.90723f)
                close()
                moveTo(6.4864f, 6.48633f)
                lineTo(6.41569f, 6.41562f)
                lineTo(6.41569f, 6.41562f)
                lineTo(6.4864f, 6.48633f)
                close()
                moveTo(5.90828f, 7.90527f)
                lineTo(6.00738f, 7.91862f)
                lineTo(6.00739f, 7.9186f)
                lineTo(5.90828f, 7.90527f)
                close()
                moveTo(5.81258f, 11.2773f)
                horizontalLineTo(5.71258f)
                verticalLineTo(11.4975f)
                lineTo(5.87838f, 11.3526f)
                lineTo(5.81258f, 11.2773f)
                close()
                moveTo(6.35457f, 10.8037f)
                lineTo(6.42037f, 10.879f)
                lineTo(6.42041f, 10.879f)
                lineTo(6.35457f, 10.8037f)
                close()
                moveTo(7.2159f, 10.4961f)
                lineTo(7.21921f, 10.3961f)
                lineTo(7.21918f, 10.3961f)
                lineTo(7.2159f, 10.4961f)
                close()
                moveTo(8.05574f, 10.8594f)
                lineTo(8.12645f, 10.7887f)
                lineTo(8.12642f, 10.7886f)
                lineTo(8.05574f, 10.8594f)
                close()
                moveTo(10.378f, 13.1816f)
                lineTo(10.4487f, 13.1109f)
                lineTo(10.4487f, 13.1109f)
                lineTo(10.378f, 13.1816f)
                close()
                moveTo(11.0499f, 13.4951f)
                lineTo(11.0412f, 13.5947f)
                horizontalLineTo(11.0412f)
                lineTo(11.0499f, 13.4951f)
                close()
                moveTo(11.7657f, 13.3027f)
                lineTo(11.7084f, 13.2208f)
                lineTo(11.7082f, 13.2209f)
                lineTo(11.7657f, 13.3027f)
                close()
                moveTo(11.9278f, 13.1895f)
                lineTo(11.9851f, 13.2714f)
                lineTo(11.9853f, 13.2713f)
                lineTo(11.9278f, 13.1895f)
                close()
                moveTo(12.9591f, 12.8965f)
                lineTo(12.9651f, 12.7967f)
                horizontalLineTo(12.9651f)
                lineTo(12.9591f, 12.8965f)
                close()
                moveTo(13.9483f, 13.3105f)
                lineTo(14.0152f, 13.2362f)
                lineTo(14.0152f, 13.2362f)
                lineTo(13.9483f, 13.3105f)
                close()
                moveTo(15.4805f, 14.6904f)
                lineTo(15.4136f, 14.7647f)
                lineTo(15.5132f, 14.8545f)
                lineTo(15.5708f, 14.7334f)
                lineTo(15.4805f, 14.6904f)
                close()
                moveTo(15.7755f, 13.3398f)
                lineTo(15.8751f, 13.348f)
                lineTo(15.8751f, 13.3479f)
                lineTo(15.7755f, 13.3398f)
                close()
                moveTo(15.8282f, 11.2275f)
                lineTo(15.9282f, 11.2276f)
                verticalLineTo(11.2275f)
                horizontalLineTo(15.8282f)
                close()
                moveTo(15.9473f, 10.9395f)
                lineTo(15.8767f, 10.8687f)
                lineTo(15.8766f, 10.8687f)
                lineTo(15.9473f, 10.9395f)
                close()
                moveTo(16.2345f, 10.8213f)
                verticalLineTo(10.7213f)
                horizontalLineTo(16.2344f)
                lineTo(16.2345f, 10.8213f)
                close()
                moveTo(16.5216f, 10.9395f)
                lineTo(16.5923f, 10.8687f)
                lineTo(16.5922f, 10.8686f)
                lineTo(16.5216f, 10.9395f)
                close()
                moveTo(16.6407f, 11.2275f)
                lineTo(16.7407f, 11.2277f)
                verticalLineTo(11.2275f)
                horizontalLineTo(16.6407f)
                close()
                moveTo(16.585f, 13.4053f)
                lineTo(16.6847f, 13.4134f)
                lineTo(16.6847f, 13.4133f)
                lineTo(16.585f, 13.4053f)
                close()
                moveTo(16.1544f, 15.1572f)
                lineTo(16.0663f, 15.1098f)
                lineTo(16.0663f, 15.1099f)
                lineTo(16.1544f, 15.1572f)
                close()
                moveTo(15.7296f, 15.7285f)
                lineTo(15.8003f, 15.7992f)
                lineTo(15.8003f, 15.7992f)
                lineTo(15.7296f, 15.7285f)
                close()
                moveTo(13.8458f, 16.5381f)
                lineTo(13.8325f, 16.439f)
                lineTo(13.8324f, 16.439f)
                lineTo(13.8458f, 16.5381f)
                close()
                moveTo(10.8516f, 16.6406f)
                verticalLineTo(16.7406f)
                verticalLineTo(16.6406f)
                close()
                moveTo(7.79597f, 16.5381f)
                lineTo(7.80932f, 16.439f)
                lineTo(7.80926f, 16.439f)
                lineTo(7.79597f, 16.5381f)
                close()
                moveTo(5.91218f, 15.7285f)
                lineTo(5.98294f, 15.6578f)
                lineTo(5.98291f, 15.6578f)
                lineTo(5.91218f, 15.7285f)
                close()
                moveTo(5.14265f, 14.1025f)
                lineTo(5.24112f, 14.0851f)
                lineTo(5.2411f, 14.085f)
                lineTo(5.14265f, 14.1025f)
                close()
                moveTo(5.00203f, 11.6338f)
                lineTo(5.10203f, 11.6334f)
                lineTo(5.10203f, 11.6333f)
                lineTo(5.00203f, 11.6338f)
                close()
                moveTo(5.00008f, 10.8213f)
                lineTo(5.10008f, 10.8214f)
                verticalLineTo(10.8213f)
                horizontalLineTo(5.00008f)
                close()
                moveTo(5.00008f, 10.7891f)
                horizontalLineTo(4.90008f)
                horizontalLineTo(5.00008f)
                close()
                moveTo(5.10359f, 7.7959f)
                lineTo(5.2027f, 7.80924f)
                lineTo(5.2027f, 7.80918f)
                lineTo(5.10359f, 7.7959f)
                close()
                moveTo(5.91218f, 5.91211f)
                lineTo(5.84152f, 5.84135f)
                lineTo(5.84151f, 5.84136f)
                lineTo(5.91218f, 5.91211f)
                close()
                moveTo(7.79597f, 5.10352f)
                lineTo(7.80926f, 5.20263f)
                lineTo(7.80929f, 5.20262f)
                lineTo(7.79597f, 5.10352f)
                close()
                moveTo(14.0792f, 5.00098f)
                verticalLineTo(4.90098f)
                verticalLineTo(5.00098f)
                close()
                moveTo(16.2657f, 5.37598f)
                lineTo(16.3364f, 5.30528f)
                lineTo(16.3364f, 5.30527f)
                lineTo(16.2657f, 5.37598f)
                close()
                moveTo(16.6407f, 7.56152f)
                horizontalLineTo(16.7407f)
                horizontalLineTo(16.6407f)
                close()
                moveTo(16.2657f, 9.74805f)
                lineTo(16.3364f, 9.81876f)
                lineTo(16.3364f, 9.81876f)
                lineTo(16.2657f, 9.74805f)
                close()
                moveTo(14.0792f, 10.123f)
                verticalLineTo(10.223f)
                verticalLineTo(10.123f)
                close()
                moveTo(11.8936f, 9.74805f)
                lineTo(11.8229f, 9.81876f)
                lineTo(11.8229f, 9.81877f)
                lineTo(11.8936f, 9.74805f)
                close()
                moveTo(11.5186f, 7.56152f)
                horizontalLineTo(11.4186f)
                horizontalLineTo(11.5186f)
                close()
                moveTo(11.8936f, 5.37598f)
                lineTo(11.8229f, 5.30526f)
                lineTo(11.8229f, 5.30527f)
                lineTo(11.8936f, 5.37598f)
                close()
                moveTo(14.0792f, 5.93262f)
                lineTo(14.0792f, 5.83262f)
                horizontalLineTo(14.0791f)
                lineTo(14.0792f, 5.93262f)
                close()
                moveTo(12.9151f, 7.09668f)
                lineTo(12.8151f, 7.09668f)
                verticalLineTo(7.09671f)
                lineTo(12.9151f, 7.09668f)
                close()
                moveTo(12.963f, 7.42773f)
                lineTo(13.0079f, 7.51706f)
                lineTo(13.0826f, 7.47948f)
                lineTo(13.0588f, 7.39932f)
                lineTo(12.963f, 7.42773f)
                close()
                moveTo(12.4493f, 8.26074f)
                lineTo(12.3493f, 8.26074f)
                verticalLineTo(8.2608f)
                lineTo(12.4493f, 8.26074f)
                close()
                moveTo(13.3809f, 9.19141f)
                lineTo(13.3809f, 9.29141f)
                horizontalLineTo(13.3809f)
                verticalLineTo(9.19141f)
                close()
                moveTo(14.7774f, 9.19141f)
                verticalLineTo(9.29141f)
                verticalLineTo(9.19141f)
                close()
                moveTo(15.7091f, 8.26074f)
                lineTo(15.8091f, 8.2608f)
                verticalLineTo(8.26074f)
                lineTo(15.7091f, 8.26074f)
                close()
                moveTo(15.1934f, 7.42773f)
                lineTo(15.0976f, 7.39929f)
                lineTo(15.0737f, 7.47964f)
                lineTo(15.1487f, 7.51716f)
                lineTo(15.1934f, 7.42773f)
                close()
                moveTo(15.2432f, 7.09668f)
                lineTo(15.3432f, 7.09671f)
                verticalLineTo(7.09668f)
                lineTo(15.2432f, 7.09668f)
                close()
                moveTo(14.0792f, 6.39844f)
                lineTo(14.0792f, 6.29844f)
                horizontalLineTo(14.0791f)
                lineTo(14.0792f, 6.39844f)
                close()
                moveTo(14.7774f, 7.09668f)
                lineTo(14.8774f, 7.09674f)
                verticalLineTo(7.09668f)
                lineTo(14.7774f, 7.09668f)
                close()
                moveTo(14.7345f, 7.3291f)
                verticalLineTo(7.4291f)
                horizontalLineTo(14.8051f)
                lineTo(14.8287f, 7.36254f)
                lineTo(14.7345f, 7.3291f)
                close()
                moveTo(13.4229f, 7.3291f)
                lineTo(13.3287f, 7.36253f)
                lineTo(13.3523f, 7.4291f)
                horizontalLineTo(13.4229f)
                verticalLineTo(7.3291f)
                close()
                moveTo(13.38f, 7.09668f)
                lineTo(13.28f, 7.09668f)
                verticalLineTo(7.09674f)
                lineTo(13.38f, 7.09668f)
                close()
                moveTo(10.4141f, 5f)
                verticalLineTo(5.1f)
                curveTo(10.495f, 5.1f, 10.573f, 5.13244f, 10.6306f, 5.18991f)
                lineTo(10.7012f, 5.11914f)
                lineTo(10.7719f, 5.04837f)
                curveTo(10.6771f, 4.95379f, 10.5485f, 4.9f, 10.4141f, 4.9f)
                verticalLineTo(5f)
                close()
                moveTo(10.7012f, 5.11914f)
                lineTo(10.6305f, 5.18985f)
                curveTo(10.688f, 5.24731f, 10.7203f, 5.32521f, 10.7204f, 5.4063f)
                lineTo(10.8204f, 5.40625f)
                lineTo(10.9204f, 5.4062f)
                curveTo(10.9203f, 5.27194f, 10.8668f, 5.14325f, 10.772f, 5.04843f)
                lineTo(10.7012f, 5.11914f)
                close()
                moveTo(10.8204f, 5.40625f)
                horizontalLineTo(10.7204f)
                curveTo(10.7204f, 5.48734f, 10.6881f, 5.56512f, 10.6305f, 5.62265f)
                lineTo(10.7012f, 5.69336f)
                lineTo(10.772f, 5.76407f)
                curveTo(10.8668f, 5.66923f, 10.9204f, 5.54065f, 10.9204f, 5.40625f)
                horizontalLineTo(10.8204f)
                close()
                moveTo(10.7012f, 5.69336f)
                lineTo(10.6306f, 5.62263f)
                curveTo(10.573f, 5.68015f, 10.4952f, 5.7125f, 10.4141f, 5.7125f)
                verticalLineTo(5.8125f)
                verticalLineTo(5.9125f)
                curveTo(10.5485f, 5.9125f, 10.6771f, 5.85887f, 10.7719f, 5.76408f)
                lineTo(10.7012f, 5.69336f)
                close()
                moveTo(10.4141f, 5.8125f)
                lineTo(10.414f, 5.7125f)
                curveTo(9.33635f, 5.71366f, 8.52682f, 5.72271f, 7.89106f, 5.80812f)
                lineTo(7.90437f, 5.90723f)
                lineTo(7.91769f, 6.00634f)
                curveTo(8.53775f, 5.92304f, 9.3332f, 5.91367f, 10.4142f, 5.9125f)
                lineTo(10.4141f, 5.8125f)
                close()
                moveTo(7.90437f, 5.90723f)
                lineTo(7.89104f, 5.80812f)
                curveTo(7.18781f, 5.90275f, 6.74579f, 6.08553f, 6.41569f, 6.41562f)
                lineTo(6.4864f, 6.48633f)
                lineTo(6.55711f, 6.55704f)
                curveTo(6.84397f, 6.2702f, 7.23687f, 6.09795f, 7.91771f, 6.00633f)
                lineTo(7.90437f, 5.90723f)
                close()
                moveTo(6.4864f, 6.48633f)
                lineTo(6.41569f, 6.41562f)
                curveTo(6.08556f, 6.74579f, 5.90377f, 7.18842f, 5.80917f, 7.89195f)
                lineTo(5.90828f, 7.90527f)
                lineTo(6.00739f, 7.9186f)
                curveTo(6.09902f, 7.23709f, 6.27041f, 6.84378f, 6.55712f, 6.55703f)
                lineTo(6.4864f, 6.48633f)
                close()
                moveTo(5.90828f, 7.90527f)
                lineTo(5.80917f, 7.89193f)
                curveTo(5.71323f, 8.60435f, 5.71258f, 9.53692f, 5.71258f, 10.8213f)
                horizontalLineTo(5.81258f)
                horizontalLineTo(5.91258f)
                curveTo(5.91258f, 9.5312f, 5.91366f, 8.61458f, 6.00738f, 7.91862f)
                lineTo(5.90828f, 7.90527f)
                close()
                moveTo(5.81258f, 10.8213f)
                horizontalLineTo(5.71258f)
                verticalLineTo(11.2773f)
                horizontalLineTo(5.81258f)
                horizontalLineTo(5.91258f)
                verticalLineTo(10.8213f)
                horizontalLineTo(5.81258f)
                close()
                moveTo(5.81258f, 11.2773f)
                lineTo(5.87838f, 11.3526f)
                lineTo(6.42037f, 10.879f)
                lineTo(6.35457f, 10.8037f)
                lineTo(6.28876f, 10.7284f)
                lineTo(5.74677f, 11.202f)
                lineTo(5.81258f, 11.2773f)
                close()
                moveTo(6.35457f, 10.8037f)
                lineTo(6.42041f, 10.879f)
                curveTo(6.63903f, 10.6877f, 6.92233f, 10.5865f, 7.21261f, 10.596f)
                lineTo(7.2159f, 10.4961f)
                lineTo(7.21918f, 10.3961f)
                curveTo(6.87819f, 10.3849f, 6.54547f, 10.5038f, 6.28872f, 10.7284f)
                lineTo(6.35457f, 10.8037f)
                close()
                moveTo(7.2159f, 10.4961f)
                lineTo(7.21258f, 10.596f)
                curveTo(7.50313f, 10.6057f, 7.77968f, 10.7249f, 7.98505f, 10.9301f)
                lineTo(8.05574f, 10.8594f)
                lineTo(8.12642f, 10.7886f)
                curveTo(7.88491f, 10.5473f, 7.5601f, 10.4075f, 7.21921f, 10.3961f)
                lineTo(7.2159f, 10.4961f)
                close()
                moveTo(8.05574f, 10.8594f)
                lineTo(7.98503f, 10.9301f)
                lineTo(10.3073f, 13.2524f)
                lineTo(10.378f, 13.1816f)
                lineTo(10.4487f, 13.1109f)
                lineTo(8.12645f, 10.7887f)
                lineTo(8.05574f, 10.8594f)
                close()
                moveTo(10.378f, 13.1816f)
                lineTo(10.3073f, 13.2523f)
                curveTo(10.504f, 13.4492f, 10.7638f, 13.5707f, 11.0412f, 13.5947f)
                lineTo(11.0499f, 13.4951f)
                lineTo(11.0585f, 13.3955f)
                curveTo(10.8281f, 13.3755f, 10.6123f, 13.2746f, 10.4487f, 13.1109f)
                lineTo(10.378f, 13.1816f)
                close()
                moveTo(11.0499f, 13.4951f)
                lineTo(11.0412f, 13.5947f)
                curveTo(11.3185f, 13.6188f, 11.5954f, 13.5446f, 11.8232f, 13.3846f)
                lineTo(11.7657f, 13.3027f)
                lineTo(11.7082f, 13.2209f)
                curveTo(11.519f, 13.3538f, 11.289f, 13.4155f, 11.0585f, 13.3955f)
                lineTo(11.0499f, 13.4951f)
                close()
                moveTo(11.7657f, 13.3027f)
                lineTo(11.823f, 13.3847f)
                lineTo(11.9851f, 13.2714f)
                lineTo(11.9278f, 13.1895f)
                lineTo(11.8705f, 13.1075f)
                lineTo(11.7084f, 13.2208f)
                lineTo(11.7657f, 13.3027f)
                close()
                moveTo(11.9278f, 13.1895f)
                lineTo(11.9853f, 13.2713f)
                curveTo(12.2677f, 13.0728f, 12.6088f, 12.9756f, 12.9531f, 12.9963f)
                lineTo(12.9591f, 12.8965f)
                lineTo(12.9651f, 12.7967f)
                curveTo(12.5754f, 12.7732f, 12.1895f, 12.8833f, 11.8703f, 13.1076f)
                lineTo(11.9278f, 13.1895f)
                close()
                moveTo(12.9591f, 12.8965f)
                lineTo(12.953f, 12.9963f)
                curveTo(13.2975f, 13.017f, 13.625f, 13.1541f, 13.8814f, 13.3849f)
                lineTo(13.9483f, 13.3105f)
                lineTo(14.0152f, 13.2362f)
                curveTo(13.7251f, 12.9751f, 13.3546f, 12.8201f, 12.9651f, 12.7967f)
                lineTo(12.9591f, 12.8965f)
                close()
                moveTo(13.9483f, 13.3105f)
                lineTo(13.8814f, 13.3849f)
                lineTo(15.4136f, 14.7647f)
                lineTo(15.4805f, 14.6904f)
                lineTo(15.5475f, 14.6161f)
                lineTo(14.0152f, 13.2362f)
                lineTo(13.9483f, 13.3105f)
                close()
                moveTo(15.4805f, 14.6904f)
                lineTo(15.5708f, 14.7334f)
                curveTo(15.7327f, 14.3931f, 15.8259f, 13.9533f, 15.8751f, 13.348f)
                lineTo(15.7755f, 13.3398f)
                lineTo(15.6758f, 13.3317f)
                curveTo(15.6273f, 13.9276f, 15.5368f, 14.3394f, 15.3902f, 14.6475f)
                lineTo(15.4805f, 14.6904f)
                close()
                moveTo(15.7755f, 13.3398f)
                lineTo(15.8751f, 13.3479f)
                curveTo(15.9215f, 12.7777f, 15.9276f, 12.087f, 15.9282f, 11.2276f)
                lineTo(15.8282f, 11.2275f)
                lineTo(15.7282f, 11.2275f)
                curveTo(15.7276f, 12.0881f, 15.7214f, 12.7708f, 15.6758f, 13.3317f)
                lineTo(15.7755f, 13.3398f)
                close()
                moveTo(15.8282f, 11.2275f)
                horizontalLineTo(15.9282f)
                curveTo(15.9282f, 11.1462f, 15.9607f, 11.0675f, 16.0181f, 11.0102f)
                lineTo(15.9473f, 10.9395f)
                lineTo(15.8766f, 10.8687f)
                curveTo(15.7816f, 10.9638f, 15.7282f, 11.0934f, 15.7282f, 11.2275f)
                horizontalLineTo(15.8282f)
                close()
                moveTo(15.9473f, 10.9395f)
                lineTo(16.018f, 11.0102f)
                curveTo(16.075f, 10.9532f, 16.1529f, 10.9213f, 16.2345f, 10.9213f)
                lineTo(16.2345f, 10.8213f)
                lineTo(16.2344f, 10.7213f)
                curveTo(16.1008f, 10.7214f, 15.9719f, 10.7736f, 15.8767f, 10.8687f)
                lineTo(15.9473f, 10.9395f)
                close()
                moveTo(16.2345f, 10.8213f)
                verticalLineTo(10.9213f)
                curveTo(16.3159f, 10.9213f, 16.3938f, 10.9533f, 16.4509f, 11.0103f)
                lineTo(16.5216f, 10.9395f)
                lineTo(16.5922f, 10.8686f)
                curveTo(16.497f, 10.7738f, 16.3682f, 10.7213f, 16.2345f, 10.7213f)
                verticalLineTo(10.8213f)
                close()
                moveTo(16.5216f, 10.9395f)
                lineTo(16.4509f, 11.0102f)
                curveTo(16.5082f, 11.0675f, 16.5407f, 11.1462f, 16.5407f, 11.2275f)
                horizontalLineTo(16.6407f)
                horizontalLineTo(16.7407f)
                curveTo(16.7407f, 11.0934f, 16.6873f, 10.9638f, 16.5923f, 10.8687f)
                lineTo(16.5216f, 10.9395f)
                close()
                moveTo(16.6407f, 11.2275f)
                lineTo(16.5407f, 11.2274f)
                curveTo(16.5395f, 12.0833f, 16.5333f, 12.8054f, 16.4854f, 13.3972f)
                lineTo(16.585f, 13.4053f)
                lineTo(16.6847f, 13.4133f)
                curveTo(16.7334f, 12.8123f, 16.7395f, 12.0823f, 16.7407f, 11.2277f)
                lineTo(16.6407f, 11.2275f)
                close()
                moveTo(16.585f, 13.4053f)
                lineTo(16.4854f, 13.3971f)
                curveTo(16.4293f, 14.0848f, 16.315f, 14.6478f, 16.0663f, 15.1098f)
                lineTo(16.1544f, 15.1572f)
                lineTo(16.2424f, 15.2046f)
                curveTo(16.5106f, 14.7064f, 16.6278f, 14.1109f, 16.6847f, 13.4134f)
                lineTo(16.585f, 13.4053f)
                close()
                moveTo(16.1544f, 15.1572f)
                lineTo(16.0663f, 15.1099f)
                curveTo(15.9581f, 15.3114f, 15.8206f, 15.4959f, 15.6588f, 15.6578f)
                lineTo(15.7296f, 15.7285f)
                lineTo(15.8003f, 15.7992f)
                curveTo(15.9757f, 15.6236f, 16.125f, 15.4234f, 16.2425f, 15.2045f)
                lineTo(16.1544f, 15.1572f)
                close()
                moveTo(15.7296f, 15.7285f)
                lineTo(15.6589f, 15.6578f)
                curveTo(15.1957f, 16.1209f, 14.6034f, 16.3357f, 13.8325f, 16.439f)
                lineTo(13.8458f, 16.5381f)
                lineTo(13.8591f, 16.6372f)
                curveTo(14.6524f, 16.5309f, 15.2939f, 16.3056f, 15.8003f, 15.7992f)
                lineTo(15.7296f, 15.7285f)
                close()
                moveTo(13.8458f, 16.5381f)
                lineTo(13.8324f, 16.439f)
                curveTo(13.0765f, 16.5409f, 12.104f, 16.5406f, 10.8516f, 16.5406f)
                verticalLineTo(16.6406f)
                verticalLineTo(16.7406f)
                curveTo(12.0985f, 16.7406f, 13.0867f, 16.7413f, 13.8591f, 16.6372f)
                lineTo(13.8458f, 16.5381f)
                close()
                moveTo(10.8516f, 16.6406f)
                verticalLineTo(16.5406f)
                horizontalLineTo(10.7901f)
                verticalLineTo(16.6406f)
                verticalLineTo(16.7406f)
                horizontalLineTo(10.8516f)
                verticalLineTo(16.6406f)
                close()
                moveTo(10.7901f, 16.6406f)
                verticalLineTo(16.5406f)
                curveTo(9.53723f, 16.5406f, 8.56588f, 16.5409f, 7.80932f, 16.439f)
                lineTo(7.79597f, 16.5381f)
                lineTo(7.78263f, 16.6372f)
                curveTo(8.5556f, 16.7413f, 9.54272f, 16.7406f, 10.7901f, 16.7406f)
                verticalLineTo(16.6406f)
                close()
                moveTo(7.79597f, 16.5381f)
                lineTo(7.80926f, 16.439f)
                curveTo(7.03872f, 16.3357f, 6.4455f, 16.1209f, 5.98294f, 15.6578f)
                lineTo(5.91218f, 15.7285f)
                lineTo(5.84143f, 15.7992f)
                curveTo(6.34737f, 16.3057f, 6.9899f, 16.5309f, 7.78269f, 16.6372f)
                lineTo(7.79597f, 16.5381f)
                close()
                moveTo(5.91218f, 15.7285f)
                lineTo(5.98291f, 15.6578f)
                curveTo(5.57265f, 15.2474f, 5.3564f, 14.7349f, 5.24112f, 14.0851f)
                lineTo(5.14265f, 14.1025f)
                lineTo(5.04419f, 14.12f)
                curveTo(5.16401f, 14.7954f, 5.39319f, 15.3508f, 5.84146f, 15.7992f)
                lineTo(5.91218f, 15.7285f)
                close()
                moveTo(5.14265f, 14.1025f)
                lineTo(5.2411f, 14.085f)
                curveTo(5.12644f, 13.4429f, 5.10611f, 12.6408f, 5.10203f, 11.6334f)
                lineTo(5.00203f, 11.6338f)
                lineTo(4.90203f, 11.6342f)
                curveTo(4.9061f, 12.6394f, 4.92605f, 13.4585f, 5.04421f, 14.1201f)
                lineTo(5.14265f, 14.1025f)
                close()
                moveTo(5.00203f, 11.6338f)
                lineTo(5.10203f, 11.6333f)
                curveTo(5.10086f, 11.3774f, 5.09969f, 11.1068f, 5.10008f, 10.8214f)
                lineTo(5.00008f, 10.8213f)
                lineTo(4.90008f, 10.8212f)
                curveTo(4.89969f, 11.1072f, 4.90087f, 11.3783f, 4.90203f, 11.6342f)
                lineTo(5.00203f, 11.6338f)
                close()
                moveTo(5.00008f, 10.8213f)
                horizontalLineTo(5.10008f)
                verticalLineTo(10.7891f)
                horizontalLineTo(5.00008f)
                horizontalLineTo(4.90008f)
                verticalLineTo(10.8213f)
                horizontalLineTo(5.00008f)
                close()
                moveTo(5.00008f, 10.7891f)
                horizontalLineTo(5.10008f)
                curveTo(5.10008f, 9.53661f, 5.10083f, 8.56571f, 5.2027f, 7.80924f)
                lineTo(5.10359f, 7.7959f)
                lineTo(5.00449f, 7.78255f)
                curveTo(4.90041f, 8.55537f, 4.90008f, 9.54187f, 4.90008f, 10.7891f)
                horizontalLineTo(5.00008f)
                close()
                moveTo(5.10359f, 7.7959f)
                lineTo(5.2027f, 7.80918f)
                curveTo(5.30599f, 7.03861f, 5.51984f, 6.44534f, 5.98285f, 5.98286f)
                lineTo(5.91218f, 5.91211f)
                lineTo(5.84151f, 5.84136f)
                curveTo(5.33497f, 6.34732f, 5.11071f, 6.99f, 5.00448f, 7.78261f)
                lineTo(5.10359f, 7.7959f)
                close()
                moveTo(5.91218f, 5.91211f)
                lineTo(5.98285f, 5.98286f)
                curveTo(6.44602f, 5.52027f, 7.03872f, 5.3059f, 7.80926f, 5.20263f)
                lineTo(7.79597f, 5.10352f)
                lineTo(7.78269f, 5.0044f)
                curveTo(6.9899f, 5.11065f, 6.34801f, 5.33549f, 5.84152f, 5.84135f)
                lineTo(5.91218f, 5.91211f)
                close()
                moveTo(7.79597f, 5.10352f)
                lineTo(7.80929f, 5.20262f)
                curveTo(8.48127f, 5.11231f, 9.34847f, 5.10117f, 10.4142f, 5.1f)
                lineTo(10.4141f, 5f)
                lineTo(10.414f, 4.9f)
                curveTo(9.35106f, 4.90116f, 8.47016f, 4.912f, 7.78265f, 5.00441f)
                lineTo(7.79597f, 5.10352f)
                close()
                moveTo(14.0792f, 5.00098f)
                verticalLineTo(5.10098f)
                curveTo(14.6855f, 5.10098f, 15.1305f, 5.10118f, 15.4711f, 5.14695f)
                curveTo(15.8089f, 5.19234f, 16.0292f, 5.28087f, 16.195f, 5.44669f)
                lineTo(16.2657f, 5.37598f)
                lineTo(16.3364f, 5.30527f)
                curveTo(16.1272f, 5.09606f, 15.8577f, 4.99709f, 15.4978f, 4.94873f)
                curveTo(15.1407f, 4.90076f, 14.6799f, 4.90098f, 14.0792f, 4.90098f)
                verticalLineTo(5.00098f)
                close()
                moveTo(16.2657f, 5.37598f)
                lineTo(16.195f, 5.44667f)
                curveTo(16.3607f, 5.6125f, 16.4493f, 5.83261f, 16.4947f, 6.17021f)
                curveTo(16.5405f, 6.51068f, 16.5407f, 6.95538f, 16.5407f, 7.56152f)
                horizontalLineTo(16.6407f)
                horizontalLineTo(16.7407f)
                curveTo(16.7407f, 6.96101f, 16.7409f, 6.50044f, 16.6929f, 6.14355f)
                curveTo(16.6445f, 5.7838f, 16.5455f, 5.51446f, 16.3364f, 5.30528f)
                lineTo(16.2657f, 5.37598f)
                close()
                moveTo(16.6407f, 7.56152f)
                horizontalLineTo(16.5407f)
                curveTo(16.5407f, 8.16787f, 16.5405f, 8.6128f, 16.4947f, 8.95347f)
                curveTo(16.4493f, 9.29128f, 16.3608f, 9.51151f, 16.195f, 9.67734f)
                lineTo(16.2657f, 9.74805f)
                lineTo(16.3364f, 9.81876f)
                curveTo(16.5456f, 9.60954f, 16.6446f, 9.34003f, 16.6929f, 8.9801f)
                curveTo(16.7409f, 8.62302f, 16.7407f, 8.16222f, 16.7407f, 7.56152f)
                horizontalLineTo(16.6407f)
                close()
                moveTo(16.2657f, 9.74805f)
                lineTo(16.195f, 9.67734f)
                curveTo(16.0292f, 9.84316f, 15.8089f, 9.93169f, 15.4711f, 9.97708f)
                curveTo(15.1305f, 10.0228f, 14.6855f, 10.023f, 14.0792f, 10.023f)
                verticalLineTo(10.123f)
                verticalLineTo(10.223f)
                curveTo(14.6799f, 10.223f, 15.1407f, 10.2233f, 15.4978f, 10.1753f)
                curveTo(15.8577f, 10.1269f, 16.1272f, 10.028f, 16.3364f, 9.81876f)
                lineTo(16.2657f, 9.74805f)
                close()
                moveTo(14.0792f, 10.123f)
                verticalLineTo(10.023f)
                curveTo(13.473f, 10.023f, 13.0283f, 10.0228f, 12.6879f, 9.97701f)
                curveTo(12.3503f, 9.93161f, 12.1302f, 9.84308f, 11.9643f, 9.67732f)
                lineTo(11.8936f, 9.74805f)
                lineTo(11.8229f, 9.81877f)
                curveTo(12.0321f, 10.0279f, 12.3014f, 10.1268f, 12.6612f, 10.1752f)
                curveTo(13.0181f, 10.2232f, 13.4787f, 10.223f, 14.0792f, 10.223f)
                verticalLineTo(10.123f)
                close()
                moveTo(11.8936f, 9.74805f)
                lineTo(11.9643f, 9.67734f)
                curveTo(11.7985f, 9.51151f, 11.71f, 9.29128f, 11.6646f, 8.95348f)
                curveTo(11.6188f, 8.61281f, 11.6186f, 8.16788f, 11.6186f, 7.56152f)
                horizontalLineTo(11.5186f)
                horizontalLineTo(11.4186f)
                curveTo(11.4186f, 8.16223f, 11.4184f, 8.62303f, 11.4664f, 8.98011f)
                curveTo(11.5147f, 9.34004f, 11.6137f, 9.60954f, 11.8229f, 9.81876f)
                lineTo(11.8936f, 9.74805f)
                close()
                moveTo(11.5186f, 7.56152f)
                horizontalLineTo(11.6186f)
                curveTo(11.6186f, 6.95529f, 11.6188f, 6.51059f, 11.6646f, 6.17014f)
                curveTo(11.71f, 5.83257f, 11.7985f, 5.61251f, 11.9643f, 5.44669f)
                lineTo(11.8936f, 5.37598f)
                lineTo(11.8229f, 5.30527f)
                curveTo(11.6137f, 5.51445f, 11.5148f, 5.78375f, 11.4664f, 6.14349f)
                curveTo(11.4184f, 6.50037f, 11.4186f, 6.96094f, 11.4186f, 7.56152f)
                horizontalLineTo(11.5186f)
                close()
                moveTo(11.8936f, 5.37598f)
                lineTo(11.9643f, 5.44669f)
                curveTo(12.1302f, 5.28088f, 12.3502f, 5.19235f, 12.6878f, 5.14697f)
                curveTo(13.0282f, 5.10119f, 13.4729f, 5.10098f, 14.0792f, 5.10098f)
                verticalLineTo(5.00098f)
                verticalLineTo(4.90098f)
                curveTo(13.4786f, 4.90098f, 13.018f, 4.90077f, 12.6611f, 4.94875f)
                curveTo(12.3014f, 4.99712f, 12.0321f, 5.0961f, 11.8229f, 5.30526f)
                lineTo(11.8936f, 5.37598f)
                close()
                moveTo(14.0792f, 5.93262f)
                lineTo(14.0791f, 5.83262f)
                curveTo(13.3813f, 5.83286f, 12.8151f, 6.39867f, 12.8151f, 7.09668f)
                lineTo(12.9151f, 7.09668f)
                lineTo(13.0151f, 7.09668f)
                curveTo(13.0151f, 6.50919f, 13.4917f, 6.03282f, 14.0792f, 6.03262f)
                lineTo(14.0792f, 5.93262f)
                close()
                moveTo(12.9151f, 7.09668f)
                lineTo(12.8151f, 7.09671f)
                curveTo(12.8152f, 7.2214f, 12.8333f, 7.34214f, 12.8671f, 7.45615f)
                lineTo(12.963f, 7.42773f)
                lineTo(13.0588f, 7.39932f)
                curveTo(13.0305f, 7.30353f, 13.0151f, 7.20191f, 13.0151f, 7.09665f)
                lineTo(12.9151f, 7.09668f)
                close()
                moveTo(12.963f, 7.42773f)
                lineTo(12.918f, 7.3384f)
                curveTo(12.5812f, 7.50788f, 12.3493f, 7.8575f, 12.3493f, 8.26074f)
                lineTo(12.4493f, 8.26074f)
                lineTo(12.5493f, 8.26074f)
                curveTo(12.5493f, 7.93598f, 12.7359f, 7.6539f, 13.0079f, 7.51706f)
                lineTo(12.963f, 7.42773f)
                close()
                moveTo(12.4493f, 8.26074f)
                lineTo(12.3493f, 8.2608f)
                curveTo(12.3496f, 8.83002f, 12.8119f, 9.29116f, 13.3809f, 9.29141f)
                lineTo(13.3809f, 9.19141f)
                lineTo(13.381f, 9.09141f)
                curveTo(12.9221f, 9.0912f, 12.5496f, 8.71929f, 12.5493f, 8.26068f)
                lineTo(12.4493f, 8.26074f)
                close()
                moveTo(13.3809f, 9.19141f)
                verticalLineTo(9.29141f)
                horizontalLineTo(14.7774f)
                verticalLineTo(9.19141f)
                verticalLineTo(9.09141f)
                horizontalLineTo(13.3809f)
                verticalLineTo(9.19141f)
                close()
                moveTo(14.7774f, 9.19141f)
                verticalLineTo(9.29141f)
                curveTo(15.3467f, 9.2914f, 15.8087f, 8.83011f, 15.8091f, 8.2608f)
                lineTo(15.7091f, 8.26074f)
                lineTo(15.6091f, 8.26068f)
                curveTo(15.6088f, 8.71948f, 15.2364f, 9.0914f, 14.7774f, 9.09141f)
                lineTo(14.7774f, 9.19141f)
                close()
                moveTo(15.7091f, 8.26074f)
                lineTo(15.8091f, 8.26074f)
                curveTo(15.809f, 7.8566f, 15.5759f, 7.50737f, 15.2382f, 7.33831f)
                lineTo(15.1934f, 7.42773f)
                lineTo(15.1487f, 7.51716f)
                curveTo(15.4216f, 7.65378f, 15.6091f, 7.93553f, 15.6091f, 8.26074f)
                lineTo(15.7091f, 8.26074f)
                close()
                moveTo(15.1934f, 7.42773f)
                lineTo(15.2893f, 7.45618f)
                curveTo(15.3228f, 7.34318f, 15.3432f, 7.22263f, 15.3432f, 7.09671f)
                lineTo(15.2432f, 7.09668f)
                lineTo(15.1432f, 7.09665f)
                curveTo(15.1432f, 7.20092f, 15.1263f, 7.30229f, 15.0976f, 7.39929f)
                lineTo(15.1934f, 7.42773f)
                close()
                moveTo(15.2432f, 7.09668f)
                lineTo(15.3432f, 7.09668f)
                curveTo(15.3432f, 6.39866f, 14.7771f, 5.83284f, 14.0792f, 5.83262f)
                lineTo(14.0792f, 5.93262f)
                lineTo(14.0791f, 6.03262f)
                curveTo(14.6667f, 6.0328f, 15.1432f, 6.50917f, 15.1432f, 7.09668f)
                lineTo(15.2432f, 7.09668f)
                close()
                moveTo(14.0792f, 6.39844f)
                lineTo(14.0791f, 6.49844f)
                curveTo(14.4095f, 6.49861f, 14.6774f, 6.76635f, 14.6774f, 7.09668f)
                lineTo(14.7774f, 7.09668f)
                lineTo(14.8774f, 7.09668f)
                curveTo(14.8774f, 6.6558f, 14.5199f, 6.29867f, 14.0792f, 6.29844f)
                lineTo(14.0792f, 6.39844f)
                close()
                moveTo(14.7774f, 7.09668f)
                lineTo(14.6774f, 7.09662f)
                curveTo(14.6774f, 7.16443f, 14.6632f, 7.23071f, 14.6402f, 7.29567f)
                lineTo(14.7345f, 7.3291f)
                lineTo(14.8287f, 7.36254f)
                curveTo(14.8574f, 7.28166f, 14.8774f, 7.19248f, 14.8774f, 7.09674f)
                lineTo(14.7774f, 7.09668f)
                close()
                moveTo(14.7345f, 7.3291f)
                verticalLineTo(7.2291f)
                horizontalLineTo(13.4229f)
                verticalLineTo(7.3291f)
                verticalLineTo(7.4291f)
                horizontalLineTo(14.7345f)
                verticalLineTo(7.3291f)
                close()
                moveTo(13.4229f, 7.3291f)
                lineTo(13.5172f, 7.29568f)
                curveTo(13.4941f, 7.23072f, 13.48f, 7.1644f, 13.48f, 7.09662f)
                lineTo(13.38f, 7.09668f)
                lineTo(13.28f, 7.09674f)
                curveTo(13.28f, 7.19246f, 13.3f, 7.28168f, 13.3287f, 7.36253f)
                lineTo(13.4229f, 7.3291f)
                close()
                moveTo(13.38f, 7.09668f)
                lineTo(13.48f, 7.09668f)
                curveTo(13.48f, 6.76656f, 13.7486f, 6.49863f, 14.0792f, 6.49844f)
                lineTo(14.0792f, 6.39844f)
                lineTo(14.0791f, 6.29844f)
                curveTo(13.6386f, 6.2987f, 13.28f, 6.65562f, 13.28f, 7.09668f)
                lineTo(13.38f, 7.09668f)
                close()
            }
        }.build()
        
        return _packsPrivatePhoto!!
    }

private var _packsPrivatePhoto: ImageVector? = null

