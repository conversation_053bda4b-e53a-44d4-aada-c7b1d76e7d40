package com.flutterup.app.design.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.flutterup.app.R
import com.flutterup.app.design.haze.DefaultHazeStyle
import com.flutterup.app.design.preview.BooleanProvider
import com.flutterup.app.design.theme.AppTheme
import dev.chrisbanes.haze.hazeEffect


@Composable
fun AppAvatar(
    modifier: Modifier = Modifier,
    online: Boolean = false,
    nearby: Boolean = false,
    onlineSize: Dp = 6.dp,
    content: @Composable BoxScope.() -> Unit,
) {
    Box(modifier) {
        content()

        if (online) {
            Margin(
                paddingValues = PaddingValues(top = 2.dp, end = 2.dp),
                modifier = Modifier.align(Alignment.TopEnd)
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_online_dot),
                    contentDescription = null,
                    tint = Color.Unspecified,
                    modifier = Modifier
                        .size(onlineSize)
                        .border(1.dp, Color.White, shape = CircleShape)
                )
            }
        }

        if (nearby) {
            Image(
                painter = painterResource(R.drawable.ic_nearby),
                contentDescription = null,
                modifier = Modifier
                    .size(8.dp)
                    .align(Alignment.BottomEnd)
            )
        }
    }
}

@Preview
@Composable
private fun AppOnlineAvatarPreview(
    @PreviewParameter(BooleanProvider::class) online: Boolean,
) {
    AppTheme {
        AppAvatar(
            online = online,
            nearby = true
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_app_logo),
                contentDescription = null,
                modifier = Modifier
                    .border(1.dp, Color.White, shape = CircleShape)
                    .size(32.dp)
                    .clip(CircleShape)
                    .hazeEffect(DefaultHazeStyle) {
                        blurEnabled = true
                    }
            )
        }
    }
}