package com.flutterup.app.design.component.cardstack

import androidx.compose.ui.geometry.Offset
import kotlin.math.pow

internal object Transformations {

    fun calculateTranslation(
        index: Int,
        direction: Direction,
        visibleCount: Int,
        stackElevationPx: Float
    ): Offset {
        var translationX = 0f
        var translationY = 0f
        val translationIndex = if (index <= visibleCount) index else visibleCount
        when (direction) {
            Direction.Top -> {
                translationY = -stackElevationPx * translationIndex
            }

            Direction.Bottom -> {
                translationY = stackElevationPx * translationIndex
            }

            Direction.Left -> {
                translationX = -stackElevationPx * translationIndex
                translationY = 0f
            }

            Direction.Right -> {
                translationX = stackElevationPx * translationIndex
            }

            Direction.TopAndLeft -> {
                translationX = -stackElevationPx * translationIndex
                translationY = -stackElevationPx * translationIndex
            }

            Direction.TopAndRight -> {
                translationX = stackElevationPx * translationIndex
                translationY = -stackElevationPx * translationIndex
            }

            Direction.BottomAndLeft -> {
                translationX = -stackElevationPx * translationIndex
                translationY = stackElevationPx * translationIndex
            }

            Direction.BottomAndRight -> {
                translationX = stackElevationPx * translationIndex
                translationY = stackElevationPx * translationIndex
            }

            else -> {
                translationX = 0f
                translationY = 0f
            }

        }
        return Offset(translationX, translationY)
    }

    fun calculateScale(
        index: Int,
        visibleCount: Int,
        direction: Direction,
        scaleInterval: Float
    ): Offset {
        val scaleIndex = if (index <= visibleCount) index else visibleCount
        return when (direction) {
            Direction.Top, Direction.Bottom -> Offset(scaleInterval.pow(scaleIndex), 1f)
            Direction.Left, Direction.Right -> Offset(1f, scaleInterval.pow(scaleIndex))
            else -> Offset(1f, 1f)
        }
    }

    fun calculateAlpha(
        index: Int,
        alphaInterval: Float
    ): Float {
        return 1 - (alphaInterval * index)
    }
}