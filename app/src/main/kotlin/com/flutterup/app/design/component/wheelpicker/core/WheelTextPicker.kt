package com.flutterup.app.design.component.wheelpicker.core

import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp

@Composable
fun WheelTextPicker(
    modifier: Modifier = Modifier,
    startIndex: Int = 0,
    size: DpSize = DpSize(128.dp, 128.dp),
    texts: List<String>,
    rowCount: Int,
    style: TextStyle = MaterialTheme.typography.titleMedium,
    color: Color = LocalContentColor.current,
    selectorProperties: SelectorProperties = WheelPickerDefaults.selectorProperties(),
    selectorDividers: SelectorDividers = WheelPickerDefaults.selectorDividers(),
    onScrollFinished: (snappedIndex: Int) -> Int? = { null },
    content:  @Composable LazyItemScope.(
        index: Int,
        text: String,
        textStyle: TextStyle,
        textColor: Color
    ) -> Unit = { index, text, textStyle, textColor ->
        WheelDefaultTextItem(text, textStyle, textColor)
    },
) {
    WheelPicker(
        modifier = modifier,
        startIndex = startIndex,
        size = size,
        count = texts.size,
        rowCount = rowCount,
        selectorProperties = selectorProperties,
        selectorDividers = selectorDividers,
        onScrollFinished = onScrollFinished
    ){ index ->
        content(index, texts[index], style, color)
    }
}

@Composable
internal fun WheelDefaultTextItem(text: String, textStyle: TextStyle, textColor: Color) {
    Text(
        text = text,
        style = textStyle,
        color = textColor,
        maxLines = 1
    )
}
