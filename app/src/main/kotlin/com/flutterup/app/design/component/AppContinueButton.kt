package com.flutterup.app.design.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ButtonElevation
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.theme.AppButtonPrimaryColors
import com.flutterup.app.design.theme.AppTheme

@Composable
fun AppContinueButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    text: String = stringResource(R.string.continue_text),
    isLoading: Boolean = false,
    enabled: Boolean = true,
    shape: RoundedCornerShape = RoundedCornerShape(25.dp),
    colors: ButtonColors = AppButtonPrimaryColors,
    elevation: ButtonElevation? = ButtonDefaults.buttonElevation(),
    contentPadding: PaddingValues = ButtonDefaults.ContentPadding,
    loadingContent: @Composable () -> Unit = {
        CircularProgressIndicator(
            color = Color.White,
            modifier = Modifier.size(20.dp)
        )
    },
    content: @Composable () -> Unit = {
        Text(
            text = text,
            style = TextStyle(
                fontSize = 16.sp,
                lineHeight = 22.sp,
                fontWeight = FontWeight(500),
                color = Color.White,
                textAlign = TextAlign.Center,
            )
        )
    },
) {
    Button(
        modifier = modifier,
        onClick = { if (!isLoading) onClick() }, //loading 时禁用点击
        enabled = enabled,
        shape = shape,
        colors = colors,
        elevation = elevation,
        contentPadding = contentPadding,
    ) {
        if (!isLoading) {
            content()
        }

        if (isLoading) {
            loadingContent()
        }
    }
}


@Preview
@Composable
private fun AppContinueButtonPreview() {
    AppTheme {
        Column {
            AppContinueButton(
                onClick = {},
                isLoading = true,
                modifier = Modifier.fillMaxWidth().height(50.dp),
            )

            Spacer(modifier = Modifier.height(20.dp))

            AppContinueButton(
                onClick = {},
                modifier = Modifier.fillMaxWidth().height(50.dp),
                enabled = false,
            )
        }
    }
}