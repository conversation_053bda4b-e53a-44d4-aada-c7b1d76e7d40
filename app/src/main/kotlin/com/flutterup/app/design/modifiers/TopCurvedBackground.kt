package com.flutterup.app.design.modifiers

import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * 顶部凹陷背景
 *
 * @param backgroundColor 背景颜色
 * @param curveDepth 凹陷深度
 * @param cornerRadius 圆角半径
 * @param curveCornerRadius 凹陷处的圆角半径
 */
fun Modifier.topCurvedBackground(
    backgroundColor: Color = Color.White,
    curveDepth: Dp = 20.dp,
    cornerRadius: Dp = 16.dp,
    curveCornerRadius: Dp = 8.dp  // 凹陷处的圆角半径
) = this.drawBehind {
    val width = size.width
    val height = size.height
    val curveDepthPx = curveDepth.toPx()
    val cornerRadiusPx = cornerRadius.toPx()
    val curveCornerRadiusPx = curveCornerRadius.toPx()

    val path = Path().apply {
        moveTo(0f, cornerRadiusPx)

        // 左上圆角
        arcTo(
            rect = Rect(0f, 0f, cornerRadiusPx * 2, cornerRadiusPx * 2),
            startAngleDegrees = 180f,
            sweepAngleDegrees = 90f,
            forceMoveTo = false
        )

        // 到凹陷起始点前的位置
        lineTo(width * 0.42f - curveCornerRadiusPx, 0f)

        // 凹陷起始点的圆角过渡
        quadraticTo(
            width * 0.42f, 0f,
            width * 0.42f + curveCornerRadiusPx, curveCornerRadiusPx * 0.5f
        )

        // 主凹陷曲线（更平滑的过渡）
        quadraticTo(
            width * 0.5f, curveDepthPx,
            width * 0.58f - curveCornerRadiusPx, curveCornerRadiusPx * 0.5f
        )

        // 凹陷结束点的圆角过渡
        quadraticTo(
            width * 0.58f, 0f,
            width * 0.58f + curveCornerRadiusPx, 0f
        )

        // 到右上角
        lineTo(width - cornerRadiusPx, 0f)

        // 右上圆角
        arcTo(
            rect = Rect(
                width - cornerRadiusPx * 2, 0f,
                width, cornerRadiusPx * 2
            ),
            startAngleDegrees = 270f,
            sweepAngleDegrees = 90f,
            forceMoveTo = false
        )

        lineTo(width, height)
        lineTo(0f, height)
        close()
    }

    drawPath(path, backgroundColor)
}
