package com.flutterup.app.design.component

import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp

@Composable
fun AppTitleText(
    text: String,
    fontSize: TextUnit = 17.sp,
    lineHeight: TextUnit = 21.sp,
    fontWeight: FontWeight = FontWeight.W700,
    color: Color = Color.Unspecified,
    textAlign: TextAlign = TextAlign.Center,
) {
    Text(
        text = text,
        style = TextStyle(
            fontSize = fontSize,
            lineHeight = lineHeight,
            fontWeight = fontWeight,
            color = color,
            textAlign = textAlign,
        )
    )
}