package com.flutterup.app.design.component

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.modifiers.noRippleSelectable
import com.flutterup.app.design.preview.BooleanProvider
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.BackgroundPrimary
import com.flutterup.app.design.theme.BackgroundSecondary
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.design.theme.TextBlack666


/**
 * @see [AppRadioButton] 文字版
 */
@Composable
fun AppTextRadioButton(
    selected: Boolean,
    text: String,
    modifier: Modifier = Modifier,
    iconSize: DpSize = DpSize(14.dp, 14.dp),
    colors: AppTextRadioButtonColors = AppTextRadioButtonDefaults.colors(),
    shape: Shape = DEFAULT_SHAPE,
    containerPaddingValues: PaddingValues = DEFAULT_PADDING_VALUES,
    onSelectedChange: () -> Unit,
) {
    val borderModifier = if (selected) {
        Modifier.border(1.dp, colors.selectedOutlineColor, shape)
    } else {
        if (colors.outlineColor == Color.Unspecified) {
            Modifier
        } else {
            Modifier.border(1.dp, colors.outlineColor, shape)
        }
    }

    Box(
        modifier = modifier
            .then(borderModifier)
            .clip(shape)
            .background(if (selected) colors.selectedBackgroundColor else colors.backgroundColor)
            .noRippleSelectable(
                selected = selected,
                role = Role.RadioButton,
                onClick = onSelectedChange
            )
    ) {
        Row(
            modifier = Modifier
                .matchParentSize()
                .padding(containerPaddingValues),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = text,
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight.W400,
                    color = if (selected) colors.selectedTextColor else colors.textColor,
                )
            )

            Icon(
                painter = painterResource(id = if (selected) R.drawable.ic_radio_button_selected else R.drawable.ic_radio_button_unselected),
                contentDescription = null,
                tint = if (selected) colors.selectedTintColor else colors.tintColor,
                modifier = Modifier.size(iconSize)
            )
        }
    }
}

object AppTextRadioButtonDefaults {
    @Composable fun colors() = defaultColors

    @Composable fun colors(
        backgroundColor: Color = defaultColors.backgroundColor,
        selectedBackgroundColor: Color = defaultColors.selectedBackgroundColor,
        selectedTextColor: Color = defaultColors.selectedOutlineColor,
        outlineColor: Color = defaultColors.outlineColor,
        selectedOutlineColor: Color = defaultColors.selectedOutlineColor,
        textColor: Color = defaultColors.textColor,
        tintColor: Color = defaultColors.tintColor,
        selectedTintColor: Color = defaultColors.selectedTintColor,
    ) = defaultColors.copy(
        backgroundColor = backgroundColor,
        selectedBackgroundColor = selectedBackgroundColor,
        selectedTextColor = selectedTextColor,
        outlineColor = outlineColor,
        selectedOutlineColor = selectedOutlineColor,
        textColor = textColor,
        tintColor = tintColor,
        selectedTintColor = selectedTintColor,
    )


    private val defaultColors = AppTextRadioButtonColors(
        backgroundColor = BackgroundPrimary,
        selectedBackgroundColor = BackgroundSecondary,
        outlineColor = Color.Unspecified,
        selectedOutlineColor = PurplePrimary,
        textColor = TextBlack666,
        selectedTextColor = PurplePrimary,
        tintColor = Color.Unspecified,
        selectedTintColor = Color.Unspecified,
    )
}

@Immutable
class AppTextRadioButtonColors
internal constructor(
    val backgroundColor: Color,
    val selectedBackgroundColor: Color,

    val outlineColor: Color,
    val selectedOutlineColor: Color,

    val textColor: Color,
    val selectedTextColor: Color,

    val tintColor: Color,
    val selectedTintColor: Color,
) {

    fun copy(
        backgroundColor: Color = this.backgroundColor,
        selectedBackgroundColor: Color = this.selectedBackgroundColor,
        outlineColor: Color = this.outlineColor,
        selectedOutlineColor: Color = this.selectedOutlineColor,
        textColor: Color = this.textColor,
        selectedTextColor: Color = this.selectedTextColor,
        tintColor: Color = this.tintColor,
        selectedTintColor: Color = this.selectedTintColor,
    ): AppTextRadioButtonColors {
        return AppTextRadioButtonColors(
            backgroundColor,
            selectedBackgroundColor,
            outlineColor,
            selectedOutlineColor,
            textColor,
            selectedTextColor,
            tintColor,
            selectedTintColor,
        )
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is AppTextRadioButtonColors) return false

        if (backgroundColor != other.backgroundColor) return false
        if (selectedBackgroundColor != other.selectedBackgroundColor) return false
        if (outlineColor != other.outlineColor) return false
        if (selectedOutlineColor != other.selectedOutlineColor) return false
        if (textColor != other.textColor) return false
        if (selectedTextColor != other.selectedTextColor) return false
        if (tintColor != other.tintColor) return false
        if (selectedTintColor != other.selectedTintColor) return false

        return true
    }

    override fun hashCode(): Int {
        var result = backgroundColor.hashCode()
        result = 31 * result + selectedBackgroundColor.hashCode()
        result = 31 * result + outlineColor.hashCode()
        result = 31 * result + selectedOutlineColor.hashCode()
        result = 31 * result + textColor.hashCode()
        result = 31 * result + selectedTextColor.hashCode()
        result = 31 * result + tintColor.hashCode()
        result = 31 * result + selectedTintColor.hashCode()
        return result
    }
}

private val DEFAULT_SHAPE = RoundedCornerShape(10.dp)
private val DEFAULT_PADDING_VALUES = PaddingValues(horizontal = 16.dp, vertical = 12.dp)


@Preview(
    showBackground = true,
    backgroundColor = 0xFFFFFFFF,
)
@Composable
private fun AppTextRadioButtonPreview(
    @PreviewParameter(BooleanProvider::class) selected: Boolean
) {
    AppTheme {
        AppTextRadioButton(
            selected = selected,
            text = "Hello World",
            modifier = Modifier.width(200.dp).height(50.dp),
            onSelectedChange = {},
        )
    }
}