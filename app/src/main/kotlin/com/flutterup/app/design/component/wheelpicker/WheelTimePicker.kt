package com.flutterup.app.design.component.wheelpicker

import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import com.flutterup.app.design.component.wheelpicker.core.DefaultWheelTimePicker
import com.flutterup.app.design.component.wheelpicker.core.SelectorProperties
import com.flutterup.app.design.component.wheelpicker.core.TimeFormat
import com.flutterup.app.design.component.wheelpicker.core.WheelDefaultTextItem
import com.flutterup.app.design.component.wheelpicker.core.WheelPickerDefaults
import java.time.LocalTime

@Composable
fun WheelTimePicker(
    modifier: Modifier = Modifier,
    startTime: LocalTime = LocalTime.now(),
    minTime: LocalTime = LocalTime.MIN,
    maxTime: LocalTime = LocalTime.MAX,
    timeFormat: TimeFormat = TimeFormat.HOUR_24,
    size: DpSize = DpSize(128.dp, 128.dp),
    rowCount: Int = 3,
    textStyle: TextStyle = MaterialTheme.typography.titleMedium,
    textColor: Color = LocalContentColor.current,
    selectorProperties: SelectorProperties = WheelPickerDefaults.selectorProperties(),
    onSnappedTime : (snappedTime: LocalTime) -> Unit = {},
    content:  @Composable LazyItemScope.(
        index: Int,
        text: String,
        textStyle: TextStyle,
        textColor: Color
    ) -> Unit = { index, text, textStyle, textColor ->
        WheelDefaultTextItem(text, textStyle, textColor)
    },
) {
    DefaultWheelTimePicker(
        modifier,
        startTime,
        minTime,
        maxTime,
        timeFormat,
        size,
        rowCount,
        textStyle,
        textColor,
        selectorProperties,
        content = content,
        onSnappedTime = { snappedTime, _ ->
            onSnappedTime(snappedTime.snappedLocalTime)
            snappedTime.snappedIndex
        }
    )
}