@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.design

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TopAppBarColors
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.contentColorFor
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.flutterup.app.R
import com.flutterup.app.design.component.AppBackground

@Composable
fun AppScaffold(
    title: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    canGoBack: Boolean = true,
    onBackClick: () -> Unit = {},
    navigation: @Composable () -> Unit = { AppDefaultNavigationIcon(canGoBack, onBackClick) },
    rightNavigationContent: @Composable BoxScope.() -> Unit = {},
    colors: TopAppBarColors = TopAppBarDefaults.centerAlignedTopAppBarColors(
        containerColor = MaterialTheme.colorScheme.surfaceContainer,
        titleContentColor = MaterialTheme.colorScheme.onSurface,
        navigationIconContentColor = MaterialTheme.colorScheme.onSurface
    ),
    containerColor: Color = MaterialTheme.colorScheme.background,
    contentColor: Color = contentColorFor(containerColor),
    content: @Composable (paddingValues: PaddingValues) -> Unit
) {
    AppBackground {
        Scaffold(
            topBar = {
                Box {
                    CenterAlignedTopAppBar(
                        title = title,
                        navigationIcon = navigation,
                        colors = colors
                    )

                    Box(
                        modifier = Modifier
                            .windowInsetsPadding(TopAppBarDefaults.windowInsets)
                            .padding(end = 10.dp)
                            .align(Alignment.CenterEnd)
                    ) {
                        rightNavigationContent()
                    }
                }
            },
            modifier = modifier,
            containerColor = containerColor,
            contentColor = contentColor,
        ) {
            content(it)
        }
    }
}

@Composable
fun AppDefaultNavigationIcon(canGoBack: Boolean, onBackClick: () -> Unit) {
    if (!canGoBack) return
    IconButton(onClick = onBackClick) {
        Icon(
            imageVector = ImageVector.vectorResource(id = R.drawable.ic_arrow_back),
            contentDescription = null
        )
    }
}

@Preview
@Composable
private fun AppScaffoldPreview() {
    AppScaffold(
        title = { },
        onBackClick = {},
        rightNavigationContent = {
            Icon(
                painter = painterResource(R.drawable.ic_report),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier.size(24.dp)
            )
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) { }
}