package com.flutterup.app.design.theme

import androidx.compose.ui.graphics.Color

internal val ErrorPrimary = Color(0xFFB00020)
internal val LinePrimary = Color(0x33571D4F)

internal val LineSecondary = Color(0x33BCA1B8)

internal val BorderPrimary = Color(0xFF999999)

internal val ShimmerPrimary = Color(0xFFF3F3F3)

internal val ShimmerOnPrimary = Color(0xFFE7E7E7)

internal val ShadowSpotPrimary = Color(0x1F541A3C)

internal val ShadowAmbientPrimary = Color(0x1F541A3C)

internal val SwitchPrimary = Color(0xFFA081D5)
internal val SwitchSecondary = Color(0xFF554C5C)

internal val VIPPrimary = Color(0xFFD57F5B)

internal val LabelBackgroundPrimary = Color(0xFF988DE2)

internal val PurpleCoverPrimary = Color(0x822B1F6C)
internal val BackgroundPrimary = Color(0xFFF8F8F8)
internal val BackgroundSecondary = Color(0x19A081D5)

internal val BackgroundBlackPrimary = Color(0xFF170769)

internal val PurplePrimary = Color(0xFFA081D5)
internal val PurpleSecondary = Color(0xFF5C39A5)

internal val PurpleGradientPrimary = Color(0xFF705BB6)

internal val PurpleGradientSecondaryStart = Color(0xFF8E5386)
internal val PurpleGradientSecondaryEnd = Color(0xFF8D538C)

internal val WinkGradientStart = Color(0x78A69CEA)
internal val WinkGradientEnd = Color(0x33867AFF)
internal val WinkBorder = Color(0xFFC4B5F6)

internal val PaymentBackgroundGradientStart = Color(0xFFF1CE9F)
internal val PaymentBackgroundGradientEnd = Color(0xFFFFF7F0)

internal val PurpleTertiaryContainer = Color(0xFFEAE6FF)

internal val PinkPrimary = Color(0xFFFDF7FB)

internal val PinkSecondary = Color(0xFFEFD7FC)

internal val RedPrimary = Color(0xFFFF3838)

internal val TextBlack = Color(0xFF000000)
internal val TextBlack333 = Color(0xFF333333)
internal val TextBlack444 = Color(0xFF444444)
internal val TextBlack666 = Color(0xFF666666)
internal val TextBlack26  = Color(0xFF262626)
internal val TextBlack3b = Color(0xFF3B3B3B)

internal val TextBlack151 = Color(0xFF151926)

internal val TextBlack31 = Color(0xFF313131)
internal val TextGray999 = Color(0xFF999999)
internal val TextGray6f7288 = Color(0xFF6F7288)
internal val TextGrayE5 = Color(0xFFE5E5E5)

internal val TextPurple571 = Color(0xFF571E51)

internal val TextBrownVipPrimary = Color(0xFF6A1B0E)
internal val TextBrownVipSecondary = Color(0xFFD27B57)
internal val TextBrownVipTriplet = Color(0xFF7B331A)

internal val TextPurpleGradientStart = Color(0xFFBD3AAF)
internal val TextPurpleGradientCenter = Color(0xFF8845BF)
internal val TextPurpleGradientEnd = Color(0xFF56369D)

internal val TextBrownGradientStart = Color(0xFF562412)

internal val TextGoldenGradientEnd = Color(0xFFBC4F27)