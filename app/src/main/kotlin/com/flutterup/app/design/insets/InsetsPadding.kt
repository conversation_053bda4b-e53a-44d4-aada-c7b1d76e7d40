package com.flutterup.app.design.insets

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.calculateEndPadding
import androidx.compose.foundation.layout.calculateStartPadding
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.systemBars
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * Insets Padding 扩展
 * 提供更灵活的padding控制
 */

/**
 * 只应用顶部padding
 */
@Composable
fun Modifier.topInsetsPadding(insets: WindowInsets): Modifier = this.then(
    Modifier.padding(top = insets.asPaddingValues().calculateTopPadding())
)

/**
 * 只应用底部padding
 */
@Composable
fun Modifier.bottomInsetsPadding(insets: WindowInsets): Modifier = this.then(
    Modifier.padding(bottom = insets.asPaddingValues().calculateBottomPadding())
)

/**
 * 只应用左侧padding
 */
@Composable
fun Modifier.startInsetsPadding(insets: WindowInsets): Modifier = this.then(
    Modifier.padding(start = insets.asPaddingValues().calculateStartPadding(LocalLayoutDirection.current))
)

/**
 * 只应用右侧padding
 */
@Composable
fun Modifier.endInsetsPadding(insets: WindowInsets): Modifier = this.then(
    Modifier.padding(end = insets.asPaddingValues().calculateEndPadding(LocalLayoutDirection.current))
)

/**
 * 只应用水平padding（左右）
 */
@Composable
fun Modifier.horizontalInsetsPadding(insets: WindowInsets): Modifier {
    val paddingValues = insets.asPaddingValues()
    val layoutDirection = LocalLayoutDirection.current
    return this.then(
        Modifier.padding(
            start = paddingValues.calculateStartPadding(layoutDirection),
            end = paddingValues.calculateEndPadding(layoutDirection)
        )
    )
}

/**
 * 只应用垂直padding（上下）
 */
@Composable
fun Modifier.verticalInsetsPadding(insets: WindowInsets): Modifier {
    val paddingValues = insets.asPaddingValues()
    return this.then(
        Modifier.padding(
            top = paddingValues.calculateTopPadding(),
            bottom = paddingValues.calculateBottomPadding()
        )
    )
}

/**
 * 自定义insets padding，可以选择性地应用某些方向的padding
 */
@Composable
fun Modifier.selectiveInsetsPadding(
    insets: WindowInsets,
    applyTop: Boolean = true,
    applyBottom: Boolean = true,
    applyStart: Boolean = true,
    applyEnd: Boolean = true
): Modifier {
    val paddingValues = insets.asPaddingValues()
    val layoutDirection = LocalLayoutDirection.current
    
    return this.then(
        Modifier.padding(
            top = if (applyTop) paddingValues.calculateTopPadding() else 0.dp,
            bottom = if (applyBottom) paddingValues.calculateBottomPadding() else 0.dp,
            start = if (applyStart) paddingValues.calculateStartPadding(layoutDirection) else 0.dp,
            end = if (applyEnd) paddingValues.calculateEndPadding(layoutDirection) else 0.dp
        )
    )
}

/**
 * 添加额外的padding到insets padding
 */
@Composable
fun Modifier.insetsPaddingPlus(
    insets: WindowInsets,
    extraTop: Dp = 0.dp,
    extraBottom: Dp = 0.dp,
    extraStart: Dp = 0.dp,
    extraEnd: Dp = 0.dp
): Modifier {
    val paddingValues = insets.asPaddingValues()
    val layoutDirection = LocalLayoutDirection.current
    
    return this.then(
        Modifier.padding(
            top = paddingValues.calculateTopPadding() + extraTop,
            bottom = paddingValues.calculateBottomPadding() + extraBottom,
            start = paddingValues.calculateStartPadding(layoutDirection) + extraStart,
            end = paddingValues.calculateEndPadding(layoutDirection) + extraEnd
        )
    )
}

/**
 * 常用的组合padding扩展
 */

/**
 * 只应用状态栏顶部padding
 */
@Composable
fun Modifier.statusBarsTopPadding(): Modifier = topInsetsPadding(WindowInsets.statusBars)

/**
 * 只应用导航栏底部padding
 */
@Composable
fun Modifier.navigationBarsBottomPadding(): Modifier = bottomInsetsPadding(WindowInsets.navigationBars)

/**
 * 只应用键盘底部padding
 */
@Composable
fun Modifier.imeBottomPadding(): Modifier = bottomInsetsPadding(WindowInsets.ime)

/**
 * 应用状态栏顶部 + 导航栏底部padding
 */
@Composable
fun Modifier.systemBarsVerticalPadding(): Modifier = this
    .statusBarsTopPadding()
    .navigationBarsBottomPadding()

/**
 * 应用系统栏水平padding
 */
@Composable
fun Modifier.systemBarsHorizontalPadding(): Modifier = horizontalInsetsPadding(WindowInsets.systemBars)

/**
 * 应用安全区域但排除键盘的padding
 */
@Composable
fun Modifier.safeAreaExcludeImePadding(): Modifier = this.then(
    Modifier.padding(WindowInsets.safeArea.asPaddingValues())
)
