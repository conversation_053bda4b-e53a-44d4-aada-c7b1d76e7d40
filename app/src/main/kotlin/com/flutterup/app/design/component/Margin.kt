package com.flutterup.app.design.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.calculateEndPadding
import androidx.compose.foundation.layout.calculateStartPadding
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp


@Composable
fun Margin(
    paddingValues: PaddingValues,
    modifier: Modifier = Modifier,
    content: @Composable RowScope.() -> Unit,
) {
    Column(modifier) {
        if (paddingValues.calculateTopPadding() > 0.dp) {
            Spacer(Modifier.height(paddingValues.calculateTopPadding()))
        }

        Row {
            if (paddingValues.calculateStartPadding(LayoutDirection.Ltr) > 0.dp) {
                Spacer(Modifier.width(paddingValues.calculateStartPadding(LayoutDirection.Ltr)))
            }

            content()

            if (paddingValues.calculateEndPadding(LayoutDirection.Ltr) > 0.dp) {
                Spacer(Modifier.width(paddingValues.calculateEndPadding(LayoutDirection.Ltr)))
            }
        }

        if (paddingValues.calculateBottomPadding() > 0.dp) {
            Spacer(Modifier.height(paddingValues.calculateBottomPadding()))
        }
    }
}