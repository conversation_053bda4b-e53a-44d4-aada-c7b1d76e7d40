package com.flutterup.app.design.component.wheelpicker

import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import com.flutterup.app.design.component.wheelpicker.core.DefaultWheelDateTimePicker
import com.flutterup.app.design.component.wheelpicker.core.SelectorProperties
import com.flutterup.app.design.component.wheelpicker.core.TimeFormat
import com.flutterup.app.design.component.wheelpicker.core.WheelDefaultTextItem
import com.flutterup.app.design.component.wheelpicker.core.WheelPickerDefaults
import java.time.LocalDateTime

@Composable
fun WheelDateTimePicker(
    modifier: Modifier = Modifier,
    startDateTime: LocalDateTime = LocalDateTime.now(),
    minDateTime: LocalDateTime = LocalDateTime.MIN,
    maxDateTime: LocalDateTime = LocalDateTime.MAX,
    yearsRange: IntRange? = IntRange(1922, 2122),
    timeFormat: TimeFormat = TimeFormat.HOUR_24,
    size: DpSize = DpSize(256.dp, 128.dp),
    rowCount: Int = 3,
    textStyle: TextStyle = MaterialTheme.typography.titleMedium,
    textColor: Color = LocalContentColor.current,
    selectorProperties: SelectorProperties = WheelPickerDefaults.selectorProperties(),
    onSnappedDateTime : (snappedDateTime: LocalDateTime) -> Unit = {},
    content:  @Composable LazyItemScope.(
        index: Int,
        text: String,
        textStyle: TextStyle,
        textColor: Color
    ) -> Unit = { index, text, textStyle, textColor ->
        WheelDefaultTextItem(text, textStyle, textColor)
    },
) {
    DefaultWheelDateTimePicker(
        modifier,
        startDateTime,
        minDateTime,
        maxDateTime,
        yearsRange,
        timeFormat,
        size,
        rowCount,
        textStyle,
        textColor,
        selectorProperties,
        content = content,
        onSnappedDateTime = { snappedDateTime ->
            onSnappedDateTime(snappedDateTime.snappedLocalDateTime)
            snappedDateTime.snappedIndex
        }
    )
}