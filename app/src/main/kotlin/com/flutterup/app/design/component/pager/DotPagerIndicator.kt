package com.flutterup.app.design.component.pager

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Composable
fun DotPagerIndicator(
    pageCount: Int,
    selectedPage: Int,
    modifier: Modifier = Modifier,
    config: DotPagerIndicatorConfig = DotPagerIndicatorDefaults.defaults
) {
    OrientationContent(
        modifier = modifier,
        config = config
    ) {
        repeat(pageCount) { iteration ->
            val color = if (iteration == selectedPage) config.selectedColor else config.unselectedColor
            val size = if (iteration == selectedPage) config.selectedIndicatorSize else config.indicatorSize

            Box(
                modifier = Modifier
                    .clip(config.shape)
                    .size(size)
                    .background(color)
            )
        }
    }
}

data class DotPagerIndicatorConfig(
    val orientation: Orientation = Orientation.Horizontal,

    val spaceBetween: Dp = 8.dp,

    val indicatorSize: Dp = 5.dp,

    val selectedIndicatorSize: Dp = 5.dp,

    val selectedColor: Color = Color.White,

    val unselectedColor: Color = Color.White.copy(0.5f),

    val shape: Shape = CircleShape,
)

object DotPagerIndicatorDefaults {

    val defaults = DotPagerIndicatorConfig()

    fun copy(
        orientation: Orientation = defaults.orientation,
        spaceBetween: Dp = defaults.spaceBetween,
        indicatorSize: Dp = defaults.indicatorSize,
        selectedIndicatorSize: Dp = defaults.selectedIndicatorSize,
        selectedColor: Color = defaults.selectedColor,
        unselectedColor: Color = defaults.unselectedColor,
        shape: Shape = defaults.shape,
    ) = defaults.copy(
        orientation = orientation,
        spaceBetween = spaceBetween,
        indicatorSize = indicatorSize,
        selectedIndicatorSize = selectedIndicatorSize,
        selectedColor = selectedColor,
        unselectedColor = unselectedColor,
        shape = shape,
    )
}

@Composable
private fun OrientationContent(
    modifier: Modifier = Modifier,
    config: DotPagerIndicatorConfig,
    content: @Composable () -> Unit
) {
    when(config.orientation) {
        Orientation.Horizontal -> {
            Row(
                modifier = modifier,
                horizontalArrangement = Arrangement.spacedBy(config.spaceBetween),
                verticalAlignment = Alignment.CenterVertically
            ) {
                content()
            }
        }
        Orientation.Vertical -> {
            Column(
                modifier = modifier,
                verticalArrangement = Arrangement.spacedBy(config.spaceBetween),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                content()
            }
        }
    }
}
