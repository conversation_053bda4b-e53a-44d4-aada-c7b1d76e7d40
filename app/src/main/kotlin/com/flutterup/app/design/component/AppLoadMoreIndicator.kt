package com.flutterup.app.design.component

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.design.theme.TextBlack
import com.flutterup.base.compose.refresh.PullToLoadMoreState

@Composable
fun AppLoadMoreIndicator(
    isLoadingMore: Boolean,
    hasNoMoreData: Boolean,
    state: PullToLoadMoreState,
) {
    // 控制整体可见性的动画，考虑 state.distanceFraction
    val alpha by animateFloatAsState(
        targetValue = if (isLoadingMore || (hasNoMoreData && state.distanceFraction > 0f)) 1f else 0f,
        label = "load_more_alpha"
    )

    when {
        hasNoMoreData -> {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "No more data",
                    style = MaterialTheme.typography.bodyMedium,
                    color = TextBlack,
                    textAlign = TextAlign.Center,
                )
            }
        }

        isLoadingMore -> {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
                    .alpha(alpha)
                    .scale(state.distanceFraction),
                contentAlignment = Alignment.Center
            ) {
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = PurplePrimary,
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Loading...",
                        style = MaterialTheme.typography.bodyMedium,
                        color = TextBlack
                    )
                }
            }
        }
    }
}