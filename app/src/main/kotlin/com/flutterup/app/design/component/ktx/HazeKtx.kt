package com.flutterup.app.design.component.ktx

import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import dev.chrisbanes.haze.HazeStyle
import dev.chrisbanes.haze.HazeTint
import dev.chrisbanes.haze.HazeTint.Companion.DefaultBlendMode

/**
 * 将颜色转换为HazeTint
 */
fun Color.toHazeTint(
    blendMode: BlendMode = DefaultBlendMode
): HazeTint {
    return HazeTint(
        color = this,
        blendMode = blendMode
    )
}

/**
 * 将HazeTint转换为HazeStyle
 * @param blurRadius 模糊半径
 * @param backgroundColor 背景颜色
 * @param noiseFactor 噪声因子. SDK 原始值为0.15, App内修改为0f
 * @param fallbackTint 备用色调
 */
fun HazeTint.toHazeStyle(
    blurRadius: Dp = Dp.Unspecified,
    backgroundColor: Color = Color.Unspecified,
    noiseFactor: Float = 0f,
    fallbackTint: HazeTint = HazeTint.Unspecified,
) = HazeStyle(
    tint = this,
    blurRadius = blurRadius,
    backgroundColor = backgroundColor,
    noiseFactor = noiseFactor,
    fallbackTint = fallbackTint,
)