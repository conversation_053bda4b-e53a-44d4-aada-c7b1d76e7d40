package com.flutterup.app.design.insets

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.systemBars
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier

/**
 * Insets CompositionLocal 支持
 * 提供全局的insets访问
 */

/**
 * 当前系统栏insets的CompositionLocal
 */
val LocalSystemBarsInsets = compositionLocalOf<WindowInsets> {
    error("No LocalSystemBarsInsets provided")
}

/**
 * 当前状态栏insets的CompositionLocal
 */
val LocalStatusBarsInsets = compositionLocalOf<WindowInsets> {
    error("No LocalStatusBarsInsets provided")
}

/**
 * 当前导航栏insets的CompositionLocal
 */
val LocalNavigationBarsInsets = compositionLocalOf<WindowInsets> {
    error("No LocalNavigationBarsInsets provided")
}

/**
 * 当前键盘insets的CompositionLocal
 */
val LocalImeInsets = compositionLocalOf<WindowInsets> {
    error("No LocalImeInsets provided")
}

/**
 * 当前安全区域insets的CompositionLocal
 */
val LocalSafeAreaInsets = compositionLocalOf<WindowInsets> {
    error("No LocalSafeAreaInsets provided")
}

/**
 * 系统栏padding values的CompositionLocal
 */
val LocalSystemBarsPadding = compositionLocalOf<PaddingValues> {
    error("No LocalSystemBarsPadding provided")
}

/**
 * 安全区域padding values的CompositionLocal
 */
val LocalSafeAreaPadding = compositionLocalOf<PaddingValues> {
    error("No LocalSafeAreaPadding provided")
}

/**
 * Insets Provider组件
 * 提供所有insets的CompositionLocal值
 */
@Composable
fun InsetsProvider(
    content: @Composable () -> Unit
) {
    val systemBarsInsets = WindowInsets.systemBars
    val statusBarsInsets = WindowInsets.statusBars
    val navigationBarsInsets = WindowInsets.navigationBars
    val imeInsets = WindowInsets.ime
    val safeAreaInsets = WindowInsets.safeArea
    
    val systemBarsPadding = systemBarsInsets.asPaddingValues()
    val safeAreaPadding = safeAreaInsets.asPaddingValues()
    
    CompositionLocalProvider(
        LocalSystemBarsInsets provides systemBarsInsets,
        LocalStatusBarsInsets provides statusBarsInsets,
        LocalNavigationBarsInsets provides navigationBarsInsets,
        LocalImeInsets provides imeInsets,
        LocalSafeAreaInsets provides safeAreaInsets,
        LocalSystemBarsPadding provides systemBarsPadding,
        LocalSafeAreaPadding provides safeAreaPadding,
        content = content
    )
}

/**
 * 便捷的Modifier扩展，使用CompositionLocal中的insets
 */

/**
 * 使用当前CompositionLocal中的系统栏padding
 */
@Composable
fun Modifier.localSystemBarsPadding(): Modifier = this.then(
    Modifier.padding(LocalSystemBarsPadding.current)
)

/**
 * 使用当前CompositionLocal中的安全区域padding
 */
@Composable
fun Modifier.localSafeAreaPadding(): Modifier = this.then(
    Modifier.padding(LocalSafeAreaPadding.current)
)

/**
 * 使用当前CompositionLocal中的状态栏顶部padding
 */
@Composable
fun Modifier.localStatusBarsTopPadding(): Modifier = topInsetsPadding(LocalStatusBarsInsets.current)

/**
 * 使用当前CompositionLocal中的导航栏底部padding
 */
@Composable
fun Modifier.localNavigationBarsBottomPadding(): Modifier = bottomInsetsPadding(LocalNavigationBarsInsets.current)

/**
 * 使用当前CompositionLocal中的键盘底部padding
 */
@Composable
fun Modifier.localImeBottomPadding(): Modifier = bottomInsetsPadding(LocalImeInsets.current)
