package com.flutterup.app.design.component.wheelpicker

import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import com.flutterup.app.design.component.wheelpicker.core.DefaultWheelDatePicker
import com.flutterup.app.design.component.wheelpicker.core.SelectorDividers
import com.flutterup.app.design.component.wheelpicker.core.SelectorProperties
import com.flutterup.app.design.component.wheelpicker.core.WheelDefaultTextItem
import com.flutterup.app.design.component.wheelpicker.core.WheelPickerDefaults
import java.time.LocalDate

@Composable
fun WheelDatePicker(
    modifier: Modifier = Modifier,
    startDate: LocalDate = LocalDate.now(),
    minDate: LocalDate = LocalDate.MIN,
    maxDate: LocalDate = LocalDate.MAX,
    yearsRange: IntRange? = IntRange(1922, 2122),
    size: DpSize = DpSize(256.dp, 128.dp),
    rowCount: Int = 3,
    textStyle: TextStyle = MaterialTheme.typography.titleMedium,
    textColor: Color = LocalContentColor.current,
    selectorProperties: SelectorProperties = WheelPickerDefaults.selectorProperties(),
    selectorDividers: SelectorDividers = WheelPickerDefaults.selectorDividers(),
    onSnappedDate : (snappedDate: LocalDate) -> Unit = {},
    content:  @Composable LazyItemScope.(
        index: Int,
        text: String,
        textStyle: TextStyle,
        textColor: Color
    ) -> Unit = { index, text, textStyle, textColor ->
        WheelDefaultTextItem(text, textStyle, textColor)
    },
) {
    DefaultWheelDatePicker(
        modifier,
        startDate,
        minDate,
        maxDate,
        yearsRange,
        size,
        rowCount,
        textStyle,
        textColor,
        selectorProperties,
        selectorDividers,
        content = content,
        onSnappedDate = { snappedDate ->
            onSnappedDate(snappedDate.snappedLocalDate)
            snappedDate.snappedIndex
        }
    )
}