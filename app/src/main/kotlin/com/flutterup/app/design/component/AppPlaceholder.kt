package com.flutterup.app.design.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.flutterup.app.R
import com.flutterup.app.design.theme.AppTheme

@Composable
fun AppPlaceholder(
    modifier: Modifier = Modifier,
    placeholder: @Composable () -> Unit = { AppImagePlaceholder() }
) {
    AppTheme {
        Box(
            modifier = modifier,
            contentAlignment = Alignment.Center
        ) {
            placeholder()
        }
    }
}

@Composable
fun AppImagePlaceholder(
    modifier: Modifier = Modifier,
    placeholderRes: Int = R.drawable.ic_normal_placeholder
) {
    Image(
        painter = painterResource(placeholderRes),
        contentDescription = null,
        modifier = modifier
    )
}

@Preview(backgroundColor = 0xFFFFFFFF, showBackground = true)
@Composable
private fun AppPlaceholderPreview() {
    AppTheme {
        AppPlaceholder(modifier = Modifier.fillMaxSize())
    }
}