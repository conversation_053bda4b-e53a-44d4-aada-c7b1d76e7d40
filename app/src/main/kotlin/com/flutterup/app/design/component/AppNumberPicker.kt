package com.flutterup.app.design.component

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.theme.AppTheme


@Composable
fun AppNumberPicker(
    value: Int,
    onValueChange: (Int?) -> Unit,
    modifier: Modifier = Modifier,
    colors: AppNumberColors = AppNumberColorDefaults.colors(),
    fieldEnable: Boolean = true,
    negativeEnable: Boolean = true,
    positiveEnable: Boolean = true,
    borderWidth: Dp = 1.dp,
    cornerRadius: Dp = 4.dp,
    pickerSize: DpSize = DpSize(25.dp, 25.dp),
    textStyle: TextStyle = TextStyle(
        fontSize = 16.sp,
        lineHeight = 14.sp,
        fontWeight = FontWeight.W400,
        color = colors.textColor,
        textAlign = TextAlign.Center,
    ),
) {
    val focusManager = LocalFocusManager.current
    val shape = RoundedCornerShape(cornerRadius)
    val negativePickerBackgroundColor = if (negativeEnable) colors.pickerBackgroundColor else colors.disablePickerBackgroundColor
    val positivePickerBackgroundColor = if (positiveEnable) colors.pickerBackgroundColor else colors.disablePickerBackgroundColor

    val negativePickerTint = if (negativeEnable) colors.pickerTint else colors.disablePickerTint
    val positivePickerTint = if (positiveEnable) colors.pickerTint else colors.disablePickerTint

    Box(modifier.clip(shape)) {
        Box(
            modifier = Modifier
                .matchParentSize()
                .background(colors.backgroundColor)
                .border(borderWidth, colors.outlineColor, shape)
        ) {
            AppTextField(
                value = value.toString(),
                onValueChange = { strValue ->
                    val intValue = strValue.toIntOrNull()
                    onValueChange(intValue)
                },
                enabled = fieldEnable,
                modifier = Modifier.matchParentSize().padding(horizontal = pickerSize.width),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Number,
                    imeAction = ImeAction.Done
                ),
                keyboardActions = KeyboardActions(
                    onDone = { focusManager.clearFocus() }
                ),
                textStyle = textStyle,
                colors = TextFieldDefaults.colors(
                    focusedTextColor = colors.textColor,
                    unfocusedTextColor = colors.textColor,
                    disabledTextColor = colors.textColor,
                    focusedContainerColor = Color.Transparent,
                    unfocusedContainerColor = Color.Transparent,
                    disabledContainerColor = Color.Transparent,
                ),
                contentPadding = PaddingValues(0.dp)
            )
        }

        Box(
            modifier = Modifier
                .size(pickerSize)
                .clip(shape)
                .background(negativePickerBackgroundColor)
                .align(Alignment.TopStart)
                .clickable(
                    role = Role.Button,
                    enabled = negativeEnable,
                    onClick = { onValueChange(value - 1) }
                )
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_app_number_picker_negative),
                contentDescription = null,
                tint = negativePickerTint,
                modifier = Modifier.align(Alignment.Center)
            )
        }

        Box(
            modifier = Modifier
                .size(pickerSize)
                .clip(shape)
                .background(positivePickerBackgroundColor)
                .align(Alignment.TopEnd)
                .clickable(
                    role = Role.Button,
                    enabled = positiveEnable,
                    onClick = { onValueChange(value + 1) }
                )
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_app_number_picker_positive),
                contentDescription = null,
                tint = positivePickerTint,
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}

object AppNumberColorDefaults {
    private val DEFAULT_OUTLINE_COLOR = Color(0xFFE8BC94)
    private val DEFAULT_TEXT_COLOR = Color(0xFFA96C51)
    private val DEFAULT_PICKER_TINT_COLOR = Color(0xFF6A1B0E)

    fun colors() = defaults

    fun colors(
        backgroundColor: Color = defaults.backgroundColor,
        outlineColor: Color = defaults.outlineColor,
        textColor: Color = defaults.textColor,
        pickerBackgroundColor: Color = defaults.pickerBackgroundColor,
        pickerTint: Color = defaults.pickerTint,
        disablePickerBackgroundColor: Color = defaults.disablePickerBackgroundColor,
        disablePickerTint: Color = defaults.disablePickerTint,
    ) = defaults.copy(
        backgroundColor = backgroundColor,
        outlineColor = outlineColor,
        textColor = textColor,
        pickerBackgroundColor = pickerBackgroundColor,
        pickerTint = pickerTint,
        disablePickerBackgroundColor = disablePickerBackgroundColor,
        disablePickerTint = disablePickerTint,
    )


    private val defaults = AppNumberColors(
        backgroundColor = Color.White,
        outlineColor = DEFAULT_OUTLINE_COLOR,
        textColor = DEFAULT_TEXT_COLOR,
        pickerBackgroundColor = DEFAULT_OUTLINE_COLOR,
        pickerTint = DEFAULT_PICKER_TINT_COLOR,
        disablePickerBackgroundColor = DEFAULT_OUTLINE_COLOR.copy(0.3f),
        disablePickerTint = DEFAULT_PICKER_TINT_COLOR.copy(0.3f),
    )
}

@Immutable
class AppNumberColors
internal constructor(
    val backgroundColor: Color,
    val outlineColor: Color,
    val textColor: Color,
    val pickerBackgroundColor: Color,
    val pickerTint: Color,
    val disablePickerBackgroundColor: Color,
    val disablePickerTint: Color,
) {

    fun copy(
        backgroundColor: Color = this.backgroundColor,
        outlineColor: Color = this.outlineColor,
        textColor: Color = this.textColor,
        pickerBackgroundColor: Color = this.pickerBackgroundColor,
        pickerTint: Color = this.pickerTint,
        disablePickerBackgroundColor: Color = this.disablePickerBackgroundColor,
        disablePickerTint: Color = this.disablePickerTint,
    ): AppNumberColors {
        return AppNumberColors(
            backgroundColor,
            outlineColor,
            textColor,
            pickerBackgroundColor,
            pickerTint,
            disablePickerBackgroundColor,
            disablePickerTint,
        )
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as AppNumberColors

        if (backgroundColor != other.backgroundColor) return false
        if (outlineColor != other.outlineColor) return false
        if (textColor != other.textColor) return false
        if (pickerBackgroundColor != other.pickerBackgroundColor) return false
        if (pickerTint != other.pickerTint) return false
        if (disablePickerBackgroundColor != other.disablePickerBackgroundColor) return false
        if (disablePickerTint != other.disablePickerTint) return false

        return true
    }

    override fun hashCode(): Int {
        var result = backgroundColor.hashCode()
        result = 31 * result + outlineColor.hashCode()
        result = 31 * result + textColor.hashCode()
        result = 31 * result + pickerBackgroundColor.hashCode()
        result = 31 * result + pickerTint.hashCode()
        result = 31 * result + disablePickerBackgroundColor.hashCode()
        result = 31 * result + disablePickerTint.hashCode()
        return result
    }
}

@Preview
@Composable
private fun AppNumberPickerPreview() {
    AppTheme {
        AppNumberPicker(
            value = 1,
            onValueChange = {},
            modifier = Modifier.width(90.dp).height(26.dp)
        )
    }
}