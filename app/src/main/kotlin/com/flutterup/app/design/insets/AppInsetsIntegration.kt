package com.flutterup.app.design.insets

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.flutterup.app.design.component.AppBackground
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.screen.LocalInnerPadding

/**
 * App Insets 集成组件
 * 展示如何在现有应用结构中集成 Insets 系统
 */

/**
 * 增强版的 AppBackground，集成了 Insets 支持
 */
@Composable
fun AppBackgroundWithInsets(
    modifier: Modifier = Modifier,
    applySystemBars: Boolean = true,
    applyIme: Boolean = false,
    content: @Composable () -> Unit
) {
    InsetsProvider {
        AppBackground(
            modifier = modifier.then(
                when {
                    applySystemBars && applyIme -> Modifier.fullScreenPadding()
                    applySystemBars -> Modifier.systemBarsPadding()
                    applyIme -> Modifier.imePadding()
                    else -> Modifier
                }
            )
        ) {
            content()
        }
    }
}

/**
 * 增强版的应用主题，集成了 Insets 支持
 */
@Composable
fun AppThemeWithInsets(
    enableEdgeToEdge: Boolean = true,
    content: @Composable () -> Unit
) {
    AppTheme {
        if (enableEdgeToEdge) {
            InsetsProvider {
                content()
            }
        } else {
            content()
        }
    }
}

/**
 * 兼容现有 LocalInnerPadding 的组件
 */
@Composable
fun CompatibleInsetsLayout(
    modifier: Modifier = Modifier,
    useSystemBars: Boolean = true,
    useIme: Boolean = false,
    content: @Composable () -> Unit
) {

    // 获取现有的 LocalInnerPadding
    val existingPadding = LocalInnerPadding.current
    
    val insetsModifier = when {
        useSystemBars && useIme -> Modifier.fullScreenPadding()
        useSystemBars -> Modifier.systemBarsPadding()
        useIme -> Modifier.imePadding()
        else -> Modifier
    }
    
    // 组合现有的 padding 和 insets padding
    val combinedModifier = modifier
        .fillMaxSize()
        .padding(existingPadding)
        .then(insetsModifier)
    
    content()
}

/**
 * 为不同类型的屏幕提供预设的 Insets 配置
 */
object ScreenInsetsPresets {
    
    /**
     * 全屏内容（如视频播放器）
     */
    @Composable
    fun FullScreenContent(
        modifier: Modifier = Modifier,
        content: @Composable () -> Unit
    ) {
        // 全屏内容通常不需要任何 insets padding
        content()
    }
    
    /**
     * 普通内容屏幕
     */
    @Composable
    fun NormalContent(
        modifier: Modifier = Modifier,
        content: @Composable () -> Unit
    ) {
        InsetsProvider {
            content()
        }
    }
    
    /**
     * 聊天/输入屏幕
     */
    @Composable
    fun ChatContent(
        modifier: Modifier = Modifier,
        content: @Composable () -> Unit
    ) {
        InsetsProvider {
            content()
        }
    }
    
    /**
     * 模态对话框内容
     */
    @Composable
    fun ModalContent(
        modifier: Modifier = Modifier,
        content: @Composable () -> Unit
    ) {
        // 模态对话框通常需要完整的 insets 处理
        InsetsProvider {
            content()
        }
    }
    
    /**
     * 底部表单内容
     */
    @Composable
    fun BottomSheetContent(
        modifier: Modifier = Modifier,
        content: @Composable () -> Unit
    ) {
        // 底部表单需要导航栏和键盘适配
        content()
    }
}

/**
 * 便捷的屏幕包装器
 */
@Composable
fun ScreenWrapper(
    modifier: Modifier = Modifier,
    type: ScreenType = ScreenType.Normal,
    content: @Composable () -> Unit
) {
    when (type) {
        ScreenType.FullScreen -> {
            ScreenInsetsPresets.FullScreenContent(modifier, content)
        }
        ScreenType.Normal -> {
            ScreenInsetsPresets.NormalContent(modifier, content)
        }
        ScreenType.Chat -> {
            ScreenInsetsPresets.ChatContent(modifier, content)
        }
        ScreenType.Modal -> {
            ScreenInsetsPresets.ModalContent(modifier, content)
        }
        ScreenType.BottomSheet -> {
            ScreenInsetsPresets.BottomSheetContent(modifier, content)
        }
    }
}

enum class ScreenType {
    FullScreen,
    Normal,
    Chat,
    Modal,
    BottomSheet
}

/**
 * 迁移助手 - 帮助从旧的 padding 系统迁移到新的 insets 系统
 */
object InsetsMigrationHelper {
    
    /**
     * 替换旧的手动 padding
     */
    @Composable
    fun ReplaceManualPadding(
        oldTopPadding: Boolean = false,
        oldBottomPadding: Boolean = false,
        content: @Composable (Modifier) -> Unit
    ) {
        val modifier = when {
            oldTopPadding && oldBottomPadding -> Modifier.systemBarsPadding()
            oldTopPadding -> Modifier.statusBarsPadding()
            oldBottomPadding -> Modifier.navigationBarsPadding()
            else -> Modifier
        }
        
        content(modifier)
    }
    
    /**
     * 渐进式迁移 - 可以同时使用旧系统和新系统
     */
    @Composable
    fun GradualMigration(
        useNewInsets: Boolean = true,
        fallbackPadding: PaddingValues = PaddingValues(),
        content: @Composable (Modifier) -> Unit
    ) {
        val modifier = if (useNewInsets) {
            Modifier.systemBarsPadding()
        } else {
            Modifier.padding(fallbackPadding)
        }
        
        content(modifier)
    }
}
