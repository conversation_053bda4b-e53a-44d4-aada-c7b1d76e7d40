package com.flutterup.app.design.insets

import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.displayCutout
import androidx.compose.foundation.layout.displayCutoutPadding
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.union
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier

/**
 * 提供统一的WindowInsets处理和padding应用
 */
val WindowInsets.Companion.safeArea: WindowInsets
    @Composable get() = systemBars.union(displayCutout)

val WindowInsets.Companion.fullScreen: WindowInsets
    @Composable get() = safeArea.union(ime)

/**
 * 应用安全区域padding
 */
fun Modifier.safeAreaPadding(): Modifier = this.then(
    Modifier.systemBarsPadding().displayCutoutPadding()
)

/**
 * 应用完整屏幕padding
 */
fun Modifier.fullScreenPadding(): Modifier = this.then(
    Modifier.safeAreaPadding().imePadding()
)

