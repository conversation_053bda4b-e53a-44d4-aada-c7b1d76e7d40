package com.flutterup.app.navigation

import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.slideOutVertically
import androidx.navigation.NavBackStackEntry

/**
 * 导航动画工具类
 * 提供常用的页面切换动画效果
 */
object NavigationAnimations {

    /**
     * 默认动画持续时间
     */
    const val DEFAULT_DURATION = 300
    const val SLOW_DURATION = 400
    const val FAST_DURATION = 200

    /**
     * 水平滑动进入动画（从右到左）
     */
    fun slideInFromRight(duration: Int = DEFAULT_DURATION): EnterTransition {
        return slideInHorizontally(
            initialOffsetX = { fullWidth -> fullWidth },
            animationSpec = tween(duration)
        ) + fadeIn(animationSpec = tween(duration))
    }

    /**
     * 水平滑动退出动画（从左到右）
     */
    fun slideOutToLeft(duration: Int = DEFAULT_DURATION): ExitTransition {
        return slideOutHorizontally(
            targetOffsetX = { fullWidth -> -fullWidth },
            animationSpec = tween(duration)
        ) + fadeOut(animationSpec = tween(duration))
    }

    /**
     * 水平滑动进入动画（从左到右）
     */
    fun slideInFromLeft(duration: Int = DEFAULT_DURATION): EnterTransition {
        return slideInHorizontally(
            initialOffsetX = { fullWidth -> -fullWidth },
            animationSpec = tween(duration)
        ) + fadeIn(animationSpec = tween(duration))
    }

    /**
     * 水平滑动退出动画（从右到左）
     */
    fun slideOutToRight(duration: Int = DEFAULT_DURATION): ExitTransition {
        return slideOutHorizontally(
            targetOffsetX = { fullWidth -> fullWidth },
            animationSpec = tween(duration)
        ) + fadeOut(animationSpec = tween(duration))
    }

    /**
     * 垂直滑动进入动画（从下到上）
     */
    fun slideInFromBottom(duration: Int = DEFAULT_DURATION): EnterTransition {
        return slideInVertically(
            initialOffsetY = { fullHeight -> fullHeight },
            animationSpec = tween(duration)
        ) + fadeIn(animationSpec = tween(duration))
    }

    /**
     * 垂直滑动退出动画（从上到下）
     */
    fun slideOutToTop(duration: Int = DEFAULT_DURATION): ExitTransition {
        return slideOutVertically(
            targetOffsetY = { fullHeight -> -fullHeight },
            animationSpec = tween(duration)
        ) + fadeOut(animationSpec = tween(duration))
    }

    /**
     * 垂直滑动进入动画（从上到下）
     */
    fun slideInFromTop(duration: Int = DEFAULT_DURATION): EnterTransition {
        return slideInVertically(
            initialOffsetY = { fullHeight -> -fullHeight },
            animationSpec = tween(duration)
        ) + fadeIn(animationSpec = tween(duration))
    }

    /**
     * 垂直滑动退出动画（从下到上）
     */
    fun slideOutToBottom(duration: Int = DEFAULT_DURATION): ExitTransition {
        return slideOutVertically(
            targetOffsetY = { fullHeight -> fullHeight },
            animationSpec = tween(duration)
        ) + fadeOut(animationSpec = tween(duration))
    }

    /**
     * 缩放进入动画
     */
    fun scaleInAnimation(
        initialScale: Float = 0.8f,
        duration: Int = DEFAULT_DURATION
    ): EnterTransition {
        return scaleIn(
            initialScale = initialScale,
            animationSpec = tween(duration)
        ) + fadeIn(animationSpec = tween(duration))
    }

    /**
     * 缩放退出动画
     */
    fun scaleOutAnimation(
        targetScale: Float = 0.8f,
        duration: Int = DEFAULT_DURATION
    ): ExitTransition {
        return scaleOut(
            targetScale = targetScale,
            animationSpec = tween(duration)
        ) + fadeOut(animationSpec = tween(duration))
    }

    /**
     * 淡入动画
     */
    fun fadeInAnimation(duration: Int = DEFAULT_DURATION): EnterTransition {
        return fadeIn(animationSpec = tween(duration))
    }

    /**
     * 淡出动画
     */
    fun fadeOutAnimation(duration: Int = DEFAULT_DURATION): ExitTransition {
        return fadeOut(animationSpec = tween(duration))
    }

    /**
     * 无动画
     */
    fun noAnimation(): EnterTransition = EnterTransition.None
    fun noExitAnimation(): ExitTransition = ExitTransition.None

    /**
     * 预定义的动画组合
     */
    object Presets {
        /**
         * 标准水平滑动动画
         */
        val horizontalSlide = AnimationSet(
            enter = slideInFromRight(),
            exit = slideOutToLeft(),
            popEnter = slideInFromLeft(),
            popExit = slideOutToRight()
        )

        /**
         * 标准垂直滑动动画
         */
        val verticalSlide = AnimationSet(
            enter = slideInFromBottom(),
            exit = slideOutToTop(),
            popEnter = slideInFromTop(),
            popExit = slideOutToBottom()
        )

        /**
         * 缩放动画
         */
        val scale = AnimationSet(
            enter = scaleInAnimation(),
            exit = scaleOutAnimation(),
            popEnter = scaleInAnimation(),
            popExit = scaleOutAnimation()
        )

        /**
         * 淡入淡出动画
         */
        val fade = AnimationSet(
            enter = fadeInAnimation(),
            exit = fadeOutAnimation(),
            popEnter = fadeInAnimation(),
            popExit = fadeOutAnimation()
        )
    }

    /**
     * 动画集合数据类
     */
    data class AnimationSet(
        val enter: EnterTransition,
        val exit: ExitTransition,
        val popEnter: EnterTransition,
        val popExit: ExitTransition
    )
}
