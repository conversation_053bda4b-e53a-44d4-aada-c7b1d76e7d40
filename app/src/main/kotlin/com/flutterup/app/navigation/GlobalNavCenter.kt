package com.flutterup.app.navigation

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Looper
import androidx.annotation.UiThread
import androidx.navigation.NavDestination.Companion.hasRoute
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.navOptions
import com.flutterup.app.model.AppPaymentFrom
import com.flutterup.app.model.PaymentPacksFilter
import com.flutterup.app.model.SystemMessageEntity
import com.flutterup.app.model.UserInfo
import com.flutterup.app.utils.UserMonitor
import com.flutterup.app.screen.HomeBaseRoute
import com.flutterup.app.screen.MainActivity
import com.flutterup.app.screen.chat.ChatMessageRoute
import com.flutterup.app.screen.chat.ChatPrivateMomentsRoute
import com.flutterup.app.screen.chat.ChatPrivateMomentsTutorialRoute
import com.flutterup.app.screen.chat.MatchedRoute
import com.flutterup.app.screen.chat.ReceivePingChatRoute
import com.flutterup.app.screen.common.MediaPreviewRoute
import com.flutterup.app.screen.login.LoginBaseRoute
import com.flutterup.app.screen.login.LoginProfileStep1Route
import com.flutterup.app.screen.login.LoginProfileStep2Route
import com.flutterup.app.screen.payment.PaymentDiamondsRoute
import com.flutterup.app.screen.payment.PaymentPacksRoute
import com.flutterup.app.screen.payment.PaymentSubscriptionRoute
import com.flutterup.app.screen.relate.RelateRoute
import com.flutterup.app.screen.relate.state.Visitors
import com.flutterup.app.screen.relate.state.WinksReceived
import com.flutterup.app.utils.AppActivityManager
import com.flutterup.app.utils.ReviewUtils
import com.flutterup.base.store.MMKVStore
import com.flutterup.base.store.booleanValue
import com.flutterup.base.utils.JsonUtils
import com.flutterup.base.utils.Timber
import com.flutterup.base.utils.applicationEntryPoint
import com.flutterup.network.Action
import com.flutterup.network.AppDispatchers
import com.flutterup.network.Dispatcher
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import io.rong.imlib.model.Message
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.json.JSONObject
import javax.inject.Inject
import javax.inject.Singleton

private const val TAG = "GlobalNavCenter"

/**
 * 全局导航中心
 * 用于管理全局导航相关逻辑
 */
@Singleton
class GlobalNavCenter @Inject constructor(
    private val userMonitor: UserMonitor,
    private val mmkvStore: MMKVStore,
    @ApplicationContext private val context: Context,
    @Dispatcher(AppDispatchers.Main) val dispatcher: CoroutineDispatcher,
    private val scope: CoroutineScope,
) {
    private var navController: NavHostController? = null

    private var isFirstOpenPrivateMomentTutorial: Boolean by mmkvStore.booleanValue("isFirstOpenPrivateMomentTutorial", true)

    private fun runOnUiThread(block: () -> Unit) {
        if (Thread.currentThread() == Looper.getMainLooper().thread) {
            block()
        } else {
            scope.launch(dispatcher) { block() }
        }
    }

    fun setNavController(navController: NavHostController) {
        this.navController = navController
    }

    @UiThread
    fun popupMediaPreview() {
        val backStackEntry = navController?.currentBackStackEntry ?: return
        val destination = backStackEntry.destination

        if (destination.hasRoute(MediaPreviewRoute::class)) {
            runOnUiThread {
                navController?.popBackStack(MediaPreviewRoute::class, inclusive = true, saveState = false)
            }
        }
    }

    fun navigateLogin() {
        runOnUiThread {
            navController?.navigate(LoginBaseRoute)
        }
    }

    /**
     * 导航到首页, 会检查是是无效用户
     */
    fun navigateHome() {
        if (userMonitor.isInvalid) { //是无效用户, 跳转资料填写页
            navigateToLoginProfileStep1()
            return
        }

        runOnUiThread {
            navController?.navigate(HomeBaseRoute)
        }
    }

    fun navigateToLoginProfileStep1() {
        runOnUiThread { navController?.navigate(LoginProfileStep1Route) }
    }

    fun navigateToLoginProfileStep2() {
        runOnUiThread { navController?.navigate(LoginProfileStep2Route) }
    }


    /**
     * UI logic for navigating to a top level destination in the app. Top level destinations have
     * only one copy of the destination of the back stack, and save and restore state whenever you
     * navigate to and from it.
     *
     * @param topLevelDestination: The destination the app needs to navigate to.
     */
    fun navigateToTopLevelDestination(topLevelDestination: TopLevelDestination) {
        runOnUiThread {
            navController?.let {
                val topLevelNavOptions = navOptions {
                    // 弹出到图表的起始目标，避免在返回栈中堆积大量目标页面
                    popUpTo(it.graph.findStartDestination().id) {
                        saveState = true
                    }
                    // 避免重新选择同一项目时产生多个相同目标的副本
                    launchSingleTop = true
                    // 重新选择之前选择的项目时恢复状态
                    restoreState = true
                }

                it.navigate(topLevelDestination.route, topLevelNavOptions)
            }
        }
    }

    fun navigateToTopLevelDestination(route: BottomNavRoute) {
        runOnUiThread {
            navController?.let {
                val topLevelNavOptions = navOptions {
                    // 弹出到图表的起始目标，避免在返回栈中堆积大量目标页面
                    popUpTo(it.graph.findStartDestination().id) {
                        saveState = true
                    }
                    // 避免重新选择同一项目时产生多个相同目标的副本
                    launchSingleTop = true
                    // 重新选择之前选择的项目时恢复状态
                    restoreState = false //不恢复
                }

                it.navigate(route, topLevelNavOptions)
            }
        }
    }

    fun navigateToVipDialog(
        from: AppPaymentFrom = AppPaymentFrom.UNKNOWN,
        expireTime: Long? = null,
        cancelable: Boolean = true,
    ) {
        runOnUiThread { navController?.navigate(PaymentSubscriptionRoute(from, expireTime, cancelable)) }
    }

    fun navigateToPaymentPacks(
        from: AppPaymentFrom = AppPaymentFrom.UNKNOWN,
        filter: PaymentPacksFilter = PaymentPacksFilter.ALL,
        lackNum: Int? = null,
    ) {
        val isVip = userMonitor.isVip
        if (!isVip) { //非会先强制买会员
            navigateToVipDialog(from)
            return
        }

        runOnUiThread { navController?.navigate(PaymentPacksRoute(filter, lackNum)) }
    }

    fun navigateToPaymentDiamonds(
        from: AppPaymentFrom = AppPaymentFrom.UNKNOWN,
    ) {
        val isVip = userMonitor.isVip

        if (!isVip) { //非会先强制买会员
            navigateToVipDialog(from)
            return
        }

        runOnUiThread { navController?.navigate(PaymentDiamondsRoute(from)) }
    }

    fun navigateToPrivateMoments() {
        val isVip = userMonitor.isVip
        if (!isVip) { //非会先强制买会员
            navigateToVipDialog(AppPaymentFrom.CHAT_ALBUM_LIMIT)
            return
        }

        if (isFirstOpenPrivateMomentTutorial) { //第一次打开, 先展示教程
            isFirstOpenPrivateMomentTutorial = false
            runOnUiThread {
                navController?.navigate(ChatPrivateMomentsTutorialRoute)
            }
        } else {
            runOnUiThread {
                navController?.navigate(ChatPrivateMomentsRoute)
            }
        }
    }

    fun navigateToMediaPreview(route: MediaPreviewRoute) {
        runOnUiThread {
            navController?.navigate(route)
        }
    }

    fun navigate(action: Action) {
        navigateUseAction(action)
    }

    fun navigate(systemMessageEntity: SystemMessageEntity) {
        navigateUseSystem(systemMessageEntity)
    }

    fun createPendingIntent(requestCode: Int, systemMessageEntity: SystemMessageEntity): PendingIntent {
        val intent = Intent(context, MainActivity::class.java)
        intent.putExtra(GlobalNavConst.KEY_ACTION, systemMessageEntity.action)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        return PendingIntent.getActivity(context, requestCode, intent, PendingIntent.FLAG_IMMUTABLE)
    }

    fun createMessagePendingIntent(requestCode: Int, message: Message): PendingIntent {
        val action = Action(
            id = GlobalNavConst.ACTION_CHAT,
            params = mapOf(NAV_USER_ID to message.targetId),
        )

        val intent = Intent(context, MainActivity::class.java)
        intent.putExtra(GlobalNavConst.KEY_ACTION, action)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        return PendingIntent.getActivity(context, requestCode, intent, PendingIntent.FLAG_IMMUTABLE)
    }


    private fun navigateUseAction(action: Action) {
        val navAction = NavAction.fromValue(action.id)
        navController?.let {
            navAction.navigate(this, it, action)
        }
    }

    private fun navigateUseSystem(systemMessageEntity: SystemMessageEntity) {
        val navSystemMessage = NavSystemMessage.fromValue(systemMessageEntity.pushType)
        navController?.let {
            navSystemMessage.navigate(this, it, systemMessageEntity)
        }
    }
}

private enum class NavAction(val value: Int) {
    Unknown(-1),

    Chat(3) {
        override fun navigate(
            navCenter: GlobalNavCenter,
            navController: NavHostController,
            action: Action
        ) {
            val map = action.params ?: return

            try {
                val json = jsonUtils.toJsonMap(map)
                json?.let {
                    val jsonObject = JSONObject(json)

                    if (jsonObject.has(NAV_USER_ID)) {
                        val userId = jsonObject.getString(NAV_USER_ID)
                        navController.navigate(ChatMessageRoute(userId))
                    }
                }
            } catch (_: Exception) {
            }
        }
    },

    NewMatch(9) {
        override fun navigate(
            navCenter: GlobalNavCenter,
            navController: NavHostController,
            action: Action
        ) {
            val map = action.params ?: return

            try {
                val json = jsonUtils.toJsonMap(map)
                json?.let {
                    val jsonObject = JSONObject(json)

                    if (jsonObject.has(NAV_USER_ID)) {
                        val userId = jsonObject.getString(NAV_USER_ID)
                        navController.navigate(MatchedRoute(userId))
                    }
                }
            } catch (_: Exception) {
            }
        }
    },

    Visitor(18) {
        override fun navigate(
            navCenter: GlobalNavCenter,
            navController: NavHostController,
            action: Action
        ) {
            navCenter.navigateToTopLevelDestination(RelateRoute(index = Visitors.INDEX))
        }
    },

    VipAlert(20) {
        override fun navigate(
            navCenter: GlobalNavCenter,
            navController: NavHostController,
            action: Action
        ) {
            val map = action.params ?: return

            //启动支付页，支付页关闭后检查当前会员状态，如果非会员弹出弹窗
            var payFrom = AppPaymentFrom.UNKNOWN
            var expireTime: Long? = null
            var cancelable = true

            //type类型: 0. 不提示, 1. 重复注册, 2. 快到期, 3. 已经到期
            var type = VipAlertStatus.None

            try {
                val json = jsonUtils.toJsonMap(map)
                json?.let {
                    val jsonObject = JSONObject(it)
                    if (jsonObject.has(NAV_EXPIRE_TIME)) {
                        expireTime = jsonObject.getLong(NAV_EXPIRE_TIME)
                    }
                    if (jsonObject.has(NAV_TYPE)) {
                        type = VipAlertStatus.fromValue(jsonObject.getInt(NAV_TYPE))
                    }
                }
            } catch (_: Exception) {
            }

            when(type) {
                VipAlertStatus.None -> {} //ignore this
                VipAlertStatus.Repeating -> {
                    payFrom = AppPaymentFrom.REPEATING
                    cancelable = false
                }
                VipAlertStatus.ExpiringSoon -> {
                    payFrom = AppPaymentFrom.EXPIRING_SOON
                }
                VipAlertStatus.Expired -> {
                    payFrom = AppPaymentFrom.EXPIRED
                    cancelable = false
                }
            }

            navCenter.navigateToVipDialog(payFrom, expireTime, cancelable)
        }
    },


    Winks(21) {
        override fun navigate(
            navCenter: GlobalNavCenter,
            navController: NavHostController,
            action: Action
        ) {
            navCenter.navigateToTopLevelDestination(RelateRoute(index = WinksReceived.INDEX))
        }
    },

    VipOrPacks(1006) {
        override fun navigate(
            navCenter: GlobalNavCenter,
            navController: NavHostController,
            action: Action
        ) {
            val map = action.params ?: return

            var payFrom = AppPaymentFrom.UNKNOWN
            var type = PaymentPacksFilter.ALL
            var desc: String? = null
            var lackNum: Int? = null

            try {
                val json = jsonUtils.toJsonMap(map)
                json?.let {
                    val jsonObject = JSONObject(json)
                    if (jsonObject.has(NAV_PAY_FROM)) {
                        payFrom = AppPaymentFrom.fromValue(jsonObject.getInt(NAV_PAY_FROM))
                    }
                    if (jsonObject.has(NAV_TYPE)) {
                        type = PaymentPacksFilter.fromValue(jsonObject.getInt(NAV_TYPE))
                    }
                    if (jsonObject.has(NAV_DESC)) {
                        desc = jsonObject.getString(NAV_DESC)
                    }
                    if (jsonObject.has(NAV_LACK_NUM)) {
                        lackNum = jsonObject.getInt(NAV_LACK_NUM)
                    }
                }
            } catch (_: Exception) {
            }

            if (desc != null) {
                Timber.showToast(desc)
            }
            when(type) {
                PaymentPacksFilter.ALL ->  navCenter.navigateToVipDialog(payFrom)

                PaymentPacksFilter.PHOTO,
                PaymentPacksFilter.VIDEO,
                PaymentPacksFilter.PING_CHAT-> navCenter.navigateToPaymentPacks(payFrom, type, lackNum)
            }
        }
    },

    Vip(1007) {
        override fun navigate(
            navCenter: GlobalNavCenter,
            navController: NavHostController,
            action: Action
        ) {
            val map = action.params ?: return

            //启动支付页，支付页关闭后检查当前会员状态，如果非会员弹出弹窗
            var payFrom = AppPaymentFrom.UNKNOWN
            var status: Int? = null

            try {
                val json = jsonUtils.toJsonMap(map)
                json?.let {
                    val jsonObject = JSONObject(json)
                    if (jsonObject.has(NAV_PAY_FROM)) {
                        payFrom = AppPaymentFrom.fromValue(jsonObject.getInt(NAV_PAY_FROM))
                    }
                    if (jsonObject.has(NAV_STATUS)) {
                        status = jsonObject.getInt(NAV_STATUS)
                    }
                }
            } catch (_: Exception) {
            }

            if (status == 1) { //只有1才会展示
                navCenter.navigateToVipDialog(payFrom)
            }
        }
    },

    Review(1012) {
        override fun navigate(
            navCenter: GlobalNavCenter,
            navController: NavHostController,
            action: Action
        ) {
            AppActivityManager.topActivity?.let {
                ReviewUtils.startReview(activity = it)
            }
        }
    };

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface NavActionEntryPoint {
        fun jsonUtils(): JsonUtils
    }

    protected val jsonUtils: JsonUtils by lazy {
        applicationEntryPoint<NavActionEntryPoint>().jsonUtils()
    }

    open fun navigate(navCenter: GlobalNavCenter, navController: NavHostController, action: Action) = Unit

    companion object {
        fun fromValue(value: Int?) = entries.firstOrNull { it.value == value } ?: Unknown
    }
}

enum class NavSystemMessage(val value: Int) {
    Unknown(-1),

    Winks(1) {
        override fun navigate(
            navCenter: GlobalNavCenter,
            navController: NavHostController,
            systemMessageEntity: SystemMessageEntity
        ) {
            navCenter.navigateToTopLevelDestination(RelateRoute(index = WinksReceived.INDEX))
        }
    },

    Matched(3) {
        override fun navigate(
            navCenter: GlobalNavCenter,
            navController: NavHostController,
            systemMessageEntity: SystemMessageEntity
        ) {
            systemMessageEntity.userId?.let {
                navController.navigate(MatchedRoute(systemMessageEntity.userId))
            }
        }
    },

    Visitor(4) {
        override fun navigate(
            navCenter: GlobalNavCenter,
            navController: NavHostController,
            systemMessageEntity: SystemMessageEntity
        ) {
            navCenter.navigateToTopLevelDestination(RelateRoute(index = Visitors.INDEX))
        }
    },

    ReceivePingChat(5) {
        override fun navigate(
            navCenter: GlobalNavCenter,
            navController: NavHostController,
            systemMessageEntity: SystemMessageEntity
        ) {
            systemMessageEntity.userId?.let {
                navController.navigate(ReceivePingChatRoute(systemMessageEntity.userId))
            }
        }
    };

    open fun navigate(navCenter: GlobalNavCenter, navController: NavHostController, systemMessageEntity: SystemMessageEntity) =
        Unit

    companion object {
        fun fromValue(value: Int?) = entries.firstOrNull { it.value == value } ?: Unknown
    }
}

private const val NAV_USER_ID = "user_id"
private const val NAV_FROM = "from"
private const val NAV_PAY_FROM = "event_from"
private const val NAV_TYPE = "type"
private const val NAV_EXPIRE_TIME = "expire_time"
private const val NAV_DESC = "desc"
private const val NAV_STATUS = "status"

private const val NAV_LACK_NUM = "lack_num"

private enum class VipAlertStatus(val value: Int) {
    None(0),
    Repeating(1),
    ExpiringSoon(2),
    Expired(3);

    companion object {
        fun fromValue(value: Int?) = entries.firstOrNull { it.value == value } ?: None
    }
}