package com.flutterup.app.screen.discover.vm

import com.flutterup.app.model.DiscoverListEntity
import com.flutterup.app.network.ApiService
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseRepository
import javax.inject.Inject

class DiscoverRepository @Inject constructor(
    private val apiService: ApiService,
    private val userMonitor: UserMonitor,
) : BaseRepository() {

    suspend fun getDiscoverList(lastId: String? = null, size: Int = 20, tab: Int? = null): DiscoverListEntity? {
        val result = apiService.getSwipeUserList(lastId, size, tab)
        if (result.isSuccess) {
            return result.data
        }
        return null
    }
}