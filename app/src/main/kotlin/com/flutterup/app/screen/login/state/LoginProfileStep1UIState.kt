package com.flutterup.app.screen.login.state

import com.flutterup.app.model.Gender
import com.flutterup.base.BaseState
import com.flutterup.base.utils.DateUtils
import java.time.LocalDate

data class LoginProfileStep1UIState(
    val nickname: String = "",
    val birthday: LocalDate? = null,
    val gender: Gender = Gender.MALE,
    val meet: Gender = Gender.FEMALE,
    override val isLoading: Boolean = false,
) : BaseState {

    val ageText: String
        get() = if (birthday == null) "" else DateUtils.getAgeFromLocalDate(birthday).toString()


    /**
     * 是否可以继续
     */
    val isContinueEnabled: Boolean
        get() = nickname.isNotEmpty()
                && birthday != null
                && gender != Gender.UNSPECIFIED
                && meet != Gender.UNSPECIFIED
}