package com.flutterup.app.screen.chat.vm

import com.flutterup.app.network.ApiService
import com.flutterup.base.BaseRepository
import javax.inject.Inject

class ChatCustomerServiceRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {


    suspend fun triggerCustomerServiceGreeting(targetId: String): <PERSON><PERSON>an {
        try {
            val result = apiService.triggerCustomerServiceGreeting(targetId)
            return result.isSuccess
        } catch (_: Exception) {
            return false
        }
    }
}