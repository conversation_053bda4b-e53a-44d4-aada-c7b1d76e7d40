package com.flutterup.app.screen.chat.component.message.internal

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.preview.BooleanProvider
import com.flutterup.app.design.theme.TextBlack
import com.flutterup.app.model.UserInfo
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.message.TextMessage


@Composable
fun ChatUnsupportedMessageItem(
    mine: UserInfo?,
    other: UserInfo?,
    message: Message,
    onResendClick: (Message) -> Unit,
    onProfileClick: () -> Unit
) {
    if (message.messageDirection == Message.MessageDirection.SEND) {
        ChatUnsupportedMessageItemSender(mine, message, onResendClick)
    } else {
        ChatUnsupportedMessageItemReceiver(other, message, onProfileClick)
    }
}

@Composable
private fun ChatUnsupportedMessageItemSender(
    mine: UserInfo?,
    message: Message,
    onResendClick: (Message) -> Unit,
) {
    ChatMessageRowSender(
        mine = mine,
        message = message,
        onResendClick = onResendClick
    ) {
        Box(
            modifier = Modifier
                .background(
                    color = Color(0xFF988DE2),
                    shape = RoundedCornerShape(
                        topStart = BUBBLE_SHAPE_SIZE,
                        topEnd = BUBBLE_SHAPE_SIZE,
                        bottomEnd = 0.dp,
                        bottomStart = BUBBLE_SHAPE_SIZE
                    )
                )
                .padding(
                    horizontal = 20.dp,
                    vertical = 10.dp
                )
        ) {
            Text(
                text = stringResource(R.string.message_unsupported),
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 18.sp,
                    fontWeight = FontWeight.W400,
                    color = Color.White,
                ),
                modifier = Modifier
                    .wrapContentSize()
                    .widthIn(max = 300.dp)
            )
        }
    }
}


@Composable
private fun ChatUnsupportedMessageItemReceiver(
    other: UserInfo?,
    message: Message,
    onProfileClick: () -> Unit
) {
    ChatMessageRowReceiver(
        other = other,
        message = message,
        onProfileClick = onProfileClick
    ) {
        Box(
            modifier = Modifier
                .background(
                    color = Color.White,
                    shape = RoundedCornerShape(
                        topStart = BUBBLE_SHAPE_SIZE,
                        topEnd = BUBBLE_SHAPE_SIZE,
                        bottomEnd = BUBBLE_SHAPE_SIZE,
                        bottomStart = 0.dp
                    )
                )
                .padding(
                    horizontal = 20.dp,
                    vertical = 10.dp
                )
        ) {
            Text(
                text = stringResource(R.string.message_unsupported),
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 18.sp,
                    fontWeight = FontWeight.W400,
                    color = TextBlack,
                )
            )
        }
    }
}

private val BUBBLE_SHAPE_SIZE = 15.dp

@Preview()
@Composable
private fun ChatUnsupportedMessageItemPreview(
    @PreviewParameter(BooleanProvider::class) isSender: Boolean
) {
    val mine = UserInfo(userId = "1")
    val other = UserInfo(userId = "2")

    val message = Message.obtain("", Conversation.ConversationType.PRIVATE, TextMessage.obtain(""))
    message.messageDirection = if (isSender) Message.MessageDirection.SEND else Message.MessageDirection.RECEIVE
    ChatUnsupportedMessageItem(mine, other, message, {}, {})
}