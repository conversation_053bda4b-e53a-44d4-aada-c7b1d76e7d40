@file:OptIn(ExperimentalSharedTransitionApi::class)

package com.flutterup.app.screen.chat

import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.compose.dialog
import androidx.navigation.toRoute
import com.flutterup.app.model.AppFrom
import com.flutterup.app.navigation.BottomNavRoute
import com.flutterup.app.screen.LocalSharedTransitionScope
import com.flutterup.app.screen.chat.vm.ChatMessageViewModel
import com.flutterup.app.screen.chat.vm.ChatPrivateMomentsViewModel
import com.flutterup.app.screen.common.vm.ProfileSharedViewModel
import com.flutterup.app.screen.discover.DiscoverRoute
import com.flutterup.app.utils.viewModelScopedTo
import kotlinx.serialization.Serializable

@Serializable data object ChatRoute : BottomNavRoute

@Serializable data object ChatSystemConversationsRoute

@Serializable data object CustomerServiceRoute

@Serializable data object SystemMessageRoute

@Serializable data class ChatMessageRoute(val targetId: String)

@Serializable data class SendPingChatRoute(
    val targetId: String,
    val from: AppFrom = AppFrom.Discover,
)

@Serializable data class ReceivePingChatRoute(val targetId: String)

@Serializable data class MatchedRoute(val targetId: String)

@Serializable data object ChatPrivateMomentsRoute

@Serializable data object ChatPrivateMomentsTutorialRoute

fun NavGraphBuilder.chatGraph() {
    composable(route = ChatSystemConversationsRoute::class) {
        ChatSystemConversationsScreen()
    }

    composable(route = ChatMessageRoute::class) {
        val route = it.toRoute<ChatMessageRoute>()
        ChatMessageScreen(route.targetId)
    }

    composable(route = CustomerServiceRoute::class) {
        ChatMessageCustomerServiceScreen()
    }

    composable(route = SystemMessageRoute::class) {
        ChatSystemMessageScreen()
    }

    composable(route = SendPingChatRoute::class) {
        val route = it.toRoute<SendPingChatRoute>()
        val sharedTransitionScope = LocalSharedTransitionScope.current

        SendPingChatScreen(
            targetId = route.targetId,
            from = route.from,
            sharedTransitionScope = sharedTransitionScope,
            animatedContentScope = this,
        )
    }

    composable(route = ReceivePingChatRoute::class) {
        val route = it.toRoute<ReceivePingChatRoute>()


        ReceivePingChatScreen(route.targetId)
    }

    composable(route = MatchedRoute::class) {
        val route = it.toRoute<MatchedRoute>()
        MatchedScreen(route.targetId)
    }

    composable(route = ChatPrivateMomentsRoute::class) {
        val sharedChatMessageViewModel: ChatMessageViewModel = it.viewModelScopedTo(ChatMessageRoute::class)

        ChatPrivateMomentsScreen(
            sharedViewModel = sharedChatMessageViewModel
        )
    }

    dialog(route = ChatPrivateMomentsTutorialRoute::class) {
        ChatPrivateMomentsTutorialDialog()
    }
}