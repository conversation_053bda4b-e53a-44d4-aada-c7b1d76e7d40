package com.flutterup.app.screen.chat.component.message

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.design.theme.TextBlack
import com.flutterup.app.model.UserInfo
import com.flutterup.app.screen.chat.component.message.internal.ChatConnectMessageItem
import com.flutterup.app.screen.chat.component.message.internal.ChatGiftMessageItem
import com.flutterup.app.screen.chat.component.message.internal.ChatMediaMessageItem
import com.flutterup.app.screen.chat.component.message.internal.ChatRecallMessageItem
import com.flutterup.app.screen.chat.component.message.internal.ChatTextMessageItem
import com.flutterup.app.screen.chat.component.message.internal.ChatUnsupportedMessageItem
import com.flutterup.app.utils.chat.message.isSendError
import com.flutterup.app.utils.chat.message.sendErrorReason
import com.flutterup.base.utils.DateUtils
import com.flutterup.chat.message.content.ConnectBaseMessageContent
import com.flutterup.chat.message.content.GiftMessageContent
import com.flutterup.chat.message.content.MultiPrivateMessageContent
import com.flutterup.chat.message.content.PrivateMessageContent
import com.flutterup.chat.message.content.PublicMessageContent
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.message.ImageMessage
import io.rong.message.RecallCommandMessage
import io.rong.message.RecallNotificationMessage
import io.rong.message.SightMessage
import io.rong.message.TextMessage
import java.util.concurrent.TimeUnit

@Composable
internal fun ChatMessageItem(
    mine: UserInfo?,
    other: UserInfo?,
    previousMessage: Message?,
    message: Message,
    onProfileClick: () -> Unit = {},
    onResendClick: (Message) -> Unit = {},
    onMediaClick: (Message) -> Unit = {},
    onMediaExpired: (Message) -> Unit = {},
    onGiftClick: (Message) -> Unit = {},
) {
    val isShowTime = isShowTime(previousMessage, message)
    val errorReason = message.sendErrorReason()

    Column(
        modifier = Modifier.fillMaxWidth().wrapContentHeight()
    ) {
        if (isShowTime) {
            Text(
                text = DateUtils.timestampToDetail(message.sentTime),
                style = TextStyle(
                    fontSize = 12.sp,
                    lineHeight = 16.sp,
                    fontWeight = FontWeight.W900,
                    color = TextBlack.copy(alpha = 0.4f),
                ),
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )

            Spacer(Modifier.height(10.dp))
        }

        when(val content = message.content) {
            is ConnectBaseMessageContent -> ChatConnectMessageItem(mine, other, Modifier.align(Alignment.CenterHorizontally))

            is TextMessage -> ChatTextMessageItem(mine, other, message, content, onResendClick, onProfileClick)

            is ImageMessage,
            is SightMessage,
            is PublicMessageContent,
            is PrivateMessageContent,
            is MultiPrivateMessageContent -> {
                ChatMediaMessageItem(mine, other, message, content, onResendClick, onProfileClick, onMediaClick, onMediaExpired)
            }

            is GiftMessageContent -> {
                ChatGiftMessageItem(mine, other, message, content, onResendClick, onProfileClick, onGiftClick)
            }

            is RecallCommandMessage,
            is RecallNotificationMessage -> {
                ChatRecallMessageItem()
            }


            else -> {
                ChatUnsupportedMessageItem(mine, other, message, onResendClick, onProfileClick)
            }
        }

        if (message.isSendError() && errorReason != null) {
            Spacer(Modifier.height(10.dp))

            Text(
                text = errorReason,
                style = TextStyle(
                    fontSize = 12.sp,
                    lineHeight = 16.sp,
                    fontWeight = FontWeight.W900,
                    color = TextBlack.copy(alpha = 0.4f),
                ),
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
        }
    }
}

/**
 * 是否展示时间, 他要满足以下任意条件
 * 1. 不是同一天
 * 2. 同一天，但是时间间隔大于3分钟
 */
private fun isShowTime(previousMessage: Message?, message: Message): Boolean {
    if (previousMessage == null) return false

    val previousTime = previousMessage.sentTime
    val currentTime = message.sentTime

    val isSameDay = DateUtils.isSameDay(previousTime, currentTime)

    if (!isSameDay) return true //非同一天，直接返回

    return !DateUtils.belowTimeInterval(previousTime, currentTime, 180, TimeUnit.SECONDS)
}

@Preview(showBackground = true)
@Composable
private fun ChatMessageItemPreview() {
    val mine = UserInfo(userId = "1")
    val other = UserInfo(userId = "2")

    val textMessage = TextMessage.obtain("11111")
    val previousMessage = Message.obtain("", Conversation.ConversationType.PRIVATE, textMessage)
    val message = Message.obtain("", Conversation.ConversationType.PRIVATE, textMessage)

    previousMessage.sentTime = System.currentTimeMillis() - 1000 * 60 * 5
    message.sentTime = System.currentTimeMillis()
    message.sentStatus = Message.SentStatus.FAILED

    ChatMessageItem(
        mine = mine,
        other = other,
        previousMessage = previousMessage,
        message = message,
        onProfileClick = {},
        onResendClick = {},
        onMediaClick = {},
        onMediaExpired = {},
        onGiftClick = {},
    )
}