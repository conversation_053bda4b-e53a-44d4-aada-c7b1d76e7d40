package com.flutterup.app.screen.common.vm

import com.flutterup.app.model.UserActionType
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class ProfileSharedViewModel @Inject constructor() : BaseViewModel() {

    var sharedCurrentPage = MutableStateFlow(0)
        private set

    private val actionEventChannel = Channel<ActionEvent>(Channel.Factory.BUFFERED)
    val actionEvent = actionEventChannel.receiveAsFlow()

    fun sendSharedCurrentPage(currentPage: Int) {
        sharedCurrentPage.update { currentPage }
    }

    fun sendActionEvent(userId: String, type: UserActionType) {
        actionEventChannel.trySend(ActionEvent(userId, type))
    }

    override fun onCleared() {
        super.onCleared()
        sharedCurrentPage.update { 0 }
    }

    data class ActionEvent(
        val userId: String,
        val type: UserActionType,
    )
}