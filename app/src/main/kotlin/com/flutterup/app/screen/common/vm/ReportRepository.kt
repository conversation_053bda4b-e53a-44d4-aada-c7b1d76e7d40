package com.flutterup.app.screen.common.vm

import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.ReportItem
import com.flutterup.app.network.ApiService
import com.flutterup.base.BaseRepository
import javax.inject.Inject

class ReportRepository @Inject constructor(
    private val apiService: ApiService,
) : BaseRepository() {

    suspend fun reportUser(
        targetUserId: String,
        reportItem: ReportItem,
        from: AppFrom
    ): Boolean {
        val result = apiService.reportUser(targetUserId, reportItem.id, from.value)
        return result.isSuccess
    }
}