@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.chat

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.fromHtml
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.AppDefaultNavigationIcon
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppLoadMoreIndicator
import com.flutterup.app.design.component.AppPlaceholder
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.TextBlack333
import com.flutterup.app.design.theme.TextGray999
import com.flutterup.app.model.UserInfo
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.chat.state.ChatSystemMessageUiState
import com.flutterup.app.screen.chat.vm.ChatSystemMessageViewModel
import com.flutterup.app.utils.chat.ChatMessageUtils
import com.flutterup.app.utils.chat.ChatUserCache
import com.flutterup.base.compose.refresh.RefreshLoadMoreLazyColumn
import com.flutterup.base.compose.refresh.rememberPullToLoadMoreState
import com.flutterup.base.utils.DateUtils
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.message.TextMessage

@Composable
fun ChatSystemMessageScreen() {
    val lifecycleOwner = LocalLifecycleOwner.current
    val navController = LocalNavController.current
    val viewModel = hiltViewModel<ChatSystemMessageViewModel>()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                viewModel.refreshSilence()
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }


    ChatSystemMessageContent(
        uiState = uiState,
        onBackClick = navController::popBackStack,
        onRefresh = { viewModel.refresh() },
        onLoadMore = { viewModel.loadMore() },
    )
}

@Composable
private fun ChatSystemMessageContent(
    uiState: ChatSystemMessageUiState,
    onBackClick: () -> Unit = {},
    onRefresh: () -> Unit = {},
    onLoadMore: () -> Unit = {},
) {
    val pullToLoadMoreState = rememberPullToLoadMoreState()

    var userinfo by remember { mutableStateOf<UserInfo?>(null) }
    LaunchedEffect(uiState.conversation?.targetId) {
        val targetId = uiState.conversation?.targetId

        if (!targetId.isNullOrEmpty()) {
            userinfo = ChatUserCache.getUserInfoOrFetch(targetId)
        }
    }

    AppScaffold(
        title = { },
        navigation = {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                AppDefaultNavigationIcon(canGoBack = true, onBackClick = onBackClick)

                Text(
                    text = stringResource(R.string.system_message),
                    style = TextStyle(
                        fontSize = 17.sp,
                        lineHeight = 21.sp,
                        fontWeight = FontWeight.W800,
                        fontStyle = FontStyle.Italic,
                        color = Color.White,
                    )
                )
            }
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) {
        Image(
            painter = painterResource(R.mipmap.ic_common_top_bg),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier.fillMaxWidth()
        )

        RefreshLoadMoreLazyColumn(
            modifier = Modifier
                .padding(it)
                .fillMaxSize(),
            isRefreshing = uiState.isRefreshing,
            isLoadingMore = uiState.isLoadingMore,
            hasNoMoreData = uiState.hasNoMoreData,
            onRefresh = onRefresh,
            onLoadMore = onLoadMore,
            pullLoadMoreState = pullToLoadMoreState,
            verticalArrangement = Arrangement.spacedBy(15.dp),
            contentPadding = PaddingValues(start = 19.dp, end = 16.dp),
            loadMoreIndicator = {
                AppLoadMoreIndicator(
                    isLoadingMore = uiState.isLoadingMore,
                    hasNoMoreData = uiState.hasNoMoreData,
                    state = pullToLoadMoreState
                )
            },
            placeholderEnable = uiState.messages.isEmpty(),
            placeholder = {
                AppPlaceholder(modifier = Modifier.fillMaxSize())
            }
        ) {
            items(uiState.messages, key = { message -> message.messageId }) { message ->
                ChatSystemMessageItem(
                    userinfo = userinfo,
                    message = message,
                )
            }
        }
    }
}

@Composable
private fun ChatSystemMessageItem(
    userinfo: UserInfo?,
    message: Message,
) {
    Column(
        Modifier
            .border(width = 1.dp, color = Color(0x1AB998F9), shape = SHAPE)
            .fillMaxWidth()
            .wrapContentWidth()
            .background(color = Color.White, shape = SHAPE)
    ) {
        Row(
            modifier = Modifier
                .background(Color(0x1AA081D5), TOP_SHAPE)
                .fillMaxWidth()
                .height(48.dp)
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            AsyncImage(
                model = userinfo?.headImage,
                contentDescription = null,
                contentScale = ContentScale.None,
                modifier = Modifier
                    .size(24.dp)
                    .clip(CircleShape)
            )

            Spacer(modifier = Modifier.width(8.dp))

            Text(
                text = userinfo?.nickname.orEmpty(),
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 18.sp,
                    fontWeight = FontWeight.W400,
                    color = TextBlack333,
                ),
                modifier = Modifier.weight(1f)
            )

            Text(
                text = DateUtils.timestamp2Ago(message.sentTime),
                style = TextStyle(
                    fontSize = 12.sp,
                    lineHeight = 16.sp,
                    fontWeight = FontWeight.W400,
                    color = TextGray999,
                )
            )
        }

        Text(
            text = AnnotatedString.fromHtml(ChatMessageUtils.convert(message.content)),
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight.W400,
                color = TextBlack333,
                textAlign = TextAlign.Start,
            ),
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentSize()
                .padding(horizontal = 16.dp)
                .padding(top = 12.dp, bottom = 20.dp)
        )
    }
}

@Stable
private val CORNER_SIZE = 12.dp

@Stable
private val SHAPE = RoundedCornerShape(CORNER_SIZE)

@Stable
private val TOP_SHAPE = RoundedCornerShape(topStart = CORNER_SIZE, topEnd = CORNER_SIZE)

@Preview
@Composable
private fun ChatSystemMessageScreenPreview() {
    AppTheme {
        ChatSystemMessageContent(ChatSystemMessageUiState(
            messages = listOf(
                Message.obtain("", Conversation.ConversationType.PRIVATE, TextMessage.obtain("11111")),
                Message.obtain("", Conversation.ConversationType.PRIVATE, TextMessage.obtain("22222")),
                Message.obtain("", Conversation.ConversationType.PRIVATE, TextMessage.obtain("33333")),
                Message.obtain("", Conversation.ConversationType.PRIVATE, TextMessage.obtain("44444")),
                Message.obtain("", Conversation.ConversationType.PRIVATE, TextMessage.obtain("55555")),
                Message.obtain("", Conversation.ConversationType.PRIVATE, TextMessage.obtain("66666")),
                Message.obtain("", Conversation.ConversationType.PRIVATE, TextMessage.obtain("77777")),
            )
        ))
    }
}