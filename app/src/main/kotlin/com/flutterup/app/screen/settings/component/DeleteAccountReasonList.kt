package com.flutterup.app.screen.settings.component

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import com.flutterup.app.design.component.AppTextRadioButton
import com.flutterup.app.design.component.AppTextRadioButtonColors
import com.flutterup.app.design.component.AppTextRadioButtonDefaults
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.TextBlack333

@Composable
fun DeleteAccountReasonList(
    items: List<String>,
    modifier: Modifier = Modifier,
    selectedReasonIndex: Int = 0,
    onReasonSelected: (Int) -> Unit = {},
) {

    val itemsModifier = Modifier
        .padding(horizontal = 16.dp)
        .fillMaxWidth()
        .height(48.dp)

    val itemsColors = AppTextRadioButtonDefaults.colors(
        backgroundColor = Color.Unspecified,
        selectedBackgroundColor = Color.Unspecified,
        outlineColor = Color.Unspecified,
        selectedOutlineColor = Color.Unspecified,
        textColor = TextBlack333,
        selectedTextColor = TextBlack333,
    )
    
    val iconSize = DpSize(16.dp, 16.dp)

    LazyColumn(
        modifier = modifier.selectableGroup(),
    ) {
        items(items) {
            DeleteAccountReasonItem(
                text = it,
                colors = itemsColors,
                iconSize = iconSize,
                modifier = itemsModifier,
                selectedReasonIndex = selectedReasonIndex,
                currentIndex = items.indexOf(it),
                onReasonSelected = onReasonSelected,
            )
        }
    }
}

@Composable
private fun DeleteAccountReasonItem(
    text: String,
    colors: AppTextRadioButtonColors,
    iconSize: DpSize,
    modifier: Modifier = Modifier,
    selectedReasonIndex: Int,
    currentIndex: Int,
    onReasonSelected: (Int) -> Unit,
) {
    AppTextRadioButton(
        selected = selectedReasonIndex == currentIndex,
        text = text,
        modifier = modifier,
        shape = RectangleShape,
        onSelectedChange = { onReasonSelected(currentIndex) },
        colors = colors,
        iconSize = iconSize
    )
}

@Preview(backgroundColor = 0xFFFFFFFF, showBackground = true)
@Composable
private fun DeleteAccountReasonListPreview() {
    AppTheme {
        DeleteAccountReasonList(
            items = listOf("1", "2", "3", "4", "5", "6", "7", "8", "9", "10"),
        )
    }
}
