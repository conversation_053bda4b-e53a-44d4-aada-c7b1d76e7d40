package com.flutterup.app.screen.payment.state

import androidx.compose.runtime.Immutable
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import com.flutterup.app.R
import com.flutterup.app.design.icons.None
import com.flutterup.app.design.icons.PacksPingChat
import com.flutterup.app.design.icons.PacksPrivatePhoto
import com.flutterup.app.design.icons.PacksPrivateVideo
import com.flutterup.billinghelper.model.PaymentPacksItem
import com.flutterup.app.model.PaymentPacksFilter
import com.flutterup.app.model.PaymentPacksFilter.ALL
import com.flutterup.app.model.PaymentPacksFilter.PHOTO
import com.flutterup.app.model.PaymentPacksFilter.VIDEO
import com.flutterup.app.model.PaymentPacksFilter.PING_CHAT
import com.flutterup.base.BaseState
import com.flutterup.base.utils.toDecimalSubstring
import org.jetbrains.annotations.TestOnly

data class PaymentPacksUiState(
    /** 当前钻石数量 */
    val diamonds: Float = 0f,

    /** 当前交换数量, 默认为1, 也可能不为1 */
    val currentExchangeQuantity: Int = 1,

    /** 当前选中item */
    val currentProductId: String? = null,

    /** 商品列表 */
    val products: List<PaymentPacksItem> = emptyList(),

    /** 排序方式 */
    val orderBy: PaymentPacksFilter = ALL,

    val description: String? = null,

    /** 是否加载中 */
    override val isLoading: Boolean = false,
) : BaseState {

    /**
     * 当前选中的商品
     */
    val currentProduct: PaymentPacksItem? = products.firstOrNull { it.id == currentProductId }

    /**
     * 是否能够减1, 选中商品并且数量大于1
     */
    val negativeEnable: Boolean = currentExchangeQuantity > PacksQuantity.MIN && currentProduct != null

    /**
     * 是否能够加1, 选中商品并且最多只能选99个
     */
    val positiveEnable: Boolean = currentProduct != null && currentExchangeQuantity < PacksQuantity.MAX

    /**
     * 需要多少钻石
     */
    val neededDiamonds: String? = calculateNeededDiamonds()

    /**
     * 根据商品类型分类
     */
    val classificationByType: Map<PaymentPacksType, List<PaymentPacksItem>> = products.groupBy { PaymentPacksType.fromValue(it.type) }

    /**
     * 根据排序方式排序
     */
    val orderedProducts: List<Pair<PaymentPacksType, List<PaymentPacksItem>>> = initializeOrderedProducts()

    /**
     * 计算总共需要多少钻石
     */
    private fun calculateNeededDiamonds(): String? {
        val everyDiamond = currentProduct?.diamond ?: return null
        if (everyDiamond <= 0) {
            return null
        }

        return (everyDiamond * currentExchangeQuantity).toDecimalSubstring()
    }

    private fun initializeOrderedProducts() = when (orderBy) {
        ALL -> listOf(
            Pair(PaymentPacksType.PrivatePhoto, classificationByType[PaymentPacksType.PrivatePhoto] ?: emptyList()),
            Pair(PaymentPacksType.PrivateVideo, classificationByType[PaymentPacksType.PrivateVideo] ?: emptyList()),
            Pair(PaymentPacksType.PingChat, classificationByType[PaymentPacksType.PingChat] ?: emptyList()),
        )
        PHOTO -> listOf(
            Pair(PaymentPacksType.PrivatePhoto, classificationByType[PaymentPacksType.PrivatePhoto] ?: emptyList())
        )
        VIDEO -> listOf(
            Pair(PaymentPacksType.PrivateVideo, classificationByType[PaymentPacksType.PrivateVideo] ?: emptyList()),
        )
        PING_CHAT -> listOf(
            Pair(PaymentPacksType.PingChat, classificationByType[PaymentPacksType.PingChat] ?: emptyList()),
        )
    }

    companion object {
        /**
         * todo 替换成非测试代码
         */
        @TestOnly
        val TEST = PaymentPacksUiState(
            diamonds = 105f,
            currentExchangeQuantity = 1,
            products = listOf(
                PaymentPacksItem(
                    id = "1",
                    diamond = 100f,
                    name = "100",
                    num = 10,
                    type = 0
                ),
                PaymentPacksItem(
                    id = "2",
                    diamond = 200f,
                    name = "200",
                    num = 20,
                    type = 0
                ),
                PaymentPacksItem(
                    id = "3",
                    diamond = 300f,
                    name = "300",
                    num = 30,
                    type = 0
                ),
                PaymentPacksItem(
                    id = "4",
                    diamond = 400f,
                    name = "400",
                    num = 40,
                    type = 1
                ),
                PaymentPacksItem(
                    id = "5",
                    diamond = 500f,
                    name = "500",
                    num = 50,
                    type = 1
                ),
                PaymentPacksItem(
                    id = "6",
                    diamond = 600f,
                    name = "600",
                    num = 60,
                    type = 1
                ),
                PaymentPacksItem(
                    id = "7",
                    diamond = 700f,
                    name = "700",
                    num = 70,
                    type = 2
                ),
                PaymentPacksItem(
                    id = "8",
                    diamond = 800f,
                    name = "800",
                    num = 80,
                    type = 2
                ),
                PaymentPacksItem(
                    id = "9",
                    diamond = 900f,
                    name = "900",
                    num = 90,
                    type = 2
                ),
            ),
            description = "Feel free to cancel your subscription whenever you like.Your subscription fee will be charged to your Google Play account and will auto-renew 24 hours before the current period ends. You can disable auto-renewal anytime in your Google Play account settings. No refunds are available once the subscription is active, so please cancel before the current period ends to avoid charges. By clicking \"Continue,\" you agree to our Privacy Policy and Terms of Service."
        )
    }
}

enum class PaymentPacksType(
    val type: Int,
    val icon: ImageVector,
    val titleResources: Int,
    val colors: PacksColors,
) {
    Unknown(-1, ImageVector.None, 0, PacksColorDefaults.unknown),
    PrivatePhoto(0, PacksPrivatePhoto, R.string.private_photo, PacksColorDefaults.privatePhoto),
    PrivateVideo(1, PacksPrivateVideo, R.string.private_video, PacksColorDefaults.privateVideo),
    PingChat(2, PacksPingChat, R.string.ping_chat, PacksColorDefaults.pingChat);


    companion object {
        fun fromValue(type: Int?): PaymentPacksType {
            return entries.firstOrNull { it.type == type } ?: Unknown
        }
    }
}

object PacksQuantity {
    const val MIN = 1

    const val MAX = 99

    val RANGE = IntRange(MIN, MAX)
}

@Immutable
class PacksColors
internal constructor(
    val topCardBackground: Color,
    val packsBackground: Brush,
    val textColor: Color,
    val labelColor: Color,
    val outlineColor: Color = textColor,
)

private object PacksColorDefaults {
    val unknown = PacksColors(
        topCardBackground = Color.Unspecified,
        packsBackground = Brush.verticalGradient(listOf(Color.Unspecified)),
        textColor = Color.Unspecified,
        labelColor = Color.Unspecified,
    )

    val privatePhoto = PacksColors(
        topCardBackground = Color(0xFFFFF1E4),
        packsBackground = Brush.verticalGradient(
            colors = listOf(Color(0xFFFFDEAB), Color(0xFFFFE3BB))
        ),
        textColor = Color(0xFFD27B57),
        labelColor = Color(0xFFFFDEAB),
    )

    val privateVideo = PacksColors(
        topCardBackground = Color(0xFFEEE4FF),
        packsBackground = Brush.verticalGradient(
            colors = listOf(Color(0xFFC2ABFF), Color(0xFFE4BBFF))
        ),
        textColor = Color(0xFF680AB2),
        labelColor = Color(0xFFC2ABFF),
    )

    val pingChat = PacksColors(
        topCardBackground = Color(0xFFE4E9FF),
        packsBackground = Brush.verticalGradient(
            colors = listOf(Color(0xFFA9B9FF), Color(0xFFD5DFFF))
        ),
        textColor = Color(0xFF4040FF),
        labelColor = Color(0xFFA9B9FF),
    )
}