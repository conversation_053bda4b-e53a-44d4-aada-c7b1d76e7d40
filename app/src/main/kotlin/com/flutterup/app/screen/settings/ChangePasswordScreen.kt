@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.settings

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.component.AppTitleText
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.WhiteTextFieldColors
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.login.content.LoginEmailPwdTextField
import com.flutterup.app.screen.settings.state.ChangePwdState
import com.flutterup.app.screen.settings.vm.ChangePwdViewModel


@Composable
fun ChangePasswordScreen() {
    val navController = LocalNavController.current
    val viewModel: ChangePwdViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    ChangePasswordContent(
        uiState = uiState,
        onBackClick = navController::popBackStack,
        onPasswordChange = { viewModel.updatePassword(it) },
        onPasswordVisibilityClick = { viewModel.togglePasswordVisibility() },
        onConfirmPasswordChange = { viewModel.updateConfirmedPassword(it) },
        onConfirmPasswordVisibilityClick = { viewModel.toggleConfirmedPasswordVisibility() },
        onSubmitClick = { viewModel.submit() }
    )
}

@Composable
private fun ChangePasswordContent(
    uiState: ChangePwdState,
    onBackClick: () -> Unit = {},
    onPasswordChange: (String) -> Unit = {},
    onPasswordVisibilityClick: () -> Unit = {},
    onConfirmPasswordChange: (String) -> Unit = {},
    onConfirmPasswordVisibilityClick: () -> Unit = {},
    onSubmitClick: () -> Unit = {},
) {
    AppScaffold(
        title = { AppTitleText(stringResource(R.string.change_pwd)) },
        onBackClick = onBackClick,
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent),
        modifier = Modifier.fillMaxSize()
    ) { paddingValues ->
        Box(Modifier.fillMaxSize()) {
            Image(
                painter = painterResource(R.mipmap.ic_common_top_bg),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth().align(Alignment.TopCenter)
            )

            Box(
                modifier = Modifier
                    .padding(paddingValues)
                    .padding(top = 16.dp).fillMaxSize()
            ) {
                Column(
                     modifier = Modifier.align(Alignment.TopCenter),
                    verticalArrangement = Arrangement.spacedBy(10.dp)
                ) {
                    LoginEmailPwdTextField(
                        value = uiState.password,
                        keyboardType = KeyboardType.Password,
                        onValueChange = onPasswordChange,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 18.dp),
                        isError = !uiState.isPasswordValid,
                        isSecurity = !uiState.isPasswordVisible,
                        placeholder = R.string.new_password,
                        supportingText = R.string.login_password_format_error,
                        trailingIcon = if (uiState.isPasswordVisible) R.drawable.ic_password_shown else R.drawable.ic_password_hidden,
                        onTrailingIconClick = onPasswordVisibilityClick,
                        shape = RoundedCornerShape(16.dp),
                        colors = WhiteTextFieldColors
                    )

                    LoginEmailPwdTextField(
                        value = uiState.confirmedPassword,
                        keyboardType = KeyboardType.Password,
                        onValueChange = onConfirmPasswordChange,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 18.dp),
                        isError = !uiState.isConfirmPasswordValid,
                        isSecurity = !uiState.isConfirmedPasswordVisible,
                        placeholder = R.string.confirm_new_password,
                        supportingText = R.string.login_password_format_error,
                        trailingIcon = if (uiState.isConfirmedPasswordVisible) R.drawable.ic_password_shown else R.drawable.ic_password_hidden,
                        onTrailingIconClick = onConfirmPasswordVisibilityClick,
                        shape = RoundedCornerShape(16.dp),
                        colors = WhiteTextFieldColors
                    )
                }

                AppContinueButton(
                    text = stringResource(R.string.submit),
                    enabled = !uiState.isLoading,
                    isLoading = uiState.isLoading,
                    onClick = onSubmitClick,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 32.dp)
                        .padding(horizontal = 24.dp)
                        .align(Alignment.BottomCenter)
                )
            }
        }
    }
}

@Preview
@Composable
private fun ChangePasswordScreenPreview() {
    AppTheme {
        ChangePasswordContent(ChangePwdState())
    }
}