package com.flutterup.app.screen.chat.vm

import com.flutterup.app.model.AppFrom
import com.flutterup.app.screen.chat.state.SendPingChatUiState
import com.flutterup.app.screen.profile.vm.OtherProfileRepository
import com.flutterup.app.utils.EffectDiscoverMonitor
import com.flutterup.app.utils.EffectDiscoverType
import com.flutterup.app.utils.GlobalSettingsMonitor
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SendPingChatViewModel @Inject constructor(
    private val otherProfileRepository: OtherProfileRepository,
    private val globalSettingsMonitor: GlobalSettingsMonitor,
    private val chatMatchRepository: ChatMatchRepository,
    private val effectDiscoverMonitor: EffectDiscoverMonitor,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(SendPingChatUiState())

    val uiState: StateFlow<SendPingChatUiState> = combine(
        _uiState,
        globalSettingsMonitor.greetingList,
    ) { ui, greetingList ->
        ui.copy(greetingList = greetingList.orEmpty())
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value
    )

    fun init(targetId: String) {
        scope.launch {
            val userinfo = otherProfileRepository.getOtherProfileInfo(targetId)
            _uiState.update {
                it.copy(
                    targetId = targetId,
                    userInfo = userinfo
                )
            }
        }
    }

    fun sendPingChat(text: String, from: AppFrom, onSuccess: () -> Unit = {}) {
        val targetId = uiState.value.targetId ?: return

        scope.launch {
            _uiState.update { it.copy(isLoading = true) }
            val isSuccess = chatMatchRepository.sendInstanceMessage(
                userId = targetId,
                message = text,
                from = from,
            )

            if (isSuccess) {
                effectDiscoverMonitor.postEffect(targetId, EffectDiscoverType.Pingchat)
                onSuccess()
            }
        }.invokeOnCompletion {
            _uiState.update { it.copy(isLoading = false) }
        }
    }
}