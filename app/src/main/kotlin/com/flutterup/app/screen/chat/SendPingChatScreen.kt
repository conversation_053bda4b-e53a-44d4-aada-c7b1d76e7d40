@file:OptIn(ExperimentalMaterial3Api::class, ExperimentalSharedTransitionApi::class)

package com.flutterup.app.screen.chat

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedContentScope
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionLayout
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.UserInfo
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.chat.component.ChatShortcutGreetingInput
import com.flutterup.app.screen.chat.state.SendPingChatUiState
import com.flutterup.app.screen.chat.vm.SendPingChatViewModel

@Composable
fun SendPingChatScreen(
    targetId: String,
    from: AppFrom,
    sharedTransitionScope: SharedTransitionScope,
    animatedContentScope: AnimatedContentScope,
) {
    val navController = LocalNavController.current

    val viewModel = hiltViewModel<SendPingChatViewModel>()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(targetId) {
        viewModel.init(targetId)
    }

    SendPingChatContent(
        uiState = uiState,
        onBackClick = navController::popBackStack,
        sharedTransitionScope = sharedTransitionScope,
        animatedContentScope = animatedContentScope,
        onSendClick = {
            viewModel.sendPingChat(it, from) {
                navController.popBackStack()
            }
        }
    )
}

@Composable
private fun SendPingChatContent(
    uiState: SendPingChatUiState,
    sharedTransitionScope: SharedTransitionScope,
    animatedContentScope: AnimatedContentScope,
    onBackClick: () -> Unit = {},
    onSendClick: (String) -> Unit = {},
) {
    AppScaffold(
        title = { },
        navigation = { },
        rightNavigationContent = {
            Icon(
                painter = painterResource(R.drawable.ic_white_close),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier
                    .size(50.dp)
                    .noRippleClickable(onClick = onBackClick)
                    .padding(15.dp)
            )
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) {
        Image(
            painter = painterResource(R.mipmap.ic_fullscreen_bg),
            contentDescription = null,
            contentScale = ContentScale.FillBounds,
            modifier = Modifier.fillMaxWidth()
        )

        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
                .padding(top = 60.dp)
                .padding(horizontal = 20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceBetween,
        ) {
            UserInfoItem(
                uiState = uiState,
                sharedTransitionScope = sharedTransitionScope,
                animatedContentScope = animatedContentScope,
            )

            PingchatInput(
                uiState = uiState,
                onSendClick = onSendClick,
            )
        }
    }
}

@Composable
private fun UserInfoItem(
    uiState: SendPingChatUiState,
    sharedTransitionScope: SharedTransitionScope,
    animatedContentScope: AnimatedContentScope,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        uiState.userInfo?.headImage?.let { url ->
            Box {
                with(sharedTransitionScope) {
                    AsyncImage(
                        model = url,
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .border(1.dp, Color.White, CircleShape)
                            .padding(1.dp)
                            .sharedElement(
                                sharedContentState = sharedTransitionScope.rememberSharedContentState(url),
                                animatedVisibilityScope = animatedContentScope
                            )
                            .size(140.dp)
                            .clip(CircleShape)
                    )
                }

                Box(
                    modifier = Modifier
                        .padding(top = 8.dp)
                        .border(1.dp, Color.White, CircleShape)
                        .padding(1.dp)
                        .size(32.dp)
                        .align(Alignment.BottomEnd)
                ) {
                    Icon(
                        painter = painterResource(R.drawable.ic_label_pingchat_bg),
                        contentDescription = null,
                        tint = Color.Unspecified,
                        modifier = Modifier.matchParentSize()
                    )

                    Icon(
                        painter = painterResource(R.drawable.ic_label_ping_chat),
                        contentDescription = null,
                        tint = Color.Unspecified,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }
        }

        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(top = 8.dp)
        ) {
            Text(
                text = "${uiState.userInfo?.nickname},${uiState.userInfo?.age}",
                style = TextStyle(
                    fontSize = 18.sp,
                    lineHeight = 28.sp,
                    fontWeight = FontWeight.W700,
                    color = Color.White,
                ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )

            if (uiState.userInfo?.online == 1) {
                Icon(
                    painter = painterResource(R.drawable.ic_online_dot),
                    contentDescription = null,
                    tint = Color.Unspecified,
                    modifier = Modifier.padding(start = 8.dp).size(10.dp)
                )
            }
        }
    }
}


@Composable
private fun PingchatInput(
    uiState: SendPingChatUiState,
    onSendClick: (onSendClick: String) -> Unit = {},
) {
    Column(
        modifier = Modifier
            .padding(bottom = 100.dp)
            .fillMaxWidth()
            .wrapContentHeight(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {

        Box {
            Text(
                text = stringResource(R.string.ping_chat_title),
                style = TextStyle(
                    fontSize = 30.sp,
                    lineHeight = 50.sp,
                    fontWeight = FontWeight.W800,
                    fontStyle = FontStyle.Italic,
                    brush = Brush.linearGradient(
                        colors = listOf(
                            Color.White,
                            Color(0xFFFFE0FC),
                            Color.White
                        )
                    ),
                    drawStyle = Stroke(with(LocalDensity.current) { 2.dp.toPx() })
                )
            )

            Text(
                text = stringResource(R.string.ping_chat_title),
                style = TextStyle(
                    fontSize = 30.sp,
                    lineHeight = 50.sp,
                    fontWeight = FontWeight.W800,
                    fontStyle = FontStyle.Italic,
                    brush = Brush.linearGradient(
                        colors = listOf(
                            Color(0xFF7C4AB6),
                            Color(0xFFB648B7)
                        )
                    ),
                )
            )
        }

        Spacer(modifier = Modifier.height(5.dp))

        Text(
            text = stringResource(R.string.send_pint_chat_hot),
            style = TextStyle(
                fontSize = 14.sp,
                lineHeight = 18.sp,
                fontWeight = FontWeight.W400,
                color = Color(0xFF3A3D40)
            )
        )

        Spacer(modifier = Modifier.height(37.dp))

        ChatShortcutGreetingInput(
            greetingList = uiState.greetingList,
            onSendClick = onSendClick,
            isLoading = uiState.isLoading,
            refreshText = stringResource(R.string.rewrite)
        )
    }
}

@Preview
@Composable
private fun SendPingChatScreenPreview() {
    var isShow by remember { mutableStateOf(true) }

    AppTheme {
        SharedTransitionLayout {
            AnimatedContent(
                isShow,
                label = "test"
            ) { show ->
                if (show) {
                    SendPingChatContent(
                        uiState = SendPingChatUiState(
                            userInfo = UserInfo(
                                userId = "1",
                                headImage = "https://s3-test.aspirechatapp.com/user/1000133/022b2bc1ac227cc351f3cb7d3acc91be.jpeg",
                                nickname = "jkasdnjkankjasnjkasnsjkadnkjsnjaksnaknasdkjdsansdn1",
                                age = 18,
                                online = 1
                            )
                        ),
                        sharedTransitionScope = this@SharedTransitionLayout,
                        animatedContentScope = this,
                    )
                }
            }
        }
    }
}