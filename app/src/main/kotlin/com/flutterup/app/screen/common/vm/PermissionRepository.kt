package com.flutterup.app.screen.common.vm

import com.flutterup.app.network.ApiService
import com.flutterup.base.BaseRepository
import javax.inject.Inject

class PermissionRepository @Inject constructor(
    private val apiService: ApiService,
) : BaseRepository() {

    suspend fun upgradePermissionStatus(
        locationStatus: Int? = null,
        noticeStatus: Int? = null,
        states: String? = null,
        longitude: Double? = null,
        latitude: Double? = null,
        country: String? = null,
        timeZero: String? = null,
    ): Boolean {
        val result = apiService.upgradePermissionStatus(
            locationStatus = locationStatus,
            noticeStatus = noticeStatus,
            states = states,
            longitude = longitude,
            latitude = latitude,
            country = country,
            timeZero = timeZero,
        )

        return result.isSuccess
    }

    companion object {
        const val PERMISSION_ON = 1
        const val PERMISSION_OFF = 0
        const val PERMISSION_STAY = -1
    }
}