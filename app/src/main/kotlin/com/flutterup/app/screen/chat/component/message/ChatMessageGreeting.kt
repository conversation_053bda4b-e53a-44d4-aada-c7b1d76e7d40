package com.flutterup.app.screen.chat.component.message

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.PurpleTertiaryContainer
import com.flutterup.app.design.theme.TextBlack
import com.flutterup.app.model.GreetingItem
import kotlin.math.min


@Composable
fun ChatMessageGreeting(
    greetings: List<GreetingItem>,
    modifier: Modifier = Modifier,
    onGreetingClick: (GreetingItem) -> Unit = {},
) {
    val randomGreetings = greetings.shuffled().take(min(3, greetings.size))

    Column(
        modifier = modifier.background(
            color = Color.White,
            shape = RoundedCornerShape(22.dp)
        )
    ) {
        Row(
            modifier = Modifier
                .padding(horizontal = 13.dp)
                .padding(top = 7.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_chat_greeting),
                contentDescription = null,
                tint = Color.Unspecified
            )

            Spacer(Modifier.width(6.dp))

            Text(
                text = stringResource(R.string.chat_greet_notice),
                style = TextStyle(
                    fontSize = 10.sp,
                    lineHeight = 26.sp,
                    fontWeight = FontWeight.W900,
                    color = TextBlack,
                )
            )
        }

        LazyColumn(
            modifier = Modifier.padding(top = 5.dp, bottom = 9.dp),
            contentPadding = PaddingValues(start = 8.dp, end = 13.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(randomGreetings) { greeting ->
                Row(
                    modifier = Modifier
                        .background(color = PurpleTertiaryContainer, shape = RoundedCornerShape(size = 12.dp))
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .padding(start = 12.dp, end = 9.dp, top = 8.dp, bottom = 7.dp)
                        .noRippleClickable(onClick = { onGreetingClick(greeting) }),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = greeting.title.orEmpty(),
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight.W400,
                            color = Color(0xFF63275C),
                        ),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier
                            .padding(end = 5.dp)
                            .weight(1f)
                    )

                    Icon(
                        painter = painterResource(R.drawable.ic_chat_greeting_send),
                        contentDescription = null,
                        tint = Color.Unspecified,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
        }
    }
}


@Preview
@Composable
private fun ChatMessageGreetingPreview() {
    ChatMessageGreeting(
        greetings = List(3) {
            GreetingItem(
                title = "testanaskjdnsakjansjksandjksnadksdndjasnadsdknkjnkjasdnk$it",
                id = it.toLong(),
            )
        },
        modifier = Modifier
            .padding(horizontal = 28.dp)
            .padding(bottom = 28.dp)
            .fillMaxWidth()
    )
}