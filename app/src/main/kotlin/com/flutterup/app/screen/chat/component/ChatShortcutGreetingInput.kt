package com.flutterup.app.screen.chat.component

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.component.AppTextField
import com.flutterup.app.design.modifiers.angledGradientBackground
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppButtonPrimaryColors
import com.flutterup.app.design.theme.TextBlack666
import com.flutterup.app.design.theme.TextGray999
import com.flutterup.app.design.theme.WhiteTextFieldColors
import com.flutterup.app.model.GreetingItem

@Composable
fun ChatShortcutGreetingInput(
    greetingList: List<GreetingItem>,
    modifier: Modifier = Modifier,
    refreshText: String = stringResource(R.string.new_one),
    isLoading: Boolean = false,
    onSendClick: (String) -> Unit = {},
) {
    var currentRandomText by remember(greetingList) { mutableStateOf(greetingList.randomOrNull()?.title.orEmpty()) }
    var inputValue by remember { mutableStateOf("") }

    Column(
        modifier = modifier
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color(0xFFDBBDF3),
                        Color(0xFFE5D5FF)
                    )
                ),
                shape = RoundedCornerShape(24.dp)
            ),
    ) {
        Row(
            modifier = Modifier.padding(start = 6.dp, end = 40.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_chat_greeting_shortcut),
                contentDescription = null,
                tint = Color.Unspecified
            )

            Text(
                text = currentRandomText,
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 20.sp,
                    fontWeight = FontWeight.W400,
                    color = TextBlack666,
                ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f).noRippleClickable {
                    inputValue = currentRandomText
                },
            )

            Row(
                modifier = Modifier
                    .padding(start = 10.dp)
                    .border(
                        width = 1.dp,
                        color = Color(0xFFA073C4),
                        shape = RoundedCornerShape(size = 20.dp)
                    )
                    .padding(horizontal = 5.dp)
                    .height(18.dp)
                    .wrapContentHeight()
                    .noRippleClickable(
                        onClick = {
                            currentRandomText = greetingList.random().title.orEmpty()
                        }
                    ),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_chat_greeting_shortcut_refresh),
                    contentDescription = null,
                    tint = Color.Unspecified
                )

                Text(
                    text = refreshText,
                    style = TextStyle(
                        fontSize = 10.sp,
                        lineHeight = 20.sp,
                        fontWeight = FontWeight.W400,
                        color = Color(0xFFA073C4),
                    ),
                    modifier = Modifier.padding(start = 3.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(4.dp))


        Row (
            modifier = Modifier
                .angledGradientBackground(
                    colors = listOf(
                        Color(0xFFB70FFF),
                        Color(0xFF4646FF),
                        Color(0xFF5EFFE2),
                        Color(0xFFEE7CFF),
                        Color(0xFFFF61D5),
                        Color(0xFFFF47FC)
                    ),
                    degrees = 180f,
                    cornerRadius = 24.dp
                )
                .padding(1.dp)
                .background(Color.White, RoundedCornerShape(size = 24.dp))
                .height(48.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            AppTextField(
                value = inputValue,
                onValueChange = { inputValue = it },
                modifier = Modifier
                    .weight(1f)
                    .clip(RoundedCornerShape(size = 24.dp)),
                colors = WhiteTextFieldColors,
                contentPadding = PaddingValues(horizontal = 16.dp),
                placeholder = {
                    Text(
                        text = stringResource(R.string.your_message),
                        style = TextStyle(
                            fontSize = 14.sp,
                            lineHeight = 20.sp,
                            fontWeight = FontWeight.W300,
                            color = TextGray999,
                        )
                    )
                },
                singleLine = true,
            )

            AppContinueButton(
                onClick = { onSendClick(inputValue) },
                contentPadding = PaddingValues(0.dp),
                enabled = inputValue.isNotEmpty(),
                content = {
                    Icon(
                        painter = painterResource(R.drawable.ic_chat_white_send),
                        contentDescription = null,
                        tint = Color.Unspecified
                    )
                },
                isLoading = isLoading,
                modifier = Modifier
                    .padding(end = 6.dp)
                    .size(40.dp)
                    .clip(CircleShape),
                colors = AppButtonPrimaryColors
            )
        }
    }
}

@Preview
@Composable
private fun ChatShortcutGreetingInputPreview() {
    ChatShortcutGreetingInput(
        greetingList = List(3) {
            GreetingItem(
                title = "test$it",
                id = it.toLong(),
            )
        }
    )
}