@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.chat

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.component.AppBottomSheetDialog
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.insets.safeArea
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.TextBlack666


@Composable
fun ChatMessageMoreDialog(
    blocked: Boolean = false,
    onViewProfile: () -> Unit = {},
    onBlockClick: () -> Unit = {},
    onDeleteChatClick: () -> Unit = {},
    onReportClick: () -> Unit = {},
    onCancelClick: () -> Unit = {},
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    AppBottomSheetDialog(
        isShown = true,
        onDismissRequest = onCancelClick,
        onConfirmRequest = {},
        sheetState = sheetState,
        cancelText = null,
        confirmText = null,
        modifier = Modifier.windowInsetsPadding(WindowInsets.safeArea)
    ) {
        ChatMessageMoreContent(
            blocked = blocked,
            onViewProfile = onViewProfile,
            onBlockClick = onBlockClick,
            onDeleteChatClick = onDeleteChatClick,
            onReportClick = onReportClick,
            onCancelClick = onCancelClick,
        )
    }
}

@Composable
private fun ChatMessageMoreContent(
    blocked: Boolean = false,
    onViewProfile: () -> Unit = {},
    onBlockClick: () -> Unit = {},
    onDeleteChatClick: () -> Unit = {},
    onReportClick: () -> Unit = {},
    onCancelClick: () -> Unit = {},
) {

    Box(
        modifier = Modifier
            .background(Color.White, RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
            .fillMaxWidth()
            .clip(RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
            .padding(bottom = 33.dp)
    ) {
        Column {
            Text(
                text = stringResource(R.string.chat_more_option_title),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight.W500,
                    color = TextBlack666,
                    textAlign = TextAlign.Center,
                ),
                modifier = Modifier
                    .padding(top = 30.dp)
                    .padding(horizontal = 20.dp)
                    .fillMaxWidth()
            )

            Spacer(Modifier.height(23.dp))

            Row(
                modifier = Modifier.fillMaxWidth().padding(start = 18.dp, end = 14.dp),
                horizontalArrangement = Arrangement.spacedBy(7.dp)
            ) {
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .aspectRatio(1f)
                        .background(BackgroundColor, RoundedCornerShape(16.dp))
                        .noRippleClickable(onClick = onViewProfile),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    IconContent(
                        boxBackground = Brush.verticalGradient(
                            listOf(Color(0xFF796ADE), Color(0xFFBAAFFF))
                        )
                    ) {
                        Image(
                            painter = painterResource(R.mipmap.ic_chat_more_view),
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                    }

                    Spacer(Modifier.height(4.dp))

                    Text(
                        text = stringResource(R.string.view_profile),
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight.W500,
                            color = TextBlack666,
                        )
                    )
                }

                Column(
                    modifier = Modifier
                        .weight(1f)
                        .aspectRatio(1f)
                        .background(BackgroundColor, RoundedCornerShape(16.dp))
                        .noRippleClickable(onClick = onBlockClick),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    IconContent(
                        boxBackground = Brush.verticalGradient(
                            listOf(Color(0xFFCF77E6), Color(0xFFEDC9FF))
                        )
                    ) {
                        Image(
                            painter = painterResource(R.mipmap.ic_chat_more_block),
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }

                    Spacer(Modifier.height(4.dp))

                    Text(
                        text = stringResource(if (blocked) R.string.unblock else R.string.block),
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight.W500,
                            color = TextBlack666,
                        )
                    )
                }

                Column(
                    modifier = Modifier
                        .weight(1f)
                        .aspectRatio(1f)
                        .background(BackgroundColor, RoundedCornerShape(16.dp))
                        .noRippleClickable(onClick = onDeleteChatClick),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    IconContent(
                        boxBackground = Brush.verticalGradient(
                            listOf(Color(0xFFDBA47D), Color(0xFFFFF5CB))
                        )
                    ) {
                        Image(
                            painter = painterResource(R.mipmap.ic_chat_more_delete),
                            contentDescription = null,
                            modifier = Modifier.size(15.dp)
                        )
                    }

                    Spacer(Modifier.height(4.dp))

                    Text(
                        text = stringResource(R.string.remove_chat),
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight.W500,
                            color = TextBlack666,
                        )
                    )
                }

                Column(
                    modifier = Modifier
                        .weight(1f)
                        .aspectRatio(1f)
                        .background(BackgroundColor, RoundedCornerShape(16.dp))
                        .noRippleClickable(onClick = onReportClick),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    IconContent(
                        boxBackground = Brush.verticalGradient(
                            listOf(Color(0xFF946B83), Color(0xFFE5E5E5))
                        )
                    ) {
                        Image(
                            painter = painterResource(R.mipmap.ic_chat_more_report),
                            contentDescription = null,
                            modifier = Modifier.size(13.dp)
                        )
                    }

                    Spacer(Modifier.height(4.dp))

                    Text(
                        text = stringResource(R.string.report),
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight.W500,
                            color = TextBlack666,
                        )
                    )
                }
            }

            AppContinueButton(
                onClick = onCancelClick,
                enabled = true,
                isLoading = false,
                text = stringResource(R.string.cancel),
                modifier = Modifier
                    .padding(horizontal = 24.dp)
                    .padding(top = 37.dp)
                    .fillMaxWidth()
                    .height(50.dp)
            )
        }
    }
}

@Composable
private fun IconContent(
    boxBackground: Brush,
    icon: @Composable BoxScope.() -> Unit
) {
    Box(
        modifier = Modifier
            .border(width = 0.5.dp, Color.White, RoundedCornerShape(14.dp))
            .background(boxBackground, RoundedCornerShape(14.dp))
            .size(43.dp)
    ) {
        Box(
            modifier = Modifier
                .background(
                    brush = Brush.verticalGradient(listOf(Color.White.copy(0.3f), Color.White)),
                    shape = RoundedCornerShape(4.dp)
                )
                .size(17.81.dp, 24.dp)
                .align(Alignment.Center),
            contentAlignment = Alignment.Center,
            content = icon
        )
    }
}

private val BackgroundColor = Color(0xFFF3F0FF)

@Preview
@Composable
private fun ChatMessageMorePreview() {
    Surface {
        Box(
            modifier = Modifier.fillMaxSize().background(color = Color.Gray),
            contentAlignment = Alignment.BottomCenter
        ) {
            ChatMessageMoreContent()
        }
    }
}

