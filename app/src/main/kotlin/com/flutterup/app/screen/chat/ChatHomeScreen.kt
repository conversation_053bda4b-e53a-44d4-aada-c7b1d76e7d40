@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.chat

import android.Manifest
import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Badge
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.lerp
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppAvatar
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.component.AppLoadMoreIndicator
import com.flutterup.app.design.component.AppPlaceholder
import com.flutterup.app.design.component.swipe.SwipeToRevealBox
import com.flutterup.app.design.component.swipe.SwipeToRevealBoxState
import com.flutterup.app.design.component.swipe.SwipeToRevealBoxValue
import com.flutterup.app.design.component.swipe.rememberSwipeToRevealBoxState
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.LineSecondary
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.design.theme.PurpleTertiaryContainer
import com.flutterup.app.design.theme.TextBlack151
import com.flutterup.app.design.theme.TextBlack666
import com.flutterup.app.design.theme.TextGray6f7288
import com.flutterup.app.model.UserCountEntity
import com.flutterup.app.model.UserInfo
import com.flutterup.app.screen.LocalAppState
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.LocalPermissionManager
import com.flutterup.app.screen.chat.component.ChatHomeTopRow
import com.flutterup.app.screen.chat.state.ChatConversation
import com.flutterup.app.screen.chat.state.ChatHomeUiState
import com.flutterup.app.screen.chat.vm.ChatHomeViewModel
import com.flutterup.app.screen.common.CommonDialogScreen
import com.flutterup.app.screen.relate.RelateRoute
import com.flutterup.app.screen.relate.state.Visitors
import com.flutterup.app.screen.relate.state.WinksReceived
import com.flutterup.app.utils.NotificationUtils
import com.flutterup.app.utils.chat.ChatMessageUtils
import com.flutterup.app.utils.chat.ChatUserCache
import com.flutterup.base.compose.refresh.RefreshLoadMoreLazyColumn
import com.flutterup.base.compose.refresh.rememberPullToLoadMoreState
import com.flutterup.base.permission.PermissionLimitType
import com.flutterup.base.permission.PermissionRequestConfig
import com.flutterup.base.permission.rememberPermissionState
import com.flutterup.base.utils.DateUtils
import io.rong.imlib.model.Conversation
import kotlinx.coroutines.launch

@SuppressLint("InlinedApi")
private const val permission = Manifest.permission.POST_NOTIFICATIONS

@Composable
fun ChatHomeScreen() {
    val appState = LocalAppState.current
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val navController = LocalNavController.current
    val permissionManager = LocalPermissionManager.current
    val scope = rememberCoroutineScope()

    val viewModel = hiltViewModel<ChatHomeViewModel>()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val onlineUsers by appState.onlineUsers.collectAsStateWithLifecycle()

    val permissionState = rememberPermissionState(permissionManager)
    var isGradientNotification by remember { mutableStateOf(true) }

    val showRationale = permissionState.showRationale
    var tryDeleteConversation by remember { mutableStateOf<Conversation?>(null) }

    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                isGradientNotification = permissionManager.hasPermissions(context, listOf(permission))
                if (!isGradientNotification) {
                    viewModel.resetNotificationBanner()
                }

                viewModel.refreshSilence()
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    LaunchedEffect(Unit) {
        // 权限未授权
        permissionState.requestPermissions(
            config = PermissionRequestConfig(
                permissions = listOf(permission),
                limitType = PermissionLimitType.ONCE_PER_APP,
            ),
            onGranted = {
                isGradientNotification = true
                viewModel.upgradeNotificationPermission(true)
            },
            onDenied = {
                isGradientNotification = false
                viewModel.upgradeNotificationPermission(false)
            }
        )
    }

    ChatHomeContent(
        uiState = uiState,
        onlineUsers = onlineUsers,
        isGradientNotification = isGradientNotification,
        onRefresh = { viewModel.refresh() },
        onLoadMore = { viewModel.loadMore() },
        onVisitorClick = {
            appState.navigateToTopLevelDestination(RelateRoute(index = Visitors.INDEX))
        },
        onWinksReceivedClick = {
            appState.navigateToTopLevelDestination(RelateRoute(index = WinksReceived.INDEX))
        },
        onNowConnectionsClick = {
            val firstTargetId = uiState.newConnections.firstOrNull() ?: return@ChatHomeContent
            val targetId = firstTargetId.userId ?: return@ChatHomeContent
            navController.navigate(ChatMessageRoute(targetId))
        },
        onNotificationClick = { navController.navigate(ChatSystemConversationsRoute) },
        onCloseNotificationBannerClick = {
            viewModel.closeNotificationBanner()
        },
        onSettingNotificationClick = {
            // 权限未授权
            permissionState.requestPermissions(
                permissions = listOf(permission),
                onGranted = {
                    isGradientNotification = true
                    viewModel.upgradeNotificationPermission(true)
                },
                onDenied = {
                    isGradientNotification = false
                    viewModel.upgradeNotificationPermission(false)

                    if (it.permanentlyDeniedPermissions.isNotEmpty()) { //权限被永久拒绝了, 跳转设置
                        NotificationUtils.openNotificationSettings(context)
                    }
                }
            )
        },
        onTopClick = { swipeToRevealBoxState, conversation ->
            viewModel.topOrUntopConversation(conversation) {
                scope.launch { swipeToRevealBoxState.reset() }
            }
        },
        onDeleteClick = { swipeToRevealBoxState, conversation ->
            tryDeleteConversation = conversation
            scope.launch { swipeToRevealBoxState.reset() }
        },
        onConversationClick = { conversation ->
            navController.navigate(ChatMessageRoute(conversation.targetId))
        }
    )

    if (showRationale) {
        CommonDialogScreen(
            title = stringResource(R.string.enable_notifications),
            content = stringResource(R.string.enable_notifications_desc),
            positiveText = stringResource(R.string.ok),
            negativeText = stringResource(R.string.cancel),
            onPositiveClick = permissionState::proceedWithPermissionRequest,
            onNegativeClick = permissionState::cancelPermissionRequest,
            onDismissRequest = permissionState::cancelPermissionRequest,
        )
    }

    if (tryDeleteConversation != null) {
        CommonDialogScreen(
            title = stringResource(R.string.delete_conversation),
            content = stringResource(R.string.delete_conversation_desc),
            positiveText = stringResource(R.string.confirm),
            negativeText = stringResource(R.string.cancel),
            onPositiveClick = {
                tryDeleteConversation?.let {
                    viewModel.deleteConversation(it)
                }
            },
            onNegativeClick = { tryDeleteConversation = null },
            onDismissRequest = { tryDeleteConversation = null },
        )
    }
}


@Composable
private fun ChatHomeContent(
    uiState: ChatHomeUiState,
    onlineUsers: List<String>,
    isGradientNotification: Boolean,
    onNotificationClick: () -> Unit = {},
    onCloseNotificationBannerClick: () -> Unit = {},
    onSettingNotificationClick: () -> Unit = {},
    onRefresh: () -> Unit = {},
    onLoadMore: () -> Unit = {},
    onVisitorClick: () -> Unit = {},
    onWinksReceivedClick: () -> Unit = {},
    onNowConnectionsClick: () -> Unit = {},
    onTopClick: (SwipeToRevealBoxState, Conversation) -> Unit = { _, _ -> },
    onDeleteClick: (SwipeToRevealBoxState, Conversation) -> Unit = { _, _ -> },
    onConversationClick: (Conversation) -> Unit = {},
) {
    val pullToLoadMoreState = rememberPullToLoadMoreState()

    AppScaffold(
        title = {},
        canGoBack = false,
        navigation = {
            Text(
                text = stringResource(R.string.message),
                style = TextStyle(
                    fontSize = 24.sp,
                    lineHeight = 28.sp,
                    fontWeight = FontWeight.W800,
                    fontStyle = FontStyle.Italic,
                    color = Color.White,
                ),
                modifier = Modifier.padding(start = 17.dp)
            )
        },
        rightNavigationContent = {
            Box(
                modifier = Modifier.padding(end = 17.dp)
                    .noRippleClickable(onClick = onNotificationClick)
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_settings_notification),
                    contentDescription = null,
                    tint = Color.Unspecified,
                    modifier = Modifier.size(20.dp)
                )

                if (uiState.totalSystemMessagesCount > 0) {
                    Badge(
                        containerColor = Color.Red,
                        contentColor = Color.White,
                        modifier = Modifier.size(8.dp).align(Alignment.TopEnd)
                    )
                }
            }
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) { paddingValues ->
        Image(
            painter = painterResource(R.mipmap.ic_common_top_bg),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier.fillMaxWidth()
        )

        Column (
            modifier = Modifier.padding(paddingValues)
        ) {
            ChatHomeTopRow(
                uiState = uiState,
                modifier = Modifier.padding(start = 15.dp, end = 13.dp),
                onVisitorClick = onVisitorClick,
                onWinksReceivedClick = onWinksReceivedClick,
                onNowConnectionsClick = onNowConnectionsClick,
            )

            Spacer(Modifier.height(20.dp))

            if (!isGradientNotification && !uiState.isCloseNotificationBanner) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(30.dp)
                        .background(color = PurpleTertiaryContainer)
                        .padding(horizontal = 20.dp),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Icon(
                        painter = painterResource(R.drawable.ic_white_close),
                        contentDescription = null,
                        tint = PurplePrimary,
                        modifier = Modifier.size(16.dp)
                            .noRippleClickable(onClick = onCloseNotificationBannerClick),
                    )

                    Spacer(Modifier.width(5.dp))

                    Text(
                        text = stringResource(R.string.enable_notification_banner),
                        style = TextStyle(
                            fontSize = 10.sp,
                            lineHeight = 16.sp,
                            fontWeight = FontWeight.W400,
                            color = TextGray6f7288,
                        ),
                        modifier = Modifier.weight(1f)
                    )

                    AppContinueButton(
                        onClick = onSettingNotificationClick,
                        modifier = Modifier.size(60.dp, 22.dp),
                        contentPadding = PaddingValues(0.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.enable_notification_banner_btn),
                            style = TextStyle(
                                fontSize = 10.sp,
                                lineHeight = 16.sp,
                                fontWeight = FontWeight.W400,
                                color = Color.White,
                            ),
                        )
                    }
                }
            }

            RefreshLoadMoreLazyColumn(
                isRefreshing = uiState.isRefreshing,
                isLoadingMore = uiState.isLoadingMore,
                hasNoMoreData = uiState.hasNoMoreData,
                onRefresh = onRefresh,
                onLoadMore = onLoadMore,
                pullLoadMoreState = pullToLoadMoreState,
                loadMoreIndicator = {
                    AppLoadMoreIndicator(
                        isLoadingMore = uiState.isLoadingMore,
                        hasNoMoreData = uiState.hasNoMoreData,
                        state = pullToLoadMoreState
                    )
                },
                placeholderEnable = uiState.conversations.isEmpty(),
                placeholder = {
                    AppPlaceholder(modifier = Modifier.fillMaxSize())
                }
            ) {
                items(uiState.conversations, key = { it.conversation.targetId }) {
                    ConversationItem(
                        conversation = it.conversation,
                        existUnreadReceivedGift = it.existUnreadReceivedGift,
                        onlineUsers = onlineUsers,
                        onTopClick = onTopClick,
                        onDeleteClick = onDeleteClick,
                        onConversationClick = onConversationClick,
                    )
                }
            }
        }
    }
}


@Composable
private fun ConversationItem(
    conversation: Conversation,
    existUnreadReceivedGift: Boolean,
    onlineUsers: List<String>,
    onTopClick: (SwipeToRevealBoxState, Conversation) -> Unit = { _, _ -> },
    onDeleteClick: (SwipeToRevealBoxState, Conversation) -> Unit = { _, _ -> },
    onConversationClick: (Conversation) -> Unit = {},
) {
    var userinfo by remember { mutableStateOf<UserInfo?>(null) }

    LaunchedEffect(conversation.targetId) {
        userinfo = ChatUserCache.getUserInfoOrFetch(conversation.targetId)
    }

    val swipeToRevealBoxState = rememberSwipeToRevealBoxState()

    val colorStart = MaterialTheme.colorScheme.surface
    val colorEnd = PurpleTertiaryContainer

    SwipeToRevealBox (
        state = swipeToRevealBoxState,
        enableRevealFromStartToEnd = false,
        enableRevealFromEndToStart = true,
        revealSize = 140.dp,
        backgroundContent = {
            if (swipeToRevealBoxState.dismissDirection == SwipeToRevealBoxValue.EndToStart) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(36.dp),
                    modifier = Modifier
                        .fillMaxSize()
                        .drawBehind {
                            val isReverse = swipeToRevealBoxState.isReverse
                            
                            val color = lerp(
                                if (!isReverse) colorStart else colorEnd,
                                if (!isReverse) colorEnd else colorStart,
                                swipeToRevealBoxState.progress
                            )
                            drawRect(color)
                        }
                        .wrapContentSize(Alignment.CenterEnd)
                        .padding(horizontal = 20.dp)
                ) {
                    Icon(
                        painter = painterResource(
                            if (conversation.isTop) R.drawable.ic_conversation_untop else R.drawable.ic_conversation_top
                        ),
                        contentDescription = null,
                        tint = Color.Unspecified,
                        modifier = Modifier.size(32.dp).noRippleClickable(onClick = {
                            onTopClick(swipeToRevealBoxState, conversation)
                        })
                    )

                    Icon(
                        painter = painterResource(R.drawable.ic_conversation_delete),
                        contentDescription = null,
                        tint = Color.Unspecified,
                        modifier = Modifier.size(32.dp).noRippleClickable(onClick = {
                            onDeleteClick(swipeToRevealBoxState, conversation)
                        })
                    )
                }
            }
        }
    ) {
        Box(
            modifier = Modifier
                .background(if (conversation.isTop) lerp(colorStart, colorEnd, 0.5f) else colorStart)
                .noRippleClickable(onClick = {
                    onConversationClick(conversation)
                })
        ) {
            Row(
                modifier = Modifier
                    .padding(horizontal = 20.dp)
                    .fillMaxSize()
                    .height(75.dp),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                AppAvatar(
                    online = onlineUsers.contains(conversation.targetId),
                    onlineSize = 8.dp
                ) {
                    AsyncImage(
                        model = userinfo?.headImage,
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .clip(CircleShape)
                            .size(50.dp)
                    )
                }

                Column(
                    modifier = Modifier.weight(1f).padding(horizontal = 12.dp),
                    verticalArrangement = Arrangement.Center,
                ) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text(
                            text = userinfo?.nickname.orEmpty(),
                            style = TextStyle(
                                fontSize = 14.sp,
                                lineHeight = 16.sp,
                                fontWeight = FontWeight.W900,
                                color = TextBlack151,
                            ),
                            maxLines = 1,
                        )
                        if (userinfo?.location == 1) { //nearby
                            Icon(
                                painter = painterResource(R.drawable.ic_visitor_nearby),
                                contentDescription = null,
                                tint = Color.Unspecified,
                            )
                        }
                        if (conversation.isTop) { //是否置顶标志
                            Icon(
                                painter = painterResource(R.drawable.ic_chat_top),
                                contentDescription = null,
                                tint = Color.Unspecified,
                            )
                        }
                        if (existUnreadReceivedGift) { //是否存在未读礼物消息
                            Icon(
                                painter = painterResource(R.drawable.ic_chat_gift),
                                contentDescription = null,
                                tint = Color.Unspecified,
                                modifier = Modifier.size(16.dp).padding(bottom = 1.dp)
                            )
                        }
                    }

                    Spacer(Modifier.height(8.dp))

                    Text(
                        text = ChatMessageUtils.convert(conversation.latestMessage),
                        style = TextStyle(
                            fontSize = 12.sp,
                            lineHeight = 16.sp,
                            fontWeight = FontWeight.W400,
                            color = TextBlack666,
                        ),
                        maxLines = 1,
                    )
                }

                Column(
                    modifier = Modifier
                        .padding(top = 18.dp)
                        .fillMaxHeight(),
                    horizontalAlignment = Alignment.End
                ) {
                    Text(
                        text = DateUtils.timestamp2Ago(conversation.operationTime),
                        style = TextStyle(
                            fontSize = 12.sp,
                            lineHeight = 16.sp,
                            fontWeight = FontWeight.W400,
                            color = TextGray6f7288,
                        )
                    )

                    Spacer(Modifier.height(9.dp))

                    if (conversation.unreadMessageCount > 0) {
                        Badge(
                            containerColor = Color(0xFF988DE2),
                            contentColor = Color.White,
                        ) {
                            Text(
                                text = if (conversation.unreadMessageCount > 99) "99+" else conversation.unreadMessageCount.toString(),
                                style = TextStyle(
                                    fontSize = 10.sp,
                                    lineHeight = 16.sp,
                                    fontWeight = FontWeight.W900,
                                )
                            )
                        }
                    }
                }
            }

            HorizontalDivider(
                modifier = Modifier
                    .padding(horizontal = 20.dp)
                    .fillMaxWidth()
                    .align(Alignment.BottomStart),
                thickness = 1.dp,
                color = LineSecondary
            )
        }
    }
}

@Preview
@Composable
private fun ChatHomeScreenPreview() {
    AppTheme {
        ChatHomeContent(
            uiState = ChatHomeUiState(
                totalSystemMessagesCount = 0,
                userUnreadCount = UserCountEntity(
                    visitorNewNum = 1000,
                    headList = listOf("", "", "")
                ),
                newConnections = listOf(
                    UserInfo(userId = "1"),
                    UserInfo(userId = "2"),
                    UserInfo(userId = "3"),
                ),
                conversations = listOf(
                    ChatConversation(Conversation.obtain(Conversation.ConversationType.PRIVATE, "1", "111").apply {
                        isTop = true
                    }, existUnreadReceivedGift = true),
                    ChatConversation(Conversation.obtain(Conversation.ConversationType.PRIVATE, "2", "222"), existUnreadReceivedGift = true),
                    ChatConversation(Conversation.obtain(Conversation.ConversationType.PRIVATE, "3", "333"), existUnreadReceivedGift = true),
                )
            ),
            onlineUsers = emptyList(),
            isGradientNotification = false,
        )
    }
}