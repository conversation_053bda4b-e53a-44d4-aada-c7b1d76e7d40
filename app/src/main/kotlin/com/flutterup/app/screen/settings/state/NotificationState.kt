package com.flutterup.app.screen.settings.state

import com.flutterup.app.R
import com.flutterup.app.model.UserNotificationEntity
import com.flutterup.base.BaseState

data class NotificationState(
    val pushConfig: Int? = null,
    override val isLoading: Boolean = false
) : BaseState {

    val types = NotificationType.entries.toList()

    enum class NotificationType(val titleRes: Int, val mask: Int) {
        Message(R.string.notification_title_new_message, UserNotificationEntity.SETTINGS_NEW_MESSAGE_ALERT),

        Match(R.string.notification_title_new_connections, UserNotificationEntity.SETTINGS_NEW_CONNECTION_ALERT),

        Winks(R.string.notification_title_new_winks, UserNotificationEntity.SETTINGS_NEW_WINKS_ALERT),

        Visitor(R.string.notification_title_new_visitor, UserNotificationEntity.SETTINGS_NEW_VISITOR_ALERT);

        fun checked(pushConfig: Int?): Boolean = pushConfig != null && pushConfig.hasFlag(mask)
    }
}

private fun Int.hasFlag(mask: Int): Bo<PERSON><PERSON> {
    return this and mask != 0
}