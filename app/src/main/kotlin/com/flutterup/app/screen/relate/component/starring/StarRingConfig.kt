package com.flutterup.app.screen.relate.component.starring

import androidx.annotation.FloatRange
import androidx.compose.runtime.Stable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * 星环配置
 * @param rings 环配置
 */
data class StarRingConfig(
    val rings: List<RingConfig> = emptyList(),
)

/**
 * @param radius 环半径
 * @param broderWidth 环边框宽度
 * @param color 环颜色
 * @param maxAvatarCount 最大头像数量
 * @param avatarStartAngle 头像起始角度 0 ~ 360
 * @param avatarAngleOffset 头像角度偏移 0 ~ 360
 * @param avatarSize 头像大小
 * @param enableBlur 是否启用模糊
 * @param enableScale 是否启用缩放
 * @param avatarScale 头像缩放比例
 * @param scaleDuration 缩放持续时间
 * @param enableRotation 是否启用旋转
 * @param rotationDuration 旋转持续时间
 * @param ringStars 环上的星星
 */
data class RingConfig(
    val radius: Dp,

    val broderWidth: Dp,

    val color: Color,

    val maxAvatarCount: Int = 3,

    @FloatRange(from = 0.0, to = 360.0) val avatarStartAngle: Float = 90f,

    @FloatRange(from = 0.0, to = 360.0) val avatarAngleOffset: Float = 90f,

    val avatarSize: Dp = 33.dp,

    val enableBlur: Boolean = true,

    val enableScale: Boolean = true,

    @FloatRange(from = 1.0) val avatarScale: Float = 1.68f,

    val scaleDuration: Int = 5000, // 5秒缩放一次

    @FloatRange(from = 0.0, to = 1.0) val scaleFactor: Float = 0.3f,

    val enableRotation: Boolean = true,

    val rotationDuration: Int = 20000, // 20秒一圈

    val ringStars: List<RingStarConfig> = emptyList(),
)

/**
 * 环上的星星配置
 * @param size 星星大小
 * @param color 星星颜色
 * @param defaultAngle 默认角度, 最顶部为0度, 顺时针为正, 逆时针为负
 */
data class RingStarConfig(
    val size: Dp,

    val color: Color,

    @FloatRange(from = 0.0, to = 360.0) val defaultAngle: Float,
)

object StarRingConfigDefaults {
    private val ringConfigInner = RingConfig(
        radius = 121.dp,
        broderWidth = 40.dp,
        avatarStartAngle = 202.5f,
        color = Color(0xFFF2EDFA),
    )

    private val ringConfigOuter = RingConfig(
        radius = 176.dp,
        broderWidth = 3.dp,
        avatarStartAngle = 135f,
        maxAvatarCount = 1,
        enableScale = false,
        color = Color(0xFFFAEDF8),
        ringStars = listOf(
            RingStarConfig(16.dp, Color(0xFFF7A5A3), 330f),
            RingStarConfig(8.dp, Color(0xFF5CA6FC), 30f),
            RingStarConfig(10.dp, Color(0xFFF1D2A1), 170f)
        )
    )

    val defaults = StarRingConfig(listOf(ringConfigInner, ringConfigOuter))
}