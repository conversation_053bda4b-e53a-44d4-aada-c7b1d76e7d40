package com.flutterup.app.screen.settings.state

import com.flutterup.base.BaseState

data class ChangePwdState(
    val password: String = "",
    val isPasswordVisible: <PERSON><PERSON><PERSON> = false,

    val confirmedPassword: String = "",
    val isConfirmedPasswordVisible: <PERSON>olean = false,

    override val isLoading: <PERSON>olean = false,
) : BaseState {
    val isPasswordValid: <PERSON>olean
        get() = isPasswordValid(password)

    val isConfirmPasswordValid: <PERSON><PERSON><PERSON>
        get() = isPasswordValid(confirmedPassword)

    /**
     * 检查密码格式是否正确
     */
    private fun isPasswordValid(password: String): <PERSON><PERSON><PERSON> {
        if (password.isEmpty()) return true

        return password.length >= 8 && password.any { it.isLetter() } && password.any { it.isDigit() }
    }
}