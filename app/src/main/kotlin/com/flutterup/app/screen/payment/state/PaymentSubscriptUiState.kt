package com.flutterup.app.screen.payment.state

import com.flutterup.app.model.SubscriptionBenefitEntity
import com.flutterup.app.model.SubscriptionItem
import com.flutterup.base.BaseState

data class PaymentSubscriptUiState(

    val benefits: List<SubscriptionBenefitEntity> = emptyList(),

    val items: List<SubscriptionItem> = emptyList(),

    val selected: Int = with(items.indexOfFirst { it.hot == 1 }) { if (this == -1) 0 else this },

    val description: String? = null,

    val isVip: Boolean = false,

    override val isLoading: <PERSON>olean = false,
) : BaseState

