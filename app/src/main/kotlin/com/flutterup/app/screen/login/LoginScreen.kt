package com.flutterup.app.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.screen.login.content.LoginAgreementRadioButton
import com.flutterup.app.design.component.ktx.toHazeStyle
import com.flutterup.app.design.component.ktx.toHazeTint
import com.flutterup.app.design.haze.DefaultHazeStyle
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.modifiers.noRippleSelectable
import com.flutterup.app.design.text.buildAppAnnotatedString
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.PurpleCoverPrimary
import com.flutterup.app.screen.LocalAppState
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.developer.navigateToDeveloper
import com.flutterup.app.screen.login.content.GoogleSignInOutlinedButton
import com.flutterup.app.screen.login.helper.GoogleSignInResult
import com.flutterup.app.screen.login.vm.LoginHomeViewModel
import com.flutterup.app.screen.common.WebViewRoute
import dev.chrisbanes.haze.HazeState
import dev.chrisbanes.haze.hazeEffect
import dev.chrisbanes.haze.hazeSource
import dev.chrisbanes.haze.rememberHazeState

@Composable
fun LoginScreen() {
    val viewModel: LoginHomeViewModel = hiltViewModel()
    val navController = LocalNavController.current
    val appState = LocalAppState.current
    val terms by appState.settingsMonitor.terms.collectAsStateWithLifecycle(null)
    val privacy by appState.settingsMonitor.privacy.collectAsStateWithLifecycle(null)

    val termsTitle = stringResource(R.string.terms_policy)
    val privacyTitle = stringResource(R.string.privacy_policy)

    LoginContent(
        isAgreementPolicy = viewModel.isAgreementPolicy,
        isShowGoogleSignInDialog = viewModel.showGoogleSignInDialog,
        onLogoClick = { navController.navigateToDeveloper() },
        onGoogleButtonClick = { viewModel.checkAgreementPolicy { viewModel.showGoogleSignIn() } },
        onEmailButtonClick = { viewModel.checkAgreementPolicy { navController.navigate(LoginNormalRoute) } },
        onAgreementClick = { viewModel.rippleAgreementPolicy() },
        onAgreementTermPolicyClick = {
            terms?.let {
                navController.navigate(WebViewRoute(
                    title = termsTitle,
                    url = it
                ))
            }
        },
        onAgreementPrivacyPolicyClick = {
            privacy?.let {
                navController.navigate(WebViewRoute(
                    title = privacyTitle,
                    url = it
                ))
            }
        },
        onGoogleSignInResult = { viewModel.handleGoogleSignInResult(it) },
        onGoogleSignInDismiss = { viewModel.hideGoogleSignIn() },
    )
}

@Composable
private fun LoginContent(
    modifier: Modifier = Modifier,
    hazeState: HazeState = rememberHazeState(true),
    isAgreementPolicy: Boolean = false,
    isShowGoogleSignInDialog: Boolean = false,
    onLogoClick: () -> Unit = {},
    onGoogleButtonClick: () -> Unit = {},
    onEmailButtonClick: () -> Unit = {},
    onAgreementClick: () -> Unit = {},
    onAgreementTermPolicyClick: () -> Unit = {},
    onAgreementPrivacyPolicyClick: () -> Unit = {},
    onGoogleSignInResult: (GoogleSignInResult) -> Unit = {},
    onGoogleSignInDismiss: () -> Unit = {},
) {
    Surface(
        modifier = modifier.fillMaxSize()
    ) {
        Box(
            modifier = Modifier.fillMaxWidth()
        ) {
            LoginBackground(hazeState)

            Box(
                modifier = Modifier
                    .wrapContentSize()
                    .align(Alignment.Center)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                ) {
                    Image(
                        painter = painterResource(R.drawable.ic_logo),
                        contentDescription = null,
                        modifier = Modifier
                            .width(253.dp)
                            .height(72.dp)
                            .align(Alignment.CenterHorizontally)
                            .noRippleClickable(onClick = onLogoClick)
                    )

                    Spacer(modifier = Modifier.height(65.dp))

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .wrapContentHeight()
                            .padding(horizontal = 24.dp)
                    ) {
                        GoogleSignInOutlinedButton(
                            onClick = onGoogleButtonClick,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(50.dp)
                                .clip(ButtonDefaults.outlinedShape)
                                .hazeEffect(hazeState, DefaultHazeStyle.copy(tints = listOf(PurpleCoverPrimary.toHazeTint())))
                        )

                        Spacer(modifier = Modifier.height(20.dp))

                        OutlinedButton(
                            onClick = onEmailButtonClick,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(50.dp)
                                .clip(ButtonDefaults.outlinedShape)
                                .hazeEffect(hazeState, DefaultHazeStyle.copy(tints = listOf(PurpleCoverPrimary.toHazeTint())))
                        ) {
                            Text(
                                text = stringResource(R.string.continue_with_email),
                                style = TextStyle(
                                    fontSize = 16.sp,
                                    lineHeight = 22.sp,
                                    fontWeight = FontWeight.W900,
                                    color = Color.White,
                                    textAlign = TextAlign.Center,
                                )
                            )
                        }
                    }
                }
            }

            Box(
                modifier = Modifier
                    .wrapContentHeight()
                    .fillMaxWidth()
                    .padding(
                        horizontal = 24.dp,
                        vertical = 21.dp
                    )
                    .navigationBarsPadding()
                    .align(Alignment.BottomCenter)
            ) {
                Row(
                    modifier = Modifier
                        .noRippleSelectable(
                            selected = isAgreementPolicy,
                            onClick = onAgreementClick,
                            role = Role.RadioButton
                        )
                ) {
                    LoginAgreementRadioButton(
                        selected = isAgreementPolicy,
                        onClick = null,
                        modifier = Modifier.size(12.dp),
                    )

                    Spacer(modifier = Modifier.width(5.dp))

                    AgreementText(
                        onAgreementTermPolicyClick = onAgreementTermPolicyClick,
                        onAgreementPrivacyPolicyClick = onAgreementPrivacyPolicyClick,
                        onAgreementClick = onAgreementClick
                    )
                }
            }
        }
    }

    // Google 登录弹窗
    if (isShowGoogleSignInDialog) {
        GoogleSignInDialog(
            clientId = "lalala.apps.googleusercontent.com",
            onResult = onGoogleSignInResult,
            onDismiss = onGoogleSignInDismiss
        )
    }
}

@Composable
private fun AgreementText(
    onAgreementClick: () -> Unit = {},
    onAgreementTermPolicyClick: () -> Unit = {},
    onAgreementPrivacyPolicyClick: () -> Unit = {},
) {
    val prefix = stringResource(R.string.login_agreement_prefix)
    val termsPolicy = stringResource(R.string.terms_policy)
    val privacyPolicy = stringResource(R.string.privacy_policy)
    val delimiter = stringResource(R.string.login_agreement_delimiter)

    val annotatedString = buildAppAnnotatedString {
        appendUrl(
            text = prefix,
            url = "",
            clickable = { onAgreementClick() }
        )

        appendLinkUnderline(
            text = termsPolicy,
            tag = termsPolicy,
            clickable = { onAgreementTermPolicyClick() }
        )

        append(delimiter)

        appendLinkUnderline(
            text = privacyPolicy,
            tag = privacyPolicy,
            clickable = { onAgreementPrivacyPolicyClick() }
        )
    }

    Text(
        text = annotatedString,
        style = TextStyle(
            fontSize = 12.sp,
            fontWeight = FontWeight.W300,
            color = Color.White,
        )
    )
}

@Composable
private fun LoginBackground(state: HazeState) {
    Box(modifier = Modifier.fillMaxSize()) {
        Image(
            painter = painterResource(id = R.mipmap.ic_login_background),
            contentDescription = null,
            modifier = Modifier
                .fillMaxSize()
                .hazeSource(state, zIndex = 0f),
            contentScale = ContentScale.Crop
        )

        Spacer(
            modifier = Modifier
                .fillMaxSize()
                .background(PurpleCoverPrimary)
                .hazeSource(state, zIndex = 1f)
        )
    }
}


@Preview
@Composable
private fun LoginScreenPreview() {
    AppTheme {
        LoginContent()
    }
}