@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.profile

import android.app.Activity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.compose.currentBackStackEntryAsState
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.extension.rememberPickVisualMediaRequest
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.design.theme.TextGray999
import com.flutterup.app.model.Gender
import com.flutterup.app.screen.GlobalViewModel
import com.flutterup.app.screen.ImageCropperActivity
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.profile.component.EditProfileItem
import com.flutterup.app.screen.profile.component.EditProfilePhoto
import com.flutterup.app.screen.profile.state.ProfileEditUiState
import com.flutterup.app.screen.profile.vm.ProfileEditViewModel
import com.flutterup.app.screen.profile.vm.SharedProfileInterestsViewModel
import com.flutterup.base.utils.FileUtils
import java.io.File
import java.time.LocalDate

private const val NICKNAME_SHARED_KEY = "nickname"
private const val DESC_SHARED_KEY = "desc"

@Composable
fun ProfileEditScreen() {
    val navController = LocalNavController.current
    val context = LocalContext.current

    val viewModel: ProfileEditViewModel = hiltViewModel()
    val globalViewModel: GlobalViewModel = hiltViewModel()
    val sharedProfileInterestsViewModel: SharedProfileInterestsViewModel = hiltViewModel()

    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val interestUIState by sharedProfileInterestsViewModel.uiState.collectAsStateWithLifecycle()
    val navBackStackEntry by navController.currentBackStackEntryAsState()

    var occupiedIndex by remember { mutableIntStateOf(-1) }

    val launcher = rememberPickVisualMediaRequest { uri ->
        if (uri == null || occupiedIndex == -1) return@rememberPickVisualMediaRequest

        globalViewModel.uploadFileFromUri(
            uri = uri,
            onStart = {
                viewModel.startMediaLoading(occupiedIndex)
            },
            onSuccess = {
                viewModel.addMedia(occupiedIndex, it)
            },
            onComplete = {
                viewModel.endMediaLoading(occupiedIndex)
                occupiedIndex = -1
            }
        )
    }

    val cropperLauncher = rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        if (it.resultCode != Activity.RESULT_OK) {
            return@rememberLauncherForActivityResult
        }
        val data = it.data ?: return@rememberLauncherForActivityResult
        val isDelete = ImageCropperActivity.isDelete(data)
        if (isDelete) {
            viewModel.deleteMedia(occupiedIndex)
            return@rememberLauncherForActivityResult
        }

        val output = ImageCropperActivity.getResult(data)
        val uri = File(output).toUri()

        globalViewModel.uploadFileFromUri(
            uri = uri,
            onStart = {
                viewModel.startMediaLoading(occupiedIndex)
            },
            onSuccess = { url ->
                viewModel.updateMedia(occupiedIndex, url)
            },
            onComplete = {
                viewModel.endMediaLoading(occupiedIndex)
                occupiedIndex = -1
            }
        )
    }

    val sharedNickname = navBackStackEntry
        ?.savedStateHandle
        ?.getStateFlow(NICKNAME_SHARED_KEY, uiState.nickname)
        ?.collectAsStateWithLifecycle()

    val desc = navBackStackEntry
        ?.savedStateHandle
        ?.getStateFlow(DESC_SHARED_KEY, uiState.desc)
        ?.collectAsStateWithLifecycle()

    LaunchedEffect(sharedNickname) {
        sharedNickname?.value?.let {
            viewModel.updateNickname(it)
        }
    }

    LaunchedEffect(desc) {
        desc?.value?.let {
            viewModel.updateDesc(it)
        }
    }

    LaunchedEffect(interestUIState.confirmedInterests) {
        viewModel.updateInterests(interestUIState.confirmedInterests)
    }


    val titleEditNickname = stringResource(R.string.edit_nickname)
    val titleDescRoute = stringResource(R.string.edit_profile_desc)

    ProfileEditContent(
        uiState = uiState,
        onBackClick = navController::popBackStack,
        onItemClick = { index ->
            val item = uiState.mediaList.getOrNull(index) ?: return@ProfileEditContent

            if (item is ProfileEditUiState.MediaStatus.Idle) { //无照片状态
                occupiedIndex = uiState.firstIdleIndex //更新index为最近的一个未设置照片

                launcher.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
            } else if (item is ProfileEditUiState.MediaStatus.Success) { //已经有照片
                occupiedIndex = index //更新index为当前点击的index

                globalViewModel.downloadToFile(
                    url = item.url,
                    fileName = FileUtils.generateFileName(FileUtils.IMAGE_SUFFIX),
                    onSuccess = {
                        cropperLauncher.launch(ImageCropperActivity.createInstance(context, it.absolutePath))
                    }
                )
            }
        },
        onNicknameClick = {
            navController.navigate(EditInputRoute(
                sharedKey = NICKNAME_SHARED_KEY,
                text = uiState.nickname,
                title = titleEditNickname,
            ))
        },
        onDescClick = {
            navController.navigate(EditInputRoute(
                sharedKey = DESC_SHARED_KEY,
                text = uiState.desc,
                title = titleDescRoute,
            ))
        },
        onBirthdayChange = {
            viewModel.updateBirthday(it)
        },
        onInterestsClick = {
            sharedProfileInterestsViewModel.updateCurrentTagsByString(uiState.interests)
            navController.navigate(ProfileEditInterestsRoute)
        },
        onSaveClick = {
            viewModel.updateMediaToBackend()
        }
    )
}

@Composable
private fun ProfileEditContent(
    uiState: ProfileEditUiState,
    onBackClick: () -> Unit = {},
    onItemClick: (index: Int) -> Unit = {},
    onNicknameClick: () -> Unit = {},
    onDescClick: () -> Unit = {},
    onBirthdayChange: (LocalDate?) -> Unit = {},
    onInterestsClick: () -> Unit = {},
    onSaveClick: () -> Unit = {},
) {
    var isShownAgeDialog by remember { mutableStateOf(false) }

    AppScaffold(
        title = {
            Text(
                text = stringResource(R.string.edit),
                style = TextStyle(
                    fontSize = 17.sp,
                    lineHeight = 21.sp,
                    fontWeight = FontWeight.W700,
                    color = Color.White,
                )
            )
        },
        onBackClick = onBackClick,
        rightNavigationContent = {
            if (uiState.isMediaLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    strokeWidth = 2.dp,
                    color = TextGray999,
                )
            } else {
                Text(
                    text = stringResource(R.string.save),
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = 20.sp,
                        fontWeight = FontWeight(400),
                        color = Color.White.copy(0.85f),
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier
                        .background(color = PurplePrimary, shape = RoundedCornerShape(20.dp))
                        .noRippleClickable(onClick = onSaveClick)
                        .padding(horizontal = 16.dp, vertical = 5.dp)
                )
            }
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) {
        Image(
            painter = painterResource(R.mipmap.ic_common_top_bg),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier.fillMaxWidth()
        )

        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            EditProfilePhoto(
                medias = uiState.mediaList,
                modifier = Modifier,
                padding = PaddingValues(horizontal = 16.dp),
                onClick = onItemClick
            )

            Spacer(Modifier.height(28.dp))

            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                //nickname
                EditProfileItem(
                    modifier = Modifier.padding(horizontal = 18.dp),
                    title = stringResource(R.string.nickname),
                    text = uiState.nickname,
                    isHint = uiState.nickname.isEmpty(),
                    isShowArrow = true,
                    isLoading = uiState.isNicknameLoading,
                    onClick = onNicknameClick
                )

                //bio
                EditProfileItem(
                    modifier = Modifier.padding(horizontal = 18.dp),
                    title = stringResource(R.string.edit_profile_desc),
                    text = uiState.descOrPlaceholder,
                    isHint = uiState.desc.isEmpty(),
                    isShowArrow = true,
                    isLoading = uiState.isDescLoading,
                    onClick = onDescClick,
                )

                //age
                EditProfileItem(
                    modifier = Modifier.padding(horizontal = 18.dp),
                    title = stringResource(R.string.age),
                    text = uiState.age,
                    isHint = uiState.age.isEmpty(),
                    isShowArrow = true,
                    isLoading = uiState.isBirthdayLoading,
                    onClick = { isShownAgeDialog = true },
                )

                //gender
                EditProfileItem(
                    modifier = Modifier.padding(horizontal = 18.dp),
                    title = stringResource(R.string.gender),
                    text = stringResource(uiState.gender.stringResource),
                    isHint = uiState.gender == Gender.UNSPECIFIED,
                    isShowArrow = false,
                )

                //meet
                EditProfileItem(
                    modifier = Modifier.padding(horizontal = 18.dp),
                    title = stringResource(R.string.edit_profile_meet),
                    text = stringResource(uiState.meet.stringResource),
                    isHint = uiState.meet == Gender.UNSPECIFIED,
                    isShowArrow = false,
                )

                //interest
                EditProfileItem(
                    modifier = Modifier.padding(horizontal = 18.dp),
                    title = stringResource(R.string.edit_profile_interests),
                    text = uiState.interestsOrPlaceholder,
                    isHint = uiState.interests.isEmpty(),
                    isShowArrow = true,
                    isLoading = uiState.isInterestsLoading,
                    onClick = onInterestsClick,
                )
            }
        }
    }


    BirthdayDialog(
        isShown = isShownAgeDialog,
        defaultDate = uiState.birthday,
        onDismissRequest = { isShownAgeDialog = false },
        onBirthdayConfirmRequest = {
            isShownAgeDialog = false
            onBirthdayChange(it)
        }
    )
}

@Preview
@Composable
private fun ProfileEditContentPreview() {
    AppTheme {
        ProfileEditContent(
            uiState = ProfileEditUiState(
                nickname = "Wendy Wulawulawula",
                desc = "I am a test user",
                age = "20",
                gender = Gender.MALE,
                meet = Gender.FEMALE,
                interests = listOf("FASHION", "WUWWUUW", "sda", "kjnkjdn", "djknkdnas", "dkankdn"),
            )
        )
    }
}