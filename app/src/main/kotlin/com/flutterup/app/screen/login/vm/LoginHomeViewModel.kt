package com.flutterup.app.screen.login.vm

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.flutterup.app.R
import com.flutterup.app.screen.login.helper.GoogleSignInResult
import com.flutterup.base.BaseViewModel
import com.flutterup.base.utils.Timber
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class LoginHomeViewModel @Inject constructor(
    @ApplicationContext private val context: Context
) : BaseViewModel() {

    var showGoogleSignInDialog by mutableStateOf(false)
        private set

    var isLoading by mutableStateOf(false)
        private set

    var isAgreementPolicy by mutableStateOf(false)


    /**
     * 显示 Google 登录弹窗
     */
    fun showGoogleSignIn() {
        showGoogleSignInDialog = true
    }

    /**
     * 隐藏 Google 登录弹窗
     */
    fun hideGoogleSignIn() {
        showGoogleSignInDialog = false
    }

    fun rippleAgreementPolicy() {
        isAgreementPolicy = !isAgreementPolicy
    }

    fun checkAgreementPolicy(
        onSuccess: () -> Unit
    ) {
        if (isAgreementPolicy) {
            onSuccess()
            return
        }

        //弹出提示
        Timber.showToast(context.getString(R.string.agreement_error))
        isAgreementPolicy = true //更新状态
    }


    /**
     * 处理 Google 登录结果
     */
    fun handleGoogleSignInResult(result: GoogleSignInResult) {
        viewModelScope.launch {
            when (result) {
                is GoogleSignInResult.Success -> {
                    isLoading = true
                    try {
                        // TODO: 使用授权码换取访问令牌并完成登录
                        Timber.d("LoginHomeViewModel", "Google sign in success with authorization code: ${result.authorizationCode}")

                        // 这里应该调用后端 API 来完成登录流程
                        // 暂时模拟成功

                        isLoading = false
                        // TODO: 导航到主页面
                    } catch (e: Exception) {
                        isLoading = false
                        Timber.e("LoginHomeViewModel", "Google sign in failed", e)
                    }
                }
                is GoogleSignInResult.Error -> {
                    Timber.e("LoginHomeViewModel", "Google sign in error: ${result.error}")
                }
                GoogleSignInResult.Cancelled -> {
                    Timber.d("LoginHomeViewModel", "Google sign in cancelled")
                }
            }
        }
    }
}