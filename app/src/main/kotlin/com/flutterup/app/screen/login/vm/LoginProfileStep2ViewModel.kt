package com.flutterup.app.screen.login.vm

import com.flutterup.app.model.Tag
import com.flutterup.app.navigation.GlobalNavCenter
import com.flutterup.app.screen.HomeBaseRoute
import com.flutterup.app.screen.login.state.LoginProfileStep2UIState
import com.flutterup.app.screen.login.state.MAX_INTEREST_COUNT
import com.flutterup.base.BaseRepositoryViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class LoginProfileStep2ViewModel @Inject constructor(
    private val repository: LoginProfileRepository,
    private val navCenter: GlobalNavCenter,
) : BaseRepositoryViewModel(repository) {

    private val _uiState = MutableStateFlow(LoginProfileStep2UIState())

    val uiState: StateFlow<LoginProfileStep2UIState> = combine(
        _uiState,
        loadingState
    ) { ui, loading ->
        ui.copy(isLoading = loading)
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value.copy(isLoading = loadingState.value)
    )

    fun addMedia(index: Int, url: String) {
        _uiState.update { uiState ->
            val successStatus = LoginProfileStep2UIState.MediaStatus.Success(url)
            val mediaStatuses = uiState.mediaStatuses.toMutableList().also { it[index] = successStatus }
            uiState.copy(mediaStatuses = mediaStatuses)
        }
    }

    fun updateMedia(index: Int, url: String) {
        _uiState.update { uiState ->
            val successStatus = LoginProfileStep2UIState.MediaStatus.Success(url)
            val mediaStatuses = uiState.mediaStatuses.toMutableList().also { it[index] = successStatus }
            uiState.copy(mediaStatuses = mediaStatuses)
        }
    }

    fun startMediaLoading(index: Int) {
        _uiState.update { uiState ->
            val mediaStatuses = uiState.mediaStatuses.toMutableList().also { it[index] = LoginProfileStep2UIState.MediaStatus.Loading }
            uiState.copy(mediaStatuses = mediaStatuses)
        }
    }

    fun endMediaLoading(index: Int) {
        val status = _uiState.value.mediaStatuses.getOrNull(index)

        if (status !is LoginProfileStep2UIState.MediaStatus.Success) {
            _uiState.update { uiState ->
                val mediaStatuses = uiState.mediaStatuses.toMutableList().also { it[index] = LoginProfileStep2UIState.MediaStatus.Idle }
                uiState.copy(mediaStatuses = mediaStatuses)
            }
        }
    }

    fun deleteMedia(occupiedIndex: Int) {
        _uiState.update { uiState ->
            val mediaStatuses = uiState.mediaStatuses.toMutableList()

            mediaStatuses.removeAt(occupiedIndex) //删除
            mediaStatuses.add(LoginProfileStep2UIState.MediaStatus.Idle) //添加到最后
            uiState.copy(mediaStatuses = mediaStatuses)
        }
    }

    fun updateSign(sign: String) {
        _uiState.update { it.copy(sign = sign) }
    }

    fun updateInterestsVisible() {
        val previousVisible = _uiState.value.interestListVisible
        _uiState.update { it.copy(interestListVisible = !previousVisible) }
    }

    fun updateInterest(isSelected: Boolean, tag: Tag) {
        val interests = _uiState.value.interests.toMutableList()
        if (interests.size >= MAX_INTEREST_COUNT && !isSelected) { //选项满了，并且当前是非选中状态
            return
        }

        if (isSelected) {
            interests.remove(tag)
        } else {
            interests.add(tag)
        }

        _uiState.update { it.copy(interests = interests) }
    }

    /**
     * 结束注册流程
     *
     * @param isSkip skip会无视填写的内容
     */
    fun complete(isSkip: Boolean) {
        if (isSkip) {
            navCenter.navigateHome()
        } else {
            complete()
        }
    }

    private fun complete() {
        scope.launchWithLoading {
            val data = uiState.value

            val mediaList = data.mediaStatuses
                .filterIsInstance<LoginProfileStep2UIState.MediaStatus.Success>().map { it.url }
                .ifEmpty { null }

            val sign = data.sign.ifEmpty { null }
            val interests = data.interests.ifEmpty { null }


            val isSuccess = repository.updateOptionalProfile(
                mediaList = mediaList,
                sign = sign,
                interests = interests,
            )

            if (isSuccess) {
                navCenter.navigateHome()
            }
        }
    }
}