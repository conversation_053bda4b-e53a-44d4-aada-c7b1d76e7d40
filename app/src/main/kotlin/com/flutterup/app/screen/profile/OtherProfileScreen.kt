@file:OptIn(ExperimentalSharedTransitionApi::class, ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.profile

import androidx.compose.animation.AnimatedContentScope
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.AppDefaultNavigationIcon
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppAvatar
import com.flutterup.app.design.component.AppNearby
import com.flutterup.app.design.component.Margin
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.LabelBackgroundPrimary
import com.flutterup.app.design.theme.PurpleGradientSecondaryEnd
import com.flutterup.app.design.theme.PurpleGradientSecondaryStart
import com.flutterup.app.design.theme.ShadowAmbientPrimary
import com.flutterup.app.design.theme.ShadowSpotPrimary
import com.flutterup.app.design.theme.TextBlack
import com.flutterup.app.design.theme.TextBlack666
import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.PingRefer
import com.flutterup.app.model.UserActionCardType
import com.flutterup.app.model.UserActionType
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.chat.SendPingChatRoute
import com.flutterup.app.screen.common.MediaPreviewRoute
import com.flutterup.app.screen.common.ReportUserDialog
import com.flutterup.app.screen.profile.state.OtherProfileUiState
import com.flutterup.app.screen.profile.vm.OtherProfileViewModel
import com.flutterup.app.screen.common.vm.ProfileSharedViewModel
import com.flutterup.app.utils.SharedElementUtils
import kotlinx.coroutines.launch

@Composable
fun OtherProfileScreen(
    userId: String,
    from: AppFrom = AppFrom.Unknown,
    cardType: UserActionCardType = UserActionCardType.Default,
) {
    val navController = LocalNavController.current
    val viewModel: OtherProfileViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    var showReportDialog by remember { mutableStateOf(false) }

    LaunchedEffect(userId) {
        viewModel.getOtherProfileById(userId)
    }

    OtherProfileContent(
        uiState = uiState,
        sharedTransitionScope = null,
        animatedContentScope = null,
        onBackClick = { navController.popBackStack() },
        onDislikeClick = { viewModel.dislike(from, cardType) { navController.popBackStack() } },
        onLikeClick = { viewModel.like(from, cardType) { navController.popBackStack() } },
        onPingChatClick = { navController.navigate(SendPingChatRoute(userId)) },
        onAlbumClick = {
            val route = MediaPreviewRoute(uiState.fullMediaList, it)
            navController.navigate(route)
        },
        onReportClick = {
            showReportDialog = true
        }
    )

    if (showReportDialog) {
        ReportUserDialog(
            targetUserId = userId,
            from = from,
            onCancelClick = { showReportDialog = false },
        )
    }
}

@Composable
fun OtherProfileScreen(
    userId: String,
    nickname: String,
    age: Int,
    headimg: String,
    mediaList: List<String>,
    intro: String?,
    interests: List<String>?,
    currentIndex: Int = 0,
    location: Int = 0,
    from: AppFrom = AppFrom.Unknown,
    cardType: UserActionCardType = UserActionCardType.Default,
    sharedTransitionScope: SharedTransitionScope,
    animatedContentScope: AnimatedContentScope,
    sharedViewModel: ProfileSharedViewModel,
) {
    val navController = LocalNavController.current
    val viewModel: OtherProfileViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    var showReportDialog by remember { mutableStateOf(false) }

    LaunchedEffect(userId, nickname, age, headimg, mediaList, intro, interests) {
        viewModel.initByParams(
            userId,
            nickname,
            age,
            headimg,
            mediaList,
            intro,
            interests,
            location
        )
    }

    OtherProfileContent(
        uiState = uiState,
        sharedTransitionScope = sharedTransitionScope,
        animatedContentScope = animatedContentScope,
        initialIndex = currentIndex,
        onBackClick = { navController.popBackStack() },
        onDislikeClick = {
            viewModel.dislike(from, cardType) {
                sharedViewModel.sendActionEvent(userId, UserActionType.Dislike)
                navController.popBackStack()
            }
        },
        onLikeClick = {
            viewModel.like(from, cardType) {
                sharedViewModel.sendActionEvent(userId, UserActionType.Like)
                navController.popBackStack()
            }
        },
        onPingChatClick = {
            navController.navigate(SendPingChatRoute(userId))
        },
        onPageChange = {
            sharedViewModel.sendSharedCurrentPage(it)
        },
        onAlbumClick = {
            val route = MediaPreviewRoute(uiState.fullMediaList, it)
            navController.navigate(route)
        },
        onReportClick = {
            showReportDialog = true
        }
    )

    if (showReportDialog) {
        ReportUserDialog(
            targetUserId = userId,
            from = from,
            sharedViewModel = sharedViewModel,
            onCancelClick = { showReportDialog = false },
        )
    }
}

@Composable
private fun OtherProfileContent(
    uiState: OtherProfileUiState,
    sharedTransitionScope: SharedTransitionScope?,
    animatedContentScope: AnimatedContentScope?,
    initialIndex: Int = 0,
    onBackClick: () -> Unit = {},
    onReportClick: () -> Unit = {},
    onPageChange: (Int) -> Unit = {},
    onDislikeClick: () -> Unit = {},
    onLikeClick: () -> Unit = {},
    onPingChatClick: () -> Unit = {},
    onAlbumClick: (Int) -> Unit = {},
) {
    // get local density from composable
    val localDensity = LocalDensity.current

    val pagerState = rememberPagerState(initialPage = initialIndex) { uiState.mediaList.size }
    var bottomBarHeight by remember { mutableStateOf(0.dp) }

    LaunchedEffect(pagerState) {
        // Collect from the a snapshotFlow reading the currentPage
        snapshotFlow { pagerState.currentPage }.collect { page ->
            onPageChange(page)
        }
    }

    AppScaffold(
        title = { },
        navigation = {
            Row(verticalAlignment = Alignment.CenterVertically) {
                AppDefaultNavigationIcon(
                    canGoBack = true,
                    onBackClick = onBackClick
                )

                if (uiState.headImg != null) {
                    AppAvatar(online = uiState.online) {
                        AsyncImage(
                            model = uiState.headImg,
                            contentDescription = null,
                            contentScale = ContentScale.Crop,
                            modifier = Modifier
                                .size(32.dp)
                                .clip(CircleShape)
                                .border(1.dp, Color.White, CircleShape)
                        )
                    }
                }

                if (uiState.nickname != null) {
                    Spacer(Modifier.width(12.dp))

                    Text(
                        text = uiState.nickname + " " + uiState.age,
                        style = TextStyle(
                            fontSize = 20.sp,
                            lineHeight = 16.sp,
                            fontWeight = FontWeight.W900,
                            color = Color.White,
                            textAlign = TextAlign.Center,
                        )
                    )
                }
            }
        },
        rightNavigationContent = {
            Icon(
                painter = painterResource(R.drawable.ic_report),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier.noRippleClickable(onClick = onReportClick)
            )
        },
        modifier = Modifier.fillMaxSize(),
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) {
        Box(
            Modifier.fillMaxSize()
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_common_top_bg),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = it.calculateTopPadding(), bottom = bottomBarHeight + 10.dp)
                    .verticalScroll(rememberScrollState()),
            ) {
                OtherProfileAlbumPager(
                    uiState = uiState,
                    pagerState = pagerState,
                    sharedTransitionScope = sharedTransitionScope,
                    animatedContentScope = animatedContentScope,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 12.dp)
                        .aspectRatio(3f / 4f),
                    onBackClick = onBackClick,
                    onAlbumClick = onAlbumClick,
                )

                uiState.intro?.let { intro ->
                    Card(
                        modifier = Modifier.fillMaxWidth()
                            .wrapContentHeight()
                            .padding(start = 12.dp, end = 12.dp, top = 8.dp),
                        shape = RoundedCornerShape(21.dp),
                        colors = CardDefaults.cardColors(containerColor = Color.White)
                    ) {
                        Row(
                            Modifier.padding(top = 15.dp, start = 15.dp),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_profile_intro),
                                contentDescription = null,
                                tint = Color.Unspecified,
                            )

                            Text(
                                text = stringResource(R.string.profile_introduction_label),
                                style = TextStyle(
                                    fontSize = 16.sp,
                                    lineHeight = 28.sp,
                                    fontWeight = FontWeight.W500,
                                    color = TextBlack,
                                )
                            )
                        }

                        Text(
                            text = intro,
                            modifier = Modifier.padding(start = 14.dp, end = 14.dp, bottom = 14.dp),
                            style = TextStyle(
                                fontSize = 14.sp,
                                fontWeight = FontWeight.W400,
                                color = TextBlack666,
                            )
                        )
                    }
                }

                uiState.interests?.let { interests ->
                    if (interests.isEmpty()) return@let

                    Card(
                        modifier = Modifier.fillMaxWidth()
                            .wrapContentHeight()
                            .padding(start = 12.dp, end = 12.dp, top = 8.dp),
                        shape = RoundedCornerShape(21.dp),
                        colors = CardDefaults.cardColors(containerColor = Color.White)
                    ) {
                        Row(
                            modifier = Modifier.padding(start = 15.dp, top = 22.dp),
                            horizontalArrangement = Arrangement.spacedBy(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_profile_interests),
                                contentDescription = null,
                                tint = Color.Unspecified,
                            )

                            Text(
                                text = stringResource(R.string.profile_interest_label),
                                style = TextStyle(
                                    fontSize = 18.sp,
                                    lineHeight = 28.sp,
                                    fontWeight = FontWeight.W500,
                                    color = TextBlack,
                                )
                            )
                        }

                        FlowRow(
                            modifier = Modifier.padding(
                                start = 14.dp,
                                end = 14.dp,
                                top = 10.dp,
                                bottom = 20.dp,
                            ),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalArrangement = Arrangement.spacedBy(10.dp)
                        ) {
                            repeat(interests.size) { index ->
                                val interest = interests[index]
                                Row(
                                    modifier = Modifier
                                        .background(
                                            color = LabelBackgroundPrimary,
                                            shape = RoundedCornerShape(13.dp)
                                        )
                                        .padding(start = 8.dp, top = 5.dp, bottom = 5.dp, end = 12.dp)
                                ) {
                                    Text(
                                        text = interest,
                                        style = TextStyle(
                                            fontSize = 10.sp,
                                            fontWeight = FontWeight.W400,
                                            color = Color.White,
                                        )
                                    )
                                }
                            }
                        }
                    }
                }
            }

            Actions(
                uiState = uiState,
                onDislikeClick = onDislikeClick,
                onLikeClick = onLikeClick,
                onPingChatClick = onPingChatClick,
                modifier = Modifier
                    .onSizeChanged { size ->
                        bottomBarHeight = with(localDensity) { size.height.toDp() }
                    }
                    .align(Alignment.BottomStart)
                    .padding(bottom = it.calculateBottomPadding() + 20.dp, start = 38.dp)
            )
        }
    }
}

@Composable
private fun Actions(
    uiState: OtherProfileUiState,
    modifier: Modifier = Modifier,
    onDislikeClick: () -> Unit = {},
    onLikeClick: () -> Unit = {},
    onPingChatClick: () -> Unit = {},
) {
    //没有refer，或者已经建联不展示
    if (uiState.refer == null || uiState.refer == PingRefer.PINGED) return

    val itemModifier = Modifier
        .shadow(
            elevation = 10.dp,
            spotColor = ShadowSpotPrimary,
            ambientColor = ShadowAmbientPrimary
        )

    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (uiState.refer == PingRefer.WINKED || uiState.refer == PingRefer.NONE) {
            Icon(
                painter = painterResource(R.drawable.ic_dislike),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier
                    .then(itemModifier)
                    .clickable(onClick = onDislikeClick)
            )

            Spacer(modifier = Modifier.width(18.dp))

            Icon(
                painter = painterResource(R.drawable.ic_like),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier
                    .then(itemModifier)
                    .clickable(onClick = onLikeClick)
            )
        }

        if (uiState.refer == PingRefer.WINKED || uiState.refer == PingRefer.NONE) {
            Spacer(modifier = Modifier.width(16.dp))
        }

        Icon(
            painter = painterResource(R.drawable.ic_ping_chat),
            contentDescription = null,
            tint = Color.Unspecified,
            modifier = Modifier
                .then(itemModifier)
                .size(width = 160.dp, height = 42.dp)
                .clickable(onClick = onPingChatClick)
        )
    }
}

@Composable
private fun OtherProfileAlbumPager(
    uiState: OtherProfileUiState,
    pagerState: PagerState,
    sharedTransitionScope: SharedTransitionScope?,
    animatedContentScope: AnimatedContentScope?,
    modifier: Modifier = Modifier,
    onBackClick: () -> Unit,
    onAlbumClick: (Int) -> Unit,
) {
    if (uiState.mediaList.isEmpty()) { //不显示空
        return
    }

    val sharedIconModifier: Modifier

    if (sharedTransitionScope != null && animatedContentScope != null) {
        with(sharedTransitionScope) {
            sharedIconModifier = Modifier
                .sharedElement(
                    sharedContentState = sharedTransitionScope.rememberSharedContentState(
                        SharedElementUtils.generatorProfileIconKey(uiState.userId)
                    ),
                    animatedVisibilityScope = animatedContentScope
                )
                .renderInSharedTransitionScopeOverlay(zIndexInOverlay = 1f)
        }
    } else {
        sharedIconModifier = Modifier
    }

    val scope = rememberCoroutineScope()
    val shape = RoundedCornerShape(26.dp)
    val indicatorShape = RoundedCornerShape(12.dp)

    Card(
        modifier = modifier,
        shape = shape,
    ) {
        Box(Modifier.fillMaxSize()) {
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.fillMaxSize(),
            ) { index ->
                val media = uiState.mediaList.getOrNull(index) ?: return@HorizontalPager

                AsyncImage(
                    model = media,
                    contentDescription = null,
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier
                        .fillMaxSize()
                        .border(
                            width = 1.dp,
                            color = Color.White,
                            shape = shape
                        )
                        //这个一定要在border之后，clip之前，表示不给共享元素加边框，但是会裁剪
                        .run {
                            if (sharedTransitionScope != null && animatedContentScope != null) {
                                with(sharedTransitionScope) {
                                    then(
                                        Modifier
                                            .sharedElement(
                                                sharedContentState = sharedTransitionScope.rememberSharedContentState(SharedElementUtils.generatorMediaUrlKey(media)),
                                                animatedVisibilityScope = animatedContentScope
                                            )
                                            .renderInSharedTransitionScopeOverlay(zIndexInOverlay = 0f)
                                    )
                                }
                            } else {
                                Modifier
                            }
                        }
                        .clip(shape)
                        .noRippleClickable(onClick = { onAlbumClick(pagerState.currentPage) })
                )
            }

            if (uiState.isNearby) { //是否附近
                AppNearby(
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .padding(start = 10.dp, top = 20.dp)
                )
            }


            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                contentPadding = PaddingValues(start = 20.dp, bottom = 10.dp),
                modifier = Modifier
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                PurpleGradientSecondaryStart.copy(0f),
                                PurpleGradientSecondaryEnd.copy(0.5f)
                            )
                        )
                    )
                    .padding(end = 56.dp)
                    .fillMaxWidth()
                    .align(Alignment.BottomStart),
            ) {
                items(
                    count = uiState.mediaList.size,
                    key = { uiState.mediaList[it] }
                ) { index ->
                    AsyncImage(
                        model = uiState.mediaList[index],
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .clip(indicatorShape)
                            .size(60.dp)
                            .border(
                                width = 1.dp,
                                color = if (index == pagerState.currentPage) Color.White else Color.Transparent,
                                shape = indicatorShape
                            )
                            .noRippleClickable(onClick = {
                                scope.launch { pagerState.scrollToPage(index) }
                            })
                    )
                }
            }

            Margin(
                paddingValues = PaddingValues(end = 18.dp, bottom = 31.dp),
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .noRippleClickable(onClick = onBackClick)
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_discover_explore),
                    contentDescription = null,
                    tint = Color.Unspecified,
                    modifier = Modifier
                        .then(sharedIconModifier)
                        .rotate(180f)
                )
            }
        }
    }
}

@Preview
@Composable
private fun OtherProfileScreenPreview() {
    AppTheme {
        OtherProfileContent(
            uiState = OtherProfileUiState(
                userId = "",
                mediaList = listOf("1", "2", "3"),
                intro = "lalalaa",
                interests = listOf("FASHION", "WUWWUUW", "sda", "kjnkjdn", "djknkdnas", "dkankdn"),
                refer = PingRefer.WINK_SENT,
                isNearby = true
            ),
            sharedTransitionScope = null,
            animatedContentScope = null,
        )
    }
}