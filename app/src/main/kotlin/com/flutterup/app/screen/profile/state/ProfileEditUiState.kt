package com.flutterup.app.screen.profile.state

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import com.flutterup.app.R
import com.flutterup.app.model.Gender
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.model.UserInfo
import com.flutterup.app.screen.login.state.LoginProfileStep2UIState
import com.flutterup.base.BaseState
import com.flutterup.base.utils.toLocalDate
import java.time.LocalDate


private const val MAX_MEDIA_COUNT = 6

data class ProfileEditUiState(
    val nickname: String = "",

    val desc: String = "",

    val age: String = "",

    val birthday: LocalDate? = null,

    val gender: Gender = Gender.UNSPECIFIED,

    val meet: Gender = Gender.UNSPECIFIED,

    val interests: List<String> = emptyList(),

    val mediaList: List<MediaStatus> = List(MAX_MEDIA_COUNT) { MediaStatus.Idle },

    val isMediaLoading: Boolean = false,

    val isNicknameLoading: Boolean = false,

    val isBirthdayLoading: Boolean = false,

    val isDescLoading: Boolean = false,

    val isInterestsLoading: Boolean = false,
) {

    val descOrPlaceholder: String
        @Composable get() = desc.ifEmpty { stringResource(R.string.edit_profile_desc_placeholder) }

    val interestsOrPlaceholder: String
        @Composable get() = interests.joinToString(", ").ifEmpty { stringResource(R.string.edit_profile_interests_placeholder) }

    val firstIdleIndex: Int
        get() = mediaList.indexOfFirst { it is MediaStatus.Idle }

    sealed interface MediaStatus {
        object Idle : MediaStatus

        object Loading : MediaStatus

        data class Success(val url: String) : MediaStatus
    }

    companion object {
        fun fromUserInfo(userInfo: UserInfo?): ProfileEditUiState {
            return ProfileEditUiState(
                nickname = userInfo?.nickname.orEmpty(),
                desc = userInfo?.sign.orEmpty(),
                age = userInfo?.age.toString(),
                birthday = userInfo?.birthday?.toLocalDate(),
                gender = Gender.fromValue(userInfo?.sex),
                meet = Gender.fromValue(userInfo?.sexuality),
                interests = userInfo?.tags.orEmpty(),
                mediaList = createMediaList(userInfo?.mediaList.orEmpty()),
            )
        }

        private fun createMediaList(mediaList: List<MediaItemEntity>): List<MediaStatus> {
            val origin = MutableList<MediaStatus>(MAX_MEDIA_COUNT) { MediaStatus.Idle }

            mediaList.forEachIndexed { index, item ->
                if (index >= MAX_MEDIA_COUNT) return@forEachIndexed

                origin[index] = MediaStatus.Success(item.imageUrl)
            }
            return origin
        }
    }
}