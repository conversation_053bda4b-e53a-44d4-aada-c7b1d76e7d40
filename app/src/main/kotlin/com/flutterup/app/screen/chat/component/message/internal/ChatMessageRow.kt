package com.flutterup.app.screen.chat.component.message.internal

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.BorderPrimary
import com.flutterup.app.model.UserInfo
import io.rong.imlib.model.Message


@Composable
fun ChatMessageRow(
    mine: UserInfo?,
    other: UserInfo?,
    message: Message,
    onResendClick: (Message) -> Unit = {},
    onProfileClick: () -> Unit = {},
    content: @Composable RowScope.(message: Message) -> Unit
) {
    val direction = message.messageDirection

    when(direction) {
        Message.MessageDirection.SEND -> ChatMessageRowSender(mine, message, onResendClick, content)
        Message.MessageDirection.RECEIVE -> ChatMessageRowReceiver(other, message, onProfileClick, content)
    }
}

@Composable
fun ChatMessageRowSender(
    mine: UserInfo?,
    message: Message,
    onResendClick: (Message) -> Unit,
    content: @Composable RowScope.(message: Message) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth().wrapContentHeight(),
        horizontalArrangement = Arrangement.End
    ) {
        if (message.sentStatus == Message.SentStatus.SENDING) {
            CircularProgressIndicator(
                modifier = Modifier
                    .padding(end = 6.dp)
                    .size(16.dp)
                    .align(Alignment.CenterVertically),
                strokeWidth = 2.dp,
                color = BorderPrimary
            )
        }

        if (message.sentStatus == Message.SentStatus.FAILED) {
            Icon(
                painter = painterResource(R.drawable.ic_chat_message_send_error),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier
                    .padding(end = 6.dp)
                    .noRippleClickable(onClick = { onResendClick(message) })
                    .size(16.dp)
                    .align(Alignment.CenterVertically)
            )
        }

        content(message)

        Spacer(Modifier.width(10.dp))

        AsyncImage(
            model = mine?.headImage,
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .size(48.dp)
                .clip(CircleShape)
                .align(Alignment.Bottom)
        )
    }
}

@Composable
fun ChatMessageRowReceiver(
    other: UserInfo?,
    message: Message,
    onProfileClick: () -> Unit,
    content: @Composable RowScope.(message: Message) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth().wrapContentHeight(),
        horizontalArrangement = Arrangement.Start
    ) {
        AsyncImage(
            model = other?.headImage,
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .size(48.dp)
                .noRippleClickable(onClick = onProfileClick)
                .clip(CircleShape)
                .align(Alignment.Bottom)
        )

        Spacer(Modifier.width(10.dp))

        content(message)
    }
}