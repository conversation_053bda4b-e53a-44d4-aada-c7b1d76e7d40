package com.flutterup.app.screen.common.utils

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.base.utils.Timber

@Composable
fun rememberExoPlayersPool(
    medias: List<MediaItemEntity>
): PlayersPools {
    val context = LocalContext.current
    val videoMedia = medias.filter { it.isVideo() }
    val playersPool = remember { PlayersPools(context, videoMedia) }

    DisposableEffect(Unit) {
        onDispose {
            playersPool.releaseAll()
        }
    }

    return playersPool
}

class PlayersPools(
    private val context: Context,
    medias: List<MediaItemEntity>,
) {
    private val players = hashMapOf<Int, ExoPlayer>()

    init {
        prepareExoPlayers(medias)
    }

    private fun prepareExoPlayers(pages: List<MediaItemEntity>) {
        pages.onEachIndexed { index, item ->
            createAndGet(item)
        }
    }


    fun createAndGet(item: MediaItemEntity): ExoPlayer {
        val id = item.imageUrl.hashCode()
        if (players.contains(id)) {
            return players[id]!!
        }

        val exoPlayer = ExoPlayer.Builder(context)
            .build()
            .apply {
                repeatMode = Player.REPEAT_MODE_ALL
            }
        players[id] = exoPlayer
        players[id]?.setMediaItem(MediaItem.fromUri(item.imageUrl))

        Timber.i("Media", "created player: id=$id, player=$exoPlayer")
        return exoPlayer
    }

    fun releaseAll() {
        players.forEach {
            it.value.stop()
            it.value.release()
        }
        players.clear()
    }
}