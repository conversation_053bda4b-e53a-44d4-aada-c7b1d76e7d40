package com.flutterup.app.screen.settings.state

import android.content.Context
import com.flutterup.app.R
import com.flutterup.base.BaseState

data class DeleteAccountState(
    val reasonList: List<String>,
    val selectedReasonIndex: Int = 0,

    val customReason: String = "",
    override val isLoading: Boolean = false,
) : BaseState {

    val isCustomReasonSelected: <PERSON><PERSON><PERSON>
        get() = selectedReasonIndex == reasonList.lastIndex

    val isEnabled: <PERSON><PERSON><PERSON>
        get() = selectedReasonIndex in reasonList.indices || customReason.isNotEmpty()

    companion object {
        fun createDeleteAccountState(context: Context): DeleteAccountState {
            return DeleteAccountState(
                reasonList = context.resources.getStringArray(R.array.delete_account_reasons).toList()
            )
        }
    }
}
