package com.flutterup.app.screen.login.state

import com.flutterup.app.model.Tag
import com.flutterup.base.BaseState


const val MAX_MEDIA_COUNT = 6
const val MAX_INTEREST_COUNT = 5

data class LoginProfileStep2UIState(
    val mediaStatuses: List<MediaStatus> = List(MAX_MEDIA_COUNT) { MediaStatus.Idle },

    val sign: String = "",

    val interests: List<Tag> = emptyList(),

    val interestListVisible: Boolean = false,

    override val isLoading: Boolean = false,
) : BaseState {

    val firstIdleIndex: Int
        get() = mediaStatuses.indexOfFirst { it is MediaStatus.Idle }


    sealed interface MediaStatus {
        object Idle : MediaStatus
        object Loading : MediaStatus

        data class Success(val url: String) : MediaStatus
    }
}