package com.flutterup.app.screen.discover.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppButtonPrimaryColors
import com.flutterup.app.design.theme.ShadowAmbientPrimary
import com.flutterup.app.design.theme.ShadowSpotPrimary
import com.flutterup.app.screen.discover.state.DiscoverState
import com.valentinilk.shimmer.Shimmer

@Composable
fun DiscoverActionContent(
    uiState: DiscoverState,
    shimmerOnPrimary: Shimmer,
    onDislikeClick: () -> Unit = {},
    onLikeClick: () -> Unit = {},
    onPingChatClick: () -> Unit = {},
    onGotoChatClick: () -> Unit = {},
) {
    val itemModifier = Modifier
        .shadow(
            elevation = 10.dp,
            spotColor = ShadowSpotPrimary,
            ambientColor = ShadowAmbientPrimary
        )

    val isConnected = (uiState.discoverList.firstOrNull()?.type ?: 0) > 0
    if (isConnected) {
        Box(
            modifier = Modifier
                .padding(horizontal = 24.dp)
                .fillMaxWidth()
                .height(48.dp)
                .noRippleClickable(onClick = onGotoChatClick)
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_discover_goto_chat),
                contentDescription = null,
                contentScale = ContentScale.FillBounds,
                modifier = Modifier.matchParentSize()
            )

            Text(
                text = stringResource(R.string.start_chatting_now),
                style = TextStyle(
                    fontSize = 20.sp,
                    lineHeight = 22.sp,
                    fontWeight = FontWeight.W400,
                    color = Color.White,
                ),
                modifier = Modifier.align(Alignment.Center)
            )

            AppContinueButton(
                onClick = {},
                contentPadding = PaddingValues(0.dp),
                enabled = true,
                content = {
                    Icon(
                        painter = painterResource(R.drawable.ic_chat_white_send),
                        contentDescription = null,
                        tint = Color.Unspecified
                    )
                },
                isLoading = false,
                modifier = Modifier
                    .padding(end = 6.dp)
                    .size(40.dp)
                    .clip(CircleShape)
                    .align(Alignment.CenterEnd),
                colors = AppButtonPrimaryColors
            )
        }
    } else {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Spacer(modifier = Modifier.width(38.dp))

            ShimmerContent(
                uiState = uiState,
                shimmer = shimmerOnPrimary,
                modifier = Modifier
                    .size(54.dp)
                    .clip(CircleShape)
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_dislike),
                    contentDescription = null,
                    tint = Color.Unspecified,
                    modifier = Modifier
                        .then(itemModifier)
                        .clickable(onClick = onDislikeClick)
                )
            }

            Spacer(modifier = Modifier.width(18.dp))

            ShimmerContent(
                uiState = uiState,
                shimmer = shimmerOnPrimary,
                modifier = Modifier
                    .size(54.dp)
                    .clip(CircleShape)
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_like),
                    contentDescription = null,
                    tint = Color.Unspecified,
                    modifier = Modifier
                        .then(itemModifier)
                        .clickable(onClick = onLikeClick)
                )
            }

            Spacer(modifier = Modifier.width(21.dp))

            ShimmerContent(
                uiState = uiState,
                shimmer = shimmerOnPrimary,
                modifier = Modifier
                    .size(width = 160.dp, height = 42.dp)
                    .clip(RoundedCornerShape(21.dp))
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_ping_chat),
                    contentDescription = null,
                    tint = Color.Unspecified,
                    modifier = Modifier
                        .then(itemModifier)
                        .size(width = 160.dp, height = 42.dp)
                        .clickable(onClick = onPingChatClick)
                )
            }
        }
    }
}
