package com.flutterup.app.screen.payment.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.component.pager.DotPagerIndicator
import com.flutterup.app.design.theme.TextBrownGradientStart
import com.flutterup.app.design.theme.TextBrownVipPrimary
import com.flutterup.app.design.theme.TextBrownVipTriplet
import com.flutterup.app.design.theme.TextGoldenGradientEnd
import com.flutterup.app.screen.payment.state.PaymentSubscriptUiState
import kotlin.math.min

@Composable
fun SubscriptionBenefits(
    modifier: Modifier = Modifier,
    uiState: PaymentSubscriptUiState,
    paddingValues: PaddingValues,
    crossHeight: Dp,
    subRange: Int = 3
) {
    val pageCount = if (subRange > 0) (uiState.benefits.size + subRange - 1) / subRange else 0
    val pagerState = rememberPagerState(initialPage = 0) { pageCount }

    val cardModifier = Modifier
        .padding(start = 23.dp, end = 19.dp)
        .clip(RoundedCornerShape(size = 12.dp))
        .fillMaxWidth()
        .wrapContentHeight()
        .defaultMinSize(minHeight = 70.dp)

    val iconBackgroundModifier = Modifier
        .padding(10.dp)
        .clip(CircleShape)
        .size(50.dp)

    Box(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        Image(
            painter = painterResource(R.mipmap.ic_subscription_background),
            contentDescription = null,
            contentScale = ContentScale.FillBounds,
            modifier = Modifier.matchParentSize()
        )

        Column(
            modifier = Modifier.padding(
                top = paddingValues.calculateTopPadding(),
            )
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(start = 28.dp)
            ) {
                Text(
                    text = stringResource(R.string.flutter_up),
                    style = TextStyle(
                        fontSize = 32.sp,
                        fontWeight = FontWeight.W900,
                        fontStyle = FontStyle.Italic,
                        textAlign = TextAlign.Center,
                        brush = SUBSCRIPTION_TEXT_BRUSH
                    )
                )
                Spacer(modifier = Modifier.width(3.dp))
                Image(
                    painter = painterResource(R.mipmap.ic_subscription_pro),
                    contentDescription = null,
                    modifier = Modifier.size(width = 37.dp, height = 17.dp)
                )
            }

            Spacer(modifier = Modifier.height(5.dp))

            Text(
                text = stringResource(R.string.subscription_benefits, uiState.benefits.size),
                style = TextStyle(
                    fontSize = 10.sp,
                    fontWeight = FontWeight.W500,
                    color = TextBrownVipTriplet,
                ),
                modifier = Modifier.padding(start = 28.dp)
            )

            if (pageCount > 0) {
                Spacer(modifier = Modifier.height(42.dp))

                HorizontalPager(pagerState) { pageIndex ->
                    Column(verticalArrangement = Arrangement.spacedBy(10.dp)) {
                        val start = pageIndex * subRange
                        val end = min(start + subRange, uiState.benefits.size)

                        uiState.benefits.subList(start, end).forEach { benefit ->
                            Box(cardModifier) {
                                Image(
                                    painter = painterResource(R.mipmap.ic_subscription_benefits_background),
                                    contentDescription = null,
                                    contentScale = ContentScale.FillBounds,
                                    modifier = Modifier.matchParentSize()
                                )

                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    Box(
                                        modifier = iconBackgroundModifier,
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Image(
                                            painter = painterResource(R.mipmap.ic_subscription_benefits_icon_background),
                                            contentDescription = null,
                                            modifier = Modifier.matchParentSize()
                                        )

                                        AsyncImage(
                                            model = benefit.icon,
                                            contentDescription = null,
                                            contentScale = ContentScale.Crop,
                                            modifier = Modifier
                                                .size(29.dp)
                                                .clip(CircleShape)
                                        )
                                    }

                                    Column(
                                        modifier = Modifier
                                            .padding(end = 12.dp, top = 10.dp, bottom = 10.dp)
                                            .fillMaxWidth()
                                    ) {
                                        Text(
                                            text = benefit.title.orEmpty(),
                                            style = TextStyle(
                                                fontSize = 14.sp,
                                                fontWeight = FontWeight.W600,
                                                color = TextBrownVipPrimary,
                                            )
                                        )

                                        Spacer(modifier = Modifier.height(5.dp))

                                        Text(
                                            text = benefit.desc.orEmpty(),
                                            style = TextStyle(
                                                fontSize = 12.sp,
                                                lineHeight = 14.sp,
                                                fontWeight = FontWeight.W400,
                                                color = TextBrownVipPrimary,
                                            )
                                        )
                                    }
                                }
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(11.dp))

                DotPagerIndicator(
                    pageCount = pageCount,
                    selectedPage = pagerState.currentPage,
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
            }


            Spacer(modifier = Modifier.height(crossHeight + 15.dp))
        }
    }
}

private val SUBSCRIPTION_TEXT_BRUSH = Brush.horizontalGradient(
    colors = listOf(TextBrownGradientStart, TextGoldenGradientEnd)
)
