package com.flutterup.app.screen.chat.state

import com.flutterup.app.model.MediaItemEntity
import com.flutterup.base.BaseState

data class ChatPrivateMomentsUiState(

    val mediaList: List<MediaItemEntity> = emptyList(),

    val selectedMediaList: List<MediaItemEntity> = emptyList(),

    val isRefreshing: Boolean = false,
    val isLoadingMore: Boolean = false,
    val hasNoMoreData: Boolean = false,
    val isUploading: Boolean = false,
    val isDeleting: Boolean = false,
    override val isLoading: <PERSON>olean = false,
) : BaseState {

    val selectedImageCount = selectedMediaList.count { it.type == MediaItemEntity.TYPE_IMAGE }

    val selectedVideoCount = selectedMediaList.count { it.type == MediaItemEntity.TYPE_VIDEO }

    val currentSelectedType: Int? = if (selectedMediaList.isEmpty()) null else selectedMediaList.firstOrNull()?.type

    val canSelectedMore: Boolean = selectedMediaList.size < MAX_SELECTED_SIZE
}

private const val MAX_SELECTED_SIZE = 9