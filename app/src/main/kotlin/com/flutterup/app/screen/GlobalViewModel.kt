package com.flutterup.app.screen

import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import android.provider.OpenableColumns
import com.flutterup.app.model.Tag
import com.flutterup.app.model.UploadResponse
import com.flutterup.app.network.GlobalApiService
import com.flutterup.base.AppDirs
import com.flutterup.base.BaseRepository
import com.flutterup.base.BaseRepositoryViewModel
import com.flutterup.base.Dirs
import com.flutterup.base.utils.DateUtils
import com.flutterup.base.utils.FileUtils
import com.flutterup.network.AppClient
import com.flutterup.network.AppClients
import com.flutterup.network.DownloadApiService
import com.flutterup.network.impl.NetworkServiceProvider
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import javax.inject.Inject
import kotlin.collections.get
import kotlin.io.readBytes


@HiltViewModel
class GlobalViewModel @Inject constructor(
    private val repository: GlobalRepository
) : BaseRepositoryViewModel(repository) {

    fun uploadFileFromUri(
        uri: Uri,
        onSuccess: (String) -> Unit,
        onStart: () -> Unit = {},
        onComplete: () -> Unit = {},
    ) {
        onStart()
        scope.launch {
            val result = repository.uploadUri(uri)
            if (result != null) {
                onSuccess(result)
            }
        }.invokeOnCompletion {
            onComplete()
        }
    }

    fun downloadToFile(
        url: String,
        fileName: String,
        onSuccess: (File) -> Unit,
        onStart: () -> Unit = {},
        onComplete: () -> Unit = {},
    ) {
        onStart()
        scope.launch {
            val file = repository.downloadToFile(url, fileName)
            if (file != null) {
                onSuccess(file)
            }
        }.invokeOnCompletion {
            onComplete()
        }
    }
}