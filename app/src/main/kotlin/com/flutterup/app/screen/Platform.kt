package com.flutterup.app.screen

import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.runtime.compositionLocalOf
import androidx.navigation.NavHostController
import com.flutterup.base.permission.PermissionManager


val LocalAppState = compositionLocalOf<AppState> {
    error("No LocaleAppState provided")
}

val LocalNavController = compositionLocalOf<NavHostController> {
    error("No LocalNavController provided")
}

val LocalInnerPadding = compositionLocalOf<PaddingValues> {
    error("No LocalInnerPadding provided")
}

val LocalPermissionManager = compositionLocalOf<PermissionManager> {
    error("No LocalPermissionManager provided")
}

@ExperimentalSharedTransitionApi
val LocalSharedTransitionScope = compositionLocalOf<SharedTransitionScope> {
    error("No LocalSharedTransitionScope provided")
}