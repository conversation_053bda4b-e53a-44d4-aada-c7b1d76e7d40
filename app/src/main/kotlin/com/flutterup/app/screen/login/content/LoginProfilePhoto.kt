package com.flutterup.app.screen.login.content

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.calculateEndPadding
import androidx.compose.foundation.layout.calculateStartPadding
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.PinkSecondary
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.screen.login.state.LoginProfileStep2UIState

@Composable
internal fun LoginProfilePhoto(
    modifier: Modifier = Modifier,
    padding: PaddingValues,
    medias: List<LoginProfileStep2UIState.MediaStatus>,
    onClick: (index: Int) -> Unit
) {
    fun onItemClick(index: Int): () -> Unit = {
        if (medias.none { it is LoginProfileStep2UIState.MediaStatus.Loading }) {
            onClick(index)
        }
    }

    val density = LocalDensity.current
    val windowInfo = LocalWindowInfo.current

    val screenWidthDp = with(density) { windowInfo.containerSize.width.toDp() }

    //item是正方形的，整个grid也是正方形的，所以只需要获取到宽度，他的高度就是这么多
    val itemSize = (screenWidthDp -
            padding.calculateStartPadding(LayoutDirection.Ltr) -
            padding.calculateEndPadding(LayoutDirection.Ltr))

    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        verticalArrangement = Arrangement.spacedBy(10.dp),
        horizontalArrangement = Arrangement.spacedBy(10.dp),
        modifier = modifier
            .padding(padding)
            .fillMaxWidth()
            .height(itemSize)
    ) {
        items(
            count = 5,
            span = { if (it == 0) GridItemSpan(2) else GridItemSpan(1) }
        ) { index ->
            when(index) {
                1 -> Column(
                    verticalArrangement = Arrangement.spacedBy(10.dp)
                ) {
                    LoginProfilePhotoItem(
                        modifier = Modifier
                            .aspectRatio(1f)
                            .noRippleClickable(onClick = onItemClick(1)),
                        mediaStatus = medias.getOrNull(1),
                        isMainPhoto = false
                    )


                    LoginProfilePhotoItem(
                        modifier = Modifier
                            .aspectRatio(1f)
                            .noRippleClickable(onClick = onItemClick(2)),
                        mediaStatus = medias.getOrNull(2),
                        isMainPhoto = false
                    )
                }

                else -> {
                    val realIndex = if (index == 0) 0 else index + 1

                    LoginProfilePhotoItem(
                        modifier = Modifier
                            .aspectRatio(1f)
                            .padding(if (index == 0) 1.dp else 0.dp)
                            .noRippleClickable(onClick = onItemClick(realIndex)),
                        mediaStatus = medias.getOrNull(realIndex),
                        isMainPhoto = index == 0
                    )
                }
            }
        }
    }
}

@Composable
private fun LoginProfilePhotoItem(
    modifier: Modifier = Modifier,
    mediaStatus: LoginProfileStep2UIState.MediaStatus?,
    isMainPhoto: Boolean = false,
) {
    val stroke = Stroke(
        width =  with(LocalDensity.current) { 2.dp.toPx() },
        pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f)
    )

    val shape = RoundedCornerShape(20.dp)

    Box(
        modifier = modifier
            .background(
                color = PurplePrimary.copy(alpha = 0.21f),
                shape = shape
            )
    ) {
        Box(
            modifier = modifier
                .background(
                    brush = itemLinearGradient,
                    shape = shape
                )
                .then(
                    if (isMainPhoto) {
                        Modifier.drawBehind {
                            drawRoundRect(
                                color = PurplePrimary,
                                style = stroke,
                                cornerRadius = CornerRadius(20.dp.toPx())
                            )
                        }
                    } else {
                        Modifier
                    }
                )
        ) {
            Spacer(
                modifier = Modifier
                    .matchParentSize()
                    .background(
                        brush = itemVerticalGradient,
                        shape = shape
                    )
            )

            when (mediaStatus) {
                is LoginProfileStep2UIState.MediaStatus.Loading -> {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .size(35.dp)
                            .align(Alignment.Center),
                        color = Color.White,
                    )
                }

                is LoginProfileStep2UIState.MediaStatus.Success -> {
                    AsyncImage(
                        model = mediaStatus.url,
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier.matchParentSize().clip(shape)
                    )

                    Icon(
                        painter = painterResource(R.drawable.ic_photo_edit),
                        contentDescription = null,
                        tint = Color.Unspecified,
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(top = 8.dp, end = 16.dp)
                    )
                }
                else -> {
                    if (isMainPhoto) {
                        Icon(
                            painter = painterResource(R.drawable.ic_upload_pic),
                            contentDescription = null,
                            tint = PurplePrimary,
                            modifier = Modifier.size(50.dp)
                                .align(Alignment.Center)
                        )
                    } else {
                        Icon(
                            painter = painterResource(R.drawable.ic_logo),
                            contentDescription = null,
                            tint = Color.White.copy(alpha = 0.4f),
                            modifier = Modifier.size(50.dp)
                                .align(Alignment.Center)
                        )
                    }
                }
            }
        }
    }
}

private val itemLinearGradient = Brush.linearGradient(
    colors = listOf(
        PinkSecondary,
        Color.White.copy(alpha = 0.4f)
    )
)

private val itemVerticalGradient = Brush.verticalGradient(
    colors = listOf(
        PurplePrimary.copy(alpha = 0.21f),
        Color.Transparent
    )
)