package com.flutterup.app.screen.profile.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.preview.BooleanProvider
import com.flutterup.app.design.theme.TextBlack333
import com.flutterup.app.design.theme.TextBlack666
import com.flutterup.app.design.theme.TextGray999


@Composable
fun EditProfileItem(
    modifier: Modifier = Modifier,
    title: String,
    text: String,
    isHint: Boolean = false,
    isShowArrow: Boolean = false,
    isLoading: Boolean = false,
    onClick: () -> Unit = {},
) {
    Box(
        modifier = modifier.noRippleClickable(onClick = onClick)
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = title,
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 22.sp,
                    fontWeight = FontWeight.W400,
                    color = TextBlack666.copy(0.6f),
                ),
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(4.dp))

            Row(
                modifier = Modifier.fillMaxWidth()
                    .background(color = Color.White, shape = RoundedCornerShape(size = 16.dp))
                    .padding(vertical = 15.dp, horizontal = 12.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = text,
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight.W400,
                        color = if (isHint) TextBlack666.copy(0.6f) else TextBlack333,
                    )
                )

                when {
                    isLoading -> {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp,
                            color = TextGray999,
                        )
                    }

                    isShowArrow -> {
                        Icon(
                            painter = painterResource(R.drawable.ic_right_gray_arrow),
                            contentDescription = null,
                            modifier = Modifier,
                            tint = Color.Unspecified
                        )
                    }
                }
            }
        }
    }
}

@Preview(backgroundColor = 0xFF3333, showBackground = true)
@Composable
private fun EditProfileItemPreview(
    @PreviewParameter(BooleanProvider::class) isHint: Boolean,
) {
    EditProfileItem(
        modifier = Modifier.padding(horizontal = 20.dp),
        title = "test",
        text = "EXERCISE、ADVENTURE、FITNESS",
        isHint = isHint,
        isShowArrow = true
    )
}

@Preview(backgroundColor = 0xFF3333, showBackground = true)
@Composable
private fun EditProfileItemPreview2(
    @PreviewParameter(BooleanProvider::class) isLoading: Boolean,
) {
    EditProfileItem(
        modifier = Modifier.padding(horizontal = 20.dp),
        title = "test",
        text = "EXERCISE、ADVENTURE、FITNESS",
        isHint = true,
        isShowArrow = true,
        isLoading = isLoading,
    )
}

