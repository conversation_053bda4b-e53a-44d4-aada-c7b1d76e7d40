package com.flutterup.app.screen.chat.vm

import com.flutterup.app.screen.chat.state.ChatSystemUiState
import com.flutterup.app.utils.GlobalSettingsMonitor
import com.flutterup.app.utils.chat.core.ChatMessageMonitor
import com.flutterup.app.utils.chat.core.ChatOnReceivedMessageListener
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.imlib.model.ReceivedProfile
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ChatSystemConversationsViewModel @Inject constructor(
    private val chatMessageMonitor: ChatMessageMonitor,
    private val settingsMonitor: GlobalSettingsMonitor,
    private val chatConversationRepository: ChatConversationRepository,
) : BaseViewModel(), ChatOnReceivedMessageListener {


    private val _uiState = MutableStateFlow(ChatSystemUiState())
    val uiState: StateFlow<ChatSystemUiState> = _uiState.asStateFlow()

    init {
        chatMessageMonitor.addMessageListener(this)

        getSystemConversations()
    }

    fun getSystemConversations() {
        scope.launch {
            val systemAccount = settingsMonitor.noticeAccount.value ?: settingsMonitor.noticeAccount.first { it != null }
            val customServiceAccount = settingsMonitor.customerServiceAccount.value ?: settingsMonitor.customerServiceAccount.first { it != null }

            if (systemAccount == null || customServiceAccount == null) return@launch

            val systemConversation = chatConversationRepository
                .getConversation(systemAccount, Conversation.ConversationType.SYSTEM)
                ?: Conversation.obtain(Conversation.ConversationType.SYSTEM, systemAccount, "")

            val customServiceConversation = chatConversationRepository
                .getConversation(customServiceAccount, Conversation.ConversationType.PRIVATE)
                ?: Conversation.obtain(Conversation.ConversationType.PRIVATE, customServiceAccount, "")

            _uiState.update {
                it.copy(
                    systemConversation = systemConversation,
                    customServiceConversation = customServiceConversation,
                )
            }
        }
    }

    override fun onReceivedMessage(
        message: Message?,
        profile: ReceivedProfile?
    ) {
        if (message == null) return

        if (isFromSystemOrCustomService(message) || isSendToSystemOrCustomService(message)) {
            getSystemConversations()
        }
    }

    override fun onCleared() {
        chatMessageMonitor.removeMessageListener(this)
        super.onCleared()
    }

    private fun isFromSystemOrCustomService(message: Message): Boolean {
        return message.senderUserId == settingsMonitor.customerServiceAccount.value || message.senderUserId == settingsMonitor.noticeAccount.value
    }

    private fun isSendToSystemOrCustomService(message: Message): Boolean {
        return message.targetId == settingsMonitor.customerServiceAccount.value || message.targetId == settingsMonitor.noticeAccount.value
    }
}