package com.flutterup.app.screen.relate.vm

import com.flutterup.app.model.WinkReadType
import com.flutterup.app.model.WinkType
import com.flutterup.app.model.WinksEntity
import com.flutterup.app.network.ApiService
import com.flutterup.base.BaseRepository
import javax.inject.Inject

class WinksRepository @Inject constructor(
    private val apiService: ApiService
): BaseRepository() {

    suspend fun getWinksList(winkType: WinkType, lastId: Long? = null): WinksEntity? {
        val result = apiService.getWinksList(winkType.value, lastId)
        if (result.isSuccess) {
            return result.data
        }
        return null
    }

    suspend fun markAllWinksRead(winkReadType: WinkReadType): Boolean {
        return apiService.markAllWinksRead(winkReadType.value).isSuccess
    }
}