@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.PurpleCoverPrimary
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.login.content.LoginEmailPwdTextField
import com.flutterup.app.screen.login.state.LoginEmailUIState
import com.flutterup.app.screen.login.vm.LoginEmailViewModel


@Composable
fun LoginNormalScreen() {
    val navController = LocalNavController.current
    val viewModel: LoginEmailViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LoginNormalContent(
        uiState = uiState,
        onBackClick = { navController.popBackStack() },
        onEmailChange = { viewModel.updateEmail(it) },
        onPasswordChange = { viewModel.updatePassword(it) },
        onPasswordVisibilityClick = { viewModel.togglePasswordVisibility() },
        onLoginClick = { viewModel.login() },
    )
}


@Composable
private fun LoginNormalContent(
    uiState: LoginEmailUIState,
    onBackClick: () -> Unit = {},
    onEmailChange: (String) -> Unit = {},
    onPasswordChange: (String) -> Unit = {},
    onPasswordVisibilityClick: () -> Unit = {},
    onLoginClick: () -> Unit = {},
) {
    AppScaffold(
        title = { },
        onBackClick = onBackClick,
        modifier = Modifier.fillMaxSize(),
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
            containerColor = Color.Transparent,
        )
    ) {
        LoginNormalContent(
            topPaddingValue = it.calculateTopPadding(),
            uiState = uiState,
            onEmailChange = onEmailChange,
            onPasswordChange = onPasswordChange,
            onPasswordVisibilityClick = onPasswordVisibilityClick,
            onLoginClick = onLoginClick,
        )
    }
}

@Composable
private fun LoginNormalContent(
    topPaddingValue: Dp,
    uiState: LoginEmailUIState,
    onEmailChange: (String) -> Unit = {},
    onPasswordChange: (String) -> Unit = {},
    onPasswordVisibilityClick: () -> Unit = {},
    onLoginClick: () -> Unit = {},
) {
    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        LoginNormalTopContent(
            modifier = Modifier.align(Alignment.TopCenter)
        )

        LoginWelcome(
            modifier = Modifier.align(Alignment.TopCenter)
                .padding(top = topPaddingValue)
                .padding(top = 33.dp)
        )

        Column(
            modifier = Modifier.fillMaxSize()
                .padding(top = topPaddingValue)
                .padding(top = 137.dp)
                .clip(shape = RoundedCornerShape(
                    topStart = 32.dp,
                    topEnd = 32.dp,
                    bottomStart = 0.dp,
                    bottomEnd = 0.dp
                ))
                .background(Color.White)
                .align(Alignment.BottomCenter)
        ) {
            Spacer(Modifier.height(32.dp))

            LoginEmail(
                uiState = uiState,
                onEmailChange = onEmailChange,
            )

            Spacer(Modifier.height(20.dp))

            LoginPassword(
                uiState = uiState,
                onPasswordChange = onPasswordChange,
                onPasswordVisibilityClick = onPasswordVisibilityClick
            )

            Spacer(Modifier.height(40.dp))

            AppContinueButton(
                enabled = uiState.isLoginEnabled,
                isLoading = uiState.isLoading,
                onClick = onLoginClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(50.dp)
                    .padding(horizontal = 24.dp)
            )
        }
    }
}

@Composable
private fun LoginNormalTopContent(
    modifier: Modifier = Modifier
) {

    Box(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        Image(
            painter = painterResource(R.mipmap.ic_login_top_background),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(
            modifier = Modifier.matchParentSize()
                .background(PurpleCoverPrimary)
        )
    }
}

@Composable
private fun LoginWelcome(
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Image(
            painter = painterResource(R.drawable.ic_logo),
            contentDescription = null,
            modifier = Modifier
                .width(83.dp)
                .height(23.dp)
        )

        Text(
            text = stringResource(R.string.login_welcome),
            style = TextStyle(
                fontSize = 30.sp,
                lineHeight = 22.sp,
                fontWeight = FontWeight.W900,
                color = Color.White,
            )
        )
    }
}

@Composable
private fun LoginEmail(
    uiState: LoginEmailUIState,
    onEmailChange: (String) -> Unit = {},
) {
    LoginEmailPwdTextField(
        value = uiState.email,
        keyboardType = KeyboardType.Email,
        onValueChange = onEmailChange,
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        isError = !uiState.isEmailValid,
        placeholder = R.string.email,
        supportingText = R.string.login_email_format_error,
        leadingIcon = R.drawable.ic_email,
    )
}

@Composable
private fun LoginPassword(
    uiState: LoginEmailUIState,
    onPasswordChange: (String) -> Unit = {},
    onPasswordVisibilityClick: () -> Unit = {},
) {
    LoginEmailPwdTextField(
        value = uiState.password,
        keyboardType = KeyboardType.Password,
        onValueChange = onPasswordChange,
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        isError = !uiState.isPasswordValid,
        isSecurity = !uiState.isPasswordVisible,
        placeholder = R.string.password,
        supportingText = R.string.login_password_format_error,
        leadingIcon = R.drawable.ic_password,
        trailingIcon = if (uiState.isPasswordVisible) R.drawable.ic_password_shown else R.drawable.ic_password_hidden,
        onTrailingIconClick = onPasswordVisibilityClick
    )
}

@Preview
@Composable
private fun LoginNormalScreenPreview() {
    AppTheme {
        LoginNormalContent(
            topPaddingValue = 0.dp,
            uiState = LoginEmailUIState()
        )
    }
}