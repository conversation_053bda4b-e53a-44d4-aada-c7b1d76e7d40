package com.flutterup.app.screen.chat.vm

import com.flutterup.app.model.AppFrom
import com.flutterup.app.network.ApiService
import com.flutterup.base.BaseRepository
import javax.inject.Inject

class ChatMatchRepository @Inject constructor(
    private val apiService: ApiService,
) : BaseRepository() {

    suspend fun sendInstanceMessage(
        userId: String,
        message: String,
        from: AppFrom,
    ): Boolean {
        val result = apiService.sendInstantChatMessage(userId, message, from.value)
        return result.isSuccess
    }

    suspend fun sendGreetingMessage(
        userId: String,
        message: String
    ): <PERSON><PERSON>an {
        val result = apiService.sendGreetingMessage(userId, message)
        return result.isSuccess
    }
}