package com.flutterup.app.screen.profile

import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionScope
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.UserActionCardType
import com.flutterup.app.model.UserInfo
import com.flutterup.app.navigation.BottomNavRoute
import com.flutterup.app.screen.LocalSharedTransitionScope
import com.flutterup.app.screen.discover.DiscoverRoute
import com.flutterup.app.screen.login.LoginProfileStep2Route
import com.flutterup.app.screen.profile.vm.SharedProfileInterestsViewModel
import com.flutterup.app.screen.common.vm.ProfileSharedViewModel
import com.flutterup.app.utils.viewModelScopedTo
import kotlinx.serialization.Serializable

@Serializable data object ProfileRoute : BottomNavRoute

@Serializable
data object LoginProfileEditInterestsRoute

@Serializable
data object ProfileEditInterestsRoute

@Serializable
data class ProfileOtherRoute(
    val userId: String,
    val from: AppFrom = AppFrom.Unknown,
    val cardType: UserActionCardType = UserActionCardType.Default,
)

@Serializable
data class ProfileOtherPreRoute(
    val userId: String,
    val nickname: String,
    val age: Int,
    val headimg: String,
    val mediaList: List<String>,
    val intro: String?,
    val interests: List<String>?,
    val location: Int,
    val currentIndex: Int = 0,
    val from: AppFrom = AppFrom.Unknown,
    val cardType: UserActionCardType = UserActionCardType.Default,
) {
    constructor(userInfo: UserInfo, from: AppFrom) : this(
        userId = userInfo.userId.orEmpty(),
        nickname = userInfo.nickname.orEmpty(),
        age = userInfo.age ?: 0,
        headimg = userInfo.headImage.orEmpty(),
        mediaList = userInfo.mediaList?.map { it.imageUrl }.orEmpty(),
        intro = userInfo.sign,
        interests = userInfo.tags,
        location = userInfo.location ?: 0,
        from = from
    )
}

@Serializable
data object ProfileEditRoute

@Serializable
data class EditInputRoute(
    val sharedKey: String,
    val title: String? = null,
    val hint: String? = null,
    val text: String? = null,
    val limit: Int = Int.MAX_VALUE,
)

@OptIn(ExperimentalSharedTransitionApi::class)
fun NavGraphBuilder.profileGraph() {
    composable<LoginProfileEditInterestsRoute> {
        val viewModel: SharedProfileInterestsViewModel = it.viewModelScopedTo(LoginProfileStep2Route::class)

        EditInterestsScreen(viewModel)
    }

    composable<ProfileEditInterestsRoute> {
        val viewModel: SharedProfileInterestsViewModel = it.viewModelScopedTo(ProfileEditRoute::class)

        EditInterestsScreen(viewModel)
    }

    composable<ProfileOtherRoute> {
        val route = it.toRoute<ProfileOtherRoute>()

        OtherProfileScreen(route.userId, route.from, route.cardType)
    }

    composable<ProfileOtherPreRoute> {
        val route = it.toRoute<ProfileOtherPreRoute>()
        val mediaSharedViewModel: ProfileSharedViewModel = it.viewModelScopedTo(DiscoverRoute::class)
        val sharedTransitionScope = LocalSharedTransitionScope.current

        OtherProfileScreen(
            userId = route.userId,
            nickname = route.nickname,
            age = route.age,
            headimg = route.headimg,
            mediaList = route.mediaList,
            intro = route.intro,
            interests = route.interests,
            currentIndex = route.currentIndex,
            location = route.location,
            from = route.from,
            cardType = route.cardType,
            sharedTransitionScope = sharedTransitionScope,
            animatedContentScope = this,
            sharedViewModel = mediaSharedViewModel
        )
    }

    composable<ProfileEditRoute> {
        ProfileEditScreen()
    }

    composable<EditInputRoute> {
        val route = it.toRoute<EditInputRoute>()
        EditInputScreen(route.sharedKey, route.title, route.hint, route.text, route.limit)
    }
}