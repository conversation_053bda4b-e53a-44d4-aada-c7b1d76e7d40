package com.flutterup.app.screen.profile

import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.design.component.AppBottomSheetDialog
import com.flutterup.app.design.component.wheelpicker.WheelDatePicker
import com.flutterup.app.design.component.wheelpicker.core.WheelPickerDefaults
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.TextBlack333
import java.time.LocalDate

@ExperimentalMaterial3Api
@Composable
fun BirthdayDialog(
    isShown: Boolean,
    defaultDate: LocalDate?,
    onDismissRequest: () -> Unit,
    onBirthdayConfirmRequest: (birthday: LocalDate?) -> Unit,
) {
    BirthdayDialogContent(
        isShown = isShown,
        defaultDate = defaultDate,
        onDismissRequest = onDismissRequest,
        onBirthdayConfirmRequest = onBirthdayConfirmRequest,
    )
}

@ExperimentalMaterial3Api
@Composable
private fun BirthdayDialogContent(
    isShown: Boolean,
    defaultDate: LocalDate? = null,
    onDismissRequest: () -> Unit,
    onBirthdayConfirmRequest: (birthday: LocalDate?) -> Unit,
) {
    var currentBirthday by remember { mutableStateOf(defaultDate ?: DEFAULT_CENTER_YEAR) }

    BoxWithConstraints {
        val maxWidth = this.maxWidth

        AppBottomSheetDialog(
            isShown = isShown,
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            onDismissRequest = onDismissRequest,
            onConfirmRequest = {
                onBirthdayConfirmRequest(currentBirthday)
            },
            containerPaddingValues = PaddingValues(horizontal = 20.dp, vertical = 35.dp),
        ) {
            WheelDatePicker(
                modifier = Modifier.fillMaxWidth().wrapContentHeight(),
                size = DpSize(maxWidth, 260.dp),
                startDate = currentBirthday,
                minDate = DEFAULT_START_YEAR,
                maxDate = DEFAULT_END_YEAR,
                yearsRange = DEFAULT_RANGE,
                rowCount = 5,
                selectorProperties = WheelPickerDefaults.selectorProperties(enabled = false),
                selectorDividers = WheelPickerDefaults.selectorDividers(
                    enabled = true,
                    thickness = 1.dp,
                    color = Color(0xFFEEEEEE)
                ),
                onSnappedDate = {
                    currentBirthday = it
                },
                content = { index, text, textStyle, textColor ->
                    Text(
                        text = text,
                        style = TextStyle(
                            fontSize = 20.sp,
                            lineHeight = 20.sp,
                            fontWeight = FontWeight.W500,
                            color = TextBlack333,
                            textAlign = TextAlign.Center,
                        )
                    )
                }
            )
        }
    }
}

@ExperimentalMaterial3Api
@Preview
@Composable
private fun BirthdayDialogPreview() {
    AppTheme {
        BirthdayDialogContent(
            isShown = true,
            onDismissRequest = {},
            onBirthdayConfirmRequest = {},
        )
    }
}

private val DEFAULT_CENTER_YEAR = LocalDate.now().minusYears(25)
private val DEFAULT_START_YEAR = LocalDate.now().minusYears(70)
private val DEFAULT_END_YEAR = LocalDate.now().minusYears(18)
private val DEFAULT_RANGE = DEFAULT_START_YEAR.year..DEFAULT_END_YEAR.year
