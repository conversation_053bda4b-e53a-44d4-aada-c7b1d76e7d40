package com.flutterup.app.screen.chat.vm

import com.flutterup.app.model.UserInfo
import com.flutterup.app.network.ApiService
import com.flutterup.base.BaseRepository
import javax.inject.Inject

class ChatHomeRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {

    suspend fun getNewConnections(): List<UserInfo>? {
        val result = apiService.getNewConnections()
        if (result.isSuccess) {
            return result.data
        }
        return null
    }
}