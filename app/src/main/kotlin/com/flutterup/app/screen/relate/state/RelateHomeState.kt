package com.flutterup.app.screen.relate.state

import com.flutterup.app.R

data class RelateHomeState(
    val titles: List<RelateTitle>,

    val initialPage: Int = 0,
) {
    constructor() : this(listOf(WinksReceived.EMPTY, WinksSent.EMPTY, Visitors.EMPTY))
}

sealed class RelateTitle(
    open val titleRes: Int,
    open val count: Int,

    open val index: Int,
) {
    val existedNew: Boolean
        get() = count > 0
}

data class WinksReceived(override val count: Int = 0) : RelateTitle(R.string.winks_received, count, INDEX) {

    companion object {
        const val INDEX = 0
        val EMPTY = WinksReceived(0)
    }
}

data class WinksSent(override val count: Int = 0) : RelateTitle(R.string.winks_sent, count, INDEX) {
    companion object {
        const val INDEX = 1
        val EMPTY = WinksSent(0)
    }
}

data class Visitors(override val count: Int = 0) : RelateTitle(R.string.visitor, count, INDEX) {
    companion object {
        const val INDEX = 2
        val EMPTY = Visitors(0)
    }
}