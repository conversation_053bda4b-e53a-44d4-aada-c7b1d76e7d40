package com.flutterup.app.screen.profile.vm

import com.flutterup.app.model.Tag
import com.flutterup.app.screen.profile.state.ProfileInterestsUiState
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SharedProfileInterestsViewModel @Inject constructor(
    private val repository: ProfileRepository,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(ProfileInterestsUiState())
    val uiState: StateFlow<ProfileInterestsUiState> = _uiState.asStateFlow()

    init {
        scope.launch {
            val result = repository.getInterestList()
            _uiState.update { it.copy(interests = result) }
        }
    }

    /**
     * 移除缓存的标签
     */
    fun removeTag(tag: Tag) {
        _uiState.update {
            val newTags = it.currentInterests.toMutableList().also { list ->
                list.remove(tag)
            }
            it.copy(currentInterests = newTags)
        }
    }

    /**
     * 添加缓存的标签
     */
    fun addTag(tag: Tag) {
        _uiState.update {
            if (it.currentInterests.contains(tag)) return@update it

            val newTags = it.currentInterests.toMutableList().also { list ->
                list.add(tag)
            }
            it.copy(currentInterests = newTags)
        }
    }

    /**
     * 清空缓存的标签
     */
    fun removeAllCurrentTags() {
        _uiState.update { it.copy(currentInterests = emptyList()) }
    }

    /**
     * 确认缓存的标签，这会将缓存的标签添加到已确认的标签列表中
     */
    fun confirmTags() {
        _uiState.update { it.copy(confirmedInterests = it.currentInterests) }
    }

    /**
     * 更新缓存的标签
     */
    fun updateCurrentTags(tags: List<Tag>) {
        _uiState.update { it.copy(currentInterests = tags) }
    }

    fun updateCurrentTagsByString(tags: List<String>) {
        val currentTags = uiState.value.interests.filter { it.title in tags }
        _uiState.update { it.copy(currentInterests = currentTags) }
    }
}