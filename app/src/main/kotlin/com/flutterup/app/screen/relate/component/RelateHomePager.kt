package com.flutterup.app.screen.relate.component

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerScope
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.BackgroundBlackPrimary
import com.flutterup.app.design.theme.PinkPrimary
import com.flutterup.app.design.theme.TextPurpleGradientCenter
import com.flutterup.app.design.theme.TextPurpleGradientEnd
import com.flutterup.app.design.theme.TextPurpleGradientStart
import com.flutterup.app.screen.relate.state.RelateHomeState
import com.flutterup.app.screen.relate.state.RelateTitle
import com.flutterup.app.screen.relate.state.Visitors
import com.flutterup.app.screen.relate.state.WinksReceived
import com.flutterup.app.screen.relate.state.WinksSent
import kotlinx.coroutines.launch


@Composable
fun ColumnScope.RelateHomePager(
    uiState: RelateHomeState,
    pagerState: PagerState,
    pageContent: @Composable PagerScope.(page: Int, item: RelateTitle) -> Unit = { _, _ -> }
) {
    val scrollScope = rememberCoroutineScope()

    val shape = RoundedCornerShape(size = 8.dp)

    val normalModifier = Modifier
        .background(
            color = BackgroundBlackPrimary.copy(0.2f),
            shape = shape
        )


    val selectedModifier = Modifier
        .border(width = 0.5.dp, color = Color.White, shape = shape)
        .background(
            brush = SELECTED_BACKGROUND_BRUSH,
            shape = shape
        )

    //pager indicator
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp),
        horizontalArrangement = Arrangement.spacedBy(10.dp, Alignment.CenterHorizontally),
    ) {



        repeat(uiState.titles.size) { index ->
            val title = uiState.titles[index]
            val isSelected = index == pagerState.currentPage

            Box(
                modifier = Modifier.weight(1f).height(32.dp)
            ) {
                Box(
                    modifier = Modifier
                        .then(if (isSelected) selectedModifier else normalModifier)
                        .matchParentSize()
                        .clickable(
                            role = Role.Tab,
                            onClick = {
                                scrollScope.launch { pagerState.scrollToPage(index) }
                            }
                        ),
                    contentAlignment = Alignment.Center,
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        if (isSelected) {
                            Text(
                                text = stringResource(title.titleRes),
                                style = TextStyle(
                                    fontSize = 14.sp,
                                    lineHeight = 10.sp,
                                    fontWeight = FontWeight.Bold,
                                    fontStyle = FontStyle.Italic,
                                    brush = SELECTED_TEXT_BRUSH
                                )
                            )
                        } else {
                            Text(
                                text = stringResource(title.titleRes),
                                style = TextStyle(
                                    fontSize = 14.sp,
                                    lineHeight = 10.sp,
                                    fontWeight = FontWeight.Bold,
                                    fontStyle = FontStyle.Italic,
                                    color = Color.White.copy(0.4f),
                                )
                            )
                        }
                    }
                }

                if (title.existedNew) {
                    Box(
                        modifier = Modifier
                            .background(color = Color(0xFFFF4B4B), RoundedCornerShape(topEnd = 8.dp, bottomStart = 8.dp))
                            .padding(horizontal = 3.dp, vertical = 2.dp)
                            .align(Alignment.TopEnd),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(R.string.new_uppercase),
                            style = TextStyle(
                                fontSize = 10.sp,
                                lineHeight = 22.sp,
                                fontWeight = FontWeight.W800,
                                fontStyle = FontStyle.Italic,
                                color = Color.White,
                            )
                        )
                    }
                }
            }
        }
    }

    Spacer(Modifier.height(20.dp))

    Box(
        modifier = Modifier
            .background(PinkPrimary, RoundedCornerShape(topStart = 40.dp, topEnd = 40.dp))
            .fillMaxSize()
    ) {
        HorizontalPager(pagerState) {
            val item = uiState.titles[it]
            pageContent(it, item)
        }
    }
}

private val SELECTED_TEXT_BRUSH = Brush.verticalGradient(
    colors = listOf(
        TextPurpleGradientStart,
        TextPurpleGradientCenter,
        TextPurpleGradientEnd,
    ),
    startY = 0.93f
)

private val SELECTED_BACKGROUND_BRUSH = Brush.linearGradient(
    colors = listOf(
        Color.White,
        Color.White.copy(0.51f)
    )
)

@Preview
@Composable
private fun RelateHomePagerPreview() {
    AppTheme {
        Column {
            RelateHomePager(
                uiState = RelateHomeState(
                    titles = listOf(
                        WinksReceived(10),
                        WinksSent(0),
                        Visitors(0)
                    )
                ),
                pagerState = rememberPagerState(initialPage = 0) { 3 }
            )
        }
    }
}
