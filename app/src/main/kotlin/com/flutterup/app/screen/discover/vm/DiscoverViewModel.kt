package com.flutterup.app.screen.discover.vm

import android.content.Context
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import coil3.SingletonImageLoader
import coil3.request.ImageRequest
import com.flutterup.app.design.component.cardstack.Direction
import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.DiscoverItemEntity
import com.flutterup.app.model.UserActionCardType
import com.flutterup.app.model.UserActionType
import com.flutterup.app.model.UserRightsState
import com.flutterup.app.screen.discover.state.DiscoverState
import com.flutterup.app.screen.profile.vm.UserActionRepository
import com.flutterup.app.utils.EffectDiscoverMonitor
import com.flutterup.app.utils.EffectDiscoverType
import com.flutterup.app.utils.EffectDiscoverUser
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseRepositoryViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.subscribe
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class DiscoverViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val repository: DiscoverRepository,
    private val actionRepository: UserActionRepository,
    private val userMonitor: UserMonitor,
    private val effectDiscoverMonitor: EffectDiscoverMonitor,
) : BaseRepositoryViewModel(repository, actionRepository) {
    private val _uiState = MutableStateFlow(DiscoverState.EMPTY)
    private val cachedDiscovers = mutableSetOf<String>()

    val uiState: StateFlow<DiscoverState> = combine(
        _uiState,
        loadingState,
        userMonitor.userInfoState
    ) { ui, loading, userInfo ->
        ui.copy(
            isLoading = loading,
            headimg = userInfo?.headImage,
            nickname = userInfo?.nickname
        )
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value.copy(isLoading = loadingState.value)
    )

    val rights: StateFlow<UserRightsState> = userMonitor.userInfoState.map { userInfo ->
        UserRightsState(userInfo?.right)
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        UserRightsState(userMonitor.userInfo?.right)
    )

    val effect: StateFlow<EffectDiscoverUser?> = effectDiscoverMonitor.effect
        .stateIn(
            scope,
            SharingStarted.Eagerly,
            null
        )

    private var lastId: MutableState<String?> = mutableStateOf(null)

    init {
        getDiscoverList()
    }

    fun getDiscoverList() {
        scope.launchWithLoading {
            val result = repository.getDiscoverList(lastId.value)
            if (result != null) {
                result.rights?.let { rights ->
                    userMonitor.updateUserRight(rights)
                }

                mergedDiscoverList(result.matchList)
            }
        }
    }

    fun onSwiped(direction: Direction, onSuccess: () -> Unit = {}) {
        val matchList = _uiState.value.data.matchList
        val first = matchList.firstOrNull() ?: return

        val userId = first.userId ?: return
        val type = if (direction == Direction.Right) UserActionType.Like else UserActionType.Dislike

        val cardType = if (first.type != 0) UserActionCardType.Connected else UserActionCardType.Default

        dropCard(first)
        scope.launch {
            try {
                val isSuccess = actionRepository.userAction(
                    userId = userId,
                    type = type,
                    from = AppFrom.Discover,
                    cardFlag = cardType
                )

                if (isSuccess) {
                    onSuccess()
                } else {
                    restoreCard(first)
                }
            } catch (e: Exception) { //报错了
                restoreCard(first)
                throw e //重新抛出，交给全局异常处理去解决
            }
        }.invokeOnCompletion {
            val newMatchList = _uiState.value.data.matchList
            if (it == null && newMatchList.isEmpty()) {
                getDiscoverList()
            }
        }
    }

    fun onSwipedAutomatic() {
        val matchList = _uiState.value.data.matchList
        val first = matchList.firstOrNull() ?: return
        dropCard(first)
    }

    /**
     * 合并新的数据到旧的数据中
     */
    private fun mergedDiscoverList(newList: List<DiscoverItemEntity>) {
        if (newList.isEmpty()) return //不需要合并

        val oldList = _uiState.value.data.matchList
        val mergedList = (oldList + newList).distinctBy { it.userId }.filterNot { it.userId == null }
        _uiState.update { it.copy(data = it.data.copy(matchList = mergedList)) }
        lastId.value = newList.lastOrNull()?.userId

        coilPreloadCardResource(newList)
    }

    private fun dropCard(card: DiscoverItemEntity) {
        val list = _uiState.value.data.matchList
        val newList = list.filterNot { it.userId == card.userId }
        _uiState.update { it.copy(data = it.data.copy(matchList = newList)) }
    }

    private fun restoreCard(card: DiscoverItemEntity) {
        val list = _uiState.value.data.matchList
        val newList = list.toMutableList().also { it.add(0, card) }
        _uiState.update { it.copy(data = it.data.copy(matchList = newList)) }
    }

    private fun coilPreloadCardResource(discovers: List<DiscoverItemEntity>) {
        val topTwoDiscovers = discovers.take(2)
        val uncachedDiscovers = topTwoDiscovers.filterNot { cachedDiscovers.contains(it.userId) }
        if (uncachedDiscovers.isEmpty()) return

        val imageLoader = SingletonImageLoader.get(context)

        uncachedDiscovers.forEach {
            it.mediaList.forEach { media ->
                val request = ImageRequest.Builder(context)
                    .data(media.imageUrl)
                    .build()
                imageLoader.enqueue(request)
            }
        }
    }
}