package com.flutterup.app.screen.payment.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import com.flutterup.app.R
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.modifiers.topCurvedBackground
import com.flutterup.app.design.preview.BooleanProvider
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.TextBlack666
import com.flutterup.app.design.theme.TextBrownVipSecondary
import com.flutterup.app.design.theme.TextGray999
import com.flutterup.app.screen.payment.state.PaymentSubscriptUiState


@Composable
fun SubscriptionStores(
    modifier: Modifier = Modifier,
    uiState: PaymentSubscriptUiState,
    crossHeight: Dp,
    backgroundColor: Color,
    onItemClick: (Int) -> Unit = {},
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .defaultMinSize(minHeight = 300.dp)
            .offset(y = -crossHeight)
            .topCurvedBackground(
                curveCornerRadius = 6.dp,
                backgroundColor = backgroundColor
            )
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            Spacer(Modifier.height(23.dp))

            Row(
                modifier = Modifier.fillMaxWidth().padding(horizontal = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(10.dp),
            ) {
                repeat(uiState.items.size) { index ->
                    val item = uiState.items[index]
                    SubscriptionItem(
                        modifier = Modifier
                            .weight(1f)
                            .height(140.dp)
                            .noRippleClickable(onClick = { onItemClick(index) }),
                        isSelected = index == uiState.selected,
                        isPopular = item.hot == 1,
                        time = item.time.orEmpty(),
                        unit = item.unit.orEmpty(),
                        save = item.save,
                        prince = item.price.orEmpty(),
                        average = item.average.orEmpty()
                    )
                }
            }

            if (uiState.description != null) {
                Spacer(modifier = Modifier.height(17.dp))

                Text(
                    text = uiState.description,
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.W400,
                        color = TextGray999,
                        textAlign = TextAlign.Justify,
                    ),
                    modifier = Modifier.padding(start = 20.dp, end = 13.dp).fillMaxWidth()
                )
            }
        }
    }
}

@Composable
private fun SubscriptionItem(
    modifier: Modifier = Modifier,
    cornerRadius: Dp = 20.dp,
    isSelected: Boolean,
    isPopular: Boolean,
    time: String,
    unit: String,
    prince: String,
    average: String,
    save: String? = null,
) {

    val shape = RoundedCornerShape(cornerRadius)

    val borderBrush = if (isSelected) {
        SubscriptionItemBorderBrushSelected
    } else {
        SubscriptionItemBorderBrushUnselected
    }

    Box(modifier = modifier) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = SubscriptionItemBackgroundBrush,
                    shape = shape
                )
                .border(width = 1.dp, brush = borderBrush, shape = shape)
        ) {
            Image(
                painter = painterResource(R.drawable.ic_subscript_item_diamond),
                contentDescription = null,
                modifier = Modifier
                    .padding(top = 3.dp, end = 1.dp)
                    .align(Alignment.TopEnd)
            )

            ConstraintLayout(Modifier.fillMaxSize()) {
                val (timeElement, unitElement, saveElement) = createRefs()
                val (priceElement, averageElement) = createRefs()

                Text(
                    text = time,
                    style = TextStyle(
                        fontSize = 40.sp,
                        fontWeight = FontWeight.W700,
                        color = if (isSelected) TextBrownVipSecondary else TextBlack666,
                    ),
                    modifier = Modifier
                        .padding(top = 22.dp)
                        .constrainAs(timeElement) {
                            top.linkTo(parent.top)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                )

                Text(
                    text = unit,
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W400,
                        color = if (isSelected) TextBrownVipSecondary else TextBlack666,
                    ),
                    modifier = Modifier.constrainAs(unitElement) {
                        top.linkTo(timeElement.bottom)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
                )

                if (save != null) {
                    Text(
                        text = save,
                        style = TextStyle(
                            fontSize = 12.sp,
                            lineHeight = 16.sp,
                            fontWeight = FontWeight.W400,
                            color = TextBrownVipSecondary,
                            textAlign = TextAlign.Center,
                        ),
                        modifier = Modifier
                            .background(if (isSelected) Color(0xFFEFC8AE) else Color(0xFFFAE4D6))
                            .constrainAs(saveElement) {
                                top.linkTo(unitElement.bottom)
                                start.linkTo(parent.start)
                                end.linkTo(parent.end)
                            }
                            .padding(vertical = 1.dp)
                            .fillMaxWidth()
                    )
                }


                Text(
                    text = prince,
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.W400,
                        color = Color.Black
                    ),
                    modifier = Modifier
                        .constrainAs(priceElement) {
                            bottom.linkTo(averageElement.top)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                )

                Text(
                    text = average,
                    style = TextStyle(
                        fontSize = 10.sp,
                        lineHeight = 14.sp,
                        fontWeight = FontWeight.W400,
                        color = TextGray999,
                    ),
                    modifier = Modifier
                        .padding(bottom = 8.dp)
                        .constrainAs(averageElement) {
                            bottom.linkTo(parent.bottom)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                )
            }
        }

        if (isPopular) {
            PopularLabel(
                modifier = Modifier.size(width = 68.dp, height = 22.dp),
                cornerRadius = cornerRadius
            )
        }
    }
}

@Composable
private fun PopularLabel(modifier: Modifier, cornerRadius: Dp) {
    Box(
        modifier = modifier.background(
            brush = Brush.horizontalGradient(listOf(
                Color(0xFFC37066),
                Color(0xFFBF6C48)
            )),
            shape = RoundedCornerShape(
                topStart = cornerRadius,
                topEnd = 0.dp,
                bottomStart = 0.dp,
                bottomEnd = cornerRadius,
            )
        ),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = stringResource(R.string.popular).uppercase(),
            style = TextStyle(
                fontSize = 12.sp,
                lineHeight = 16.sp,
                fontWeight = FontWeight.W700,
                fontStyle = FontStyle.Italic,
                color = Color.White,
            )
        )
    }
}

private val SubscriptionItemBackgroundBrush = Brush.verticalGradient(
    listOf(
        Color(0xFFFCE7DD),
        Color(0xFFFEF6F4)
    )
)

private val SubscriptionItemBorderBrushSelected = Brush.sweepGradient(
    listOf(
        Color(0xFFD5805C),
        Color(0xFFE8BA93),
        Color(0xFFD5815D),
    )
)

private val SubscriptionItemBorderBrushUnselected = Brush.sweepGradient(
    listOf(
        Color(0xFFD5805C),
        Color(0xFFFFE2D4),
        Color(0xFFE8BA93)
    )
)


@Preview
@Composable
private fun SubscriptionItemPreview(
    @PreviewParameter(BooleanProvider::class) isSelected: Boolean,
) {
    AppTheme {
        SubscriptionItem(
            modifier = Modifier.size(width = 110.dp, height = 140.dp),
            isSelected = isSelected,
            isPopular = true,
            time = "1",
            unit = "Month",
            save = "SAVE 63%",
            prince = "$ 99.99",
            average = "or 7.99 $ / w."
        )
    }
}