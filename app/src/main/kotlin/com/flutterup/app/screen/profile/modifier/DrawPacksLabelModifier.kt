package com.flutterup.app.screen.profile.modifier

import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.unit.dp

internal fun Modifier.drawPacksLabel(): Modifier {
    return drawWithContent {
        val cornerRadius = 10.dp.toPx()

        // 定义每个角的圆角
        val roundRect = RoundRect(
            rect = Rect(0f, 0f, size.width, size.height),
            topLeft = CornerRadius.Zero,
            topRight = CornerRadius(cornerRadius, cornerRadius),
            bottomRight = CornerRadius.Zero,
            bottomLeft = CornerRadius(cornerRadius, cornerRadius)
        )

        // 转成 path
        val path = Path().apply {
            addRoundRect(roundRect)
        }

        // 在内容上绘制
        drawPath(
            path = path,
            brush = Brush.verticalGradient(
                colors = listOf(
                    Color(0xFFC37066),
                    Color(0xFFF2C9B3)
                )
            )
        )

        drawPath(
            path = path,
            brush = Brush.radialGradient(
                colors = listOf(
                    Color(0xFFF6D0C2),
                    Color.Black
                )
            ),
            alpha = 0.5f,
            blendMode = BlendMode.Screen
        )

        drawPath(
            path = path,
            brush = Brush.verticalGradient(
                colors = listOf(
                    Color.Transparent,
                    Color(0XFFFFD4CD)
                )
            ),
            alpha = 0.5f,
            blendMode = BlendMode.Multiply
        )

        drawPath(
            path = path,
            color = Color(0x33FF8400)
        )

        drawContent()
    }
}