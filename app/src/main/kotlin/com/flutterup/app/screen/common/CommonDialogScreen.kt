package com.flutterup.app.screen.common

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.flutterup.app.R
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.design.theme.TextBlack333
import com.flutterup.app.design.theme.TextBlack666


@Composable
fun CommonDialogScreen(
    @DrawableRes icon: Int? = null,
    title: String? = null,
    content: String? = null,
    positiveText: String? = null,
    negativeText: String? = null,
    properties: DialogProperties = DialogProperties(),
    onDismissRequest: () -> Unit = {},
    onPositiveClick: () -> Unit = {},
    onNegativeClick: () -> Unit = {},
) {
    Dialog(
        onDismissRequest = onDismissRequest,
        properties = properties,
    ) {
        CommonDialogContent(
            icon = icon,
            title = title,
            content = content,
            positiveText = positiveText,
            negativeText = negativeText,
            onPositiveClick = onPositiveClick,
            onNegativeClick = onNegativeClick,
        )
    }
}

@Composable
private fun CommonDialogContent(
    @DrawableRes icon: Int? = null,
    title: String? = null,
    content: String? = null,
    positiveText: String? = null,
    negativeText: String? = null,
    onPositiveClick: () -> Unit = {},
    onNegativeClick: () -> Unit = {},
) {
    var iconWidth by remember { mutableIntStateOf(0) }
    var contentWidth by remember { mutableIntStateOf(0) }
    var contentHeight by remember { mutableIntStateOf(0) }

    Box {
        Box(
            modifier = Modifier
                .padding(top = with(LocalDensity.current) { iconWidth.toDp() / 2 })
                .padding(horizontal = 20.dp)
                .fillMaxWidth()
                .wrapContentHeight()
                .drawWithContent {
                    drawRoundRect(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                Color(0xFFCBC5F8),
                                Color.White
                            ),
                            endY = contentHeight * 0.3f
                        ),
                        cornerRadius = CornerRadius(8.dp.toPx())
                    )

                    drawRoundRect(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                Color(0xFF67EDFF),
                                Color.Black
                            ),
                            center = Offset(x = contentWidth * 0.9f, y = 0f)
                        ),
                        cornerRadius = CornerRadius(8.dp.toPx()),
                        blendMode = BlendMode.Screen
                    )

                    drawContent()
                }
                .padding(bottom = 16.dp)
                .onSizeChanged {
                    contentWidth = it.width
                    contentHeight = it.height
                }
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxWidth()
            ) {
                if (title != null) {
                    Text(
                        text = title,
                        style = TextStyle(
                            fontSize = 17.sp,
                            lineHeight = 22.sp,
                            fontWeight = FontWeight.W600,
                            color = TextBlack333,
                        ),
                        modifier = Modifier
                            .padding(top = if (icon != null) 60.dp else 16.dp)
                            .padding(horizontal = 16.dp)
                    )
                }

                if (content != null) {
                    Text(
                        text = content,
                        style = TextStyle(
                            fontSize = 13.sp,
                            lineHeight = 18.sp,
                            fontWeight = FontWeight.W400,
                            color = TextBlack666,
                            textAlign = TextAlign.Center,
                        ),
                        modifier = Modifier
                            .padding(top = 20.dp)
                            .padding(horizontal = 16.dp)
                    )
                }

                Row(
                    modifier = Modifier
                        .padding(top = 32.dp)
                        .padding(horizontal = 16.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    if (negativeText != null) {
                        Box(
                            Modifier
                                .border(
                                    width = 1.dp,
                                    color = PurplePrimary,
                                    shape = RoundedCornerShape(size = 23.dp)
                                )
                                .weight(1f)
                                .height(46.dp)
                                .noRippleClickable(onClick = onNegativeClick),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = negativeText,
                                style = TextStyle(
                                    fontSize = 16.sp,
                                    lineHeight = 22.sp,
                                    fontWeight = FontWeight.W400,
                                    color = PurplePrimary,
                                    textAlign = TextAlign.Center,
                                ),
                            )
                        }
                    }

                    if (positiveText != null) {
                        Box(
                            modifier = Modifier
                                .background(
                                    color = PurplePrimary,
                                    shape = RoundedCornerShape(23.dp)
                                )
                                .weight(1f)
                                .height(46.dp)
                                .noRippleClickable(onClick = onPositiveClick),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = positiveText,
                                style = TextStyle(
                                    fontSize = 16.sp,
                                    lineHeight = 22.sp,
                                    fontWeight = FontWeight.W400,
                                    color = Color.White,
                                    textAlign = TextAlign.Center,
                                )
                            )
                        }
                    }
                }
            }
        }

        if (icon != null) {
            Image(
                painter = painterResource(icon),
                contentDescription = null,
                modifier = Modifier
                    .size(79.dp)
                    .align(Alignment.TopCenter)
                    .onSizeChanged {
                        iconWidth = it.width
                    }
            )
        }
    }
}

@Preview
@Composable
private fun CommonDialogScreenPreview() {
    Surface {
        Box(
            modifier = Modifier.fillMaxSize().background(color = Color.Black),
            contentAlignment = Alignment.Center
        ) {
            CommonDialogContent(
                icon = R.mipmap.ic_app_update_logo,
                title = "System Upgrade",
                content = "A new version has been released, go upgrade the app now",
                negativeText = "Cancel",
                positiveText = "Upgrade",
            )
        }
    }
}
