package com.flutterup.app.screen.chat.component.message

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.haze.DefaultHazeStyle
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.TextGrayE5
import com.flutterup.app.model.MediaMessageEntity
import com.flutterup.app.model.MultiMediaMessageEntity
import com.flutterup.chat.message.content.MultiPrivateMessageContent
import com.flutterup.chat.message.content.PrivateMessageContent
import dev.chrisbanes.haze.hazeEffect
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch


@Composable
fun ChatMessageLockPrivateBubble(
    state: LazyListState,
    messages: List<Message>,
    parentMessage: List<Message>,
    modifier: Modifier = Modifier,
    scope: CoroutineScope = rememberCoroutineScope()
) {
    var firstVisibleItemIndex by remember { mutableIntStateOf(0) }

    Row(
        modifier = modifier
            .background(
                brush = Brush.linearGradient(listOf(
                    Color(0xFF988DE2),
                    Color(0xFF56369D)
                )),
                shape = RoundedCornerShape(topStart = 22.dp, bottomStart = 22.dp)
            )
            .wrapContentSize()
            .padding(start = 12.dp, end = 5.dp, top = 3.dp, bottom = 3.dp)
            .noRippleClickable {
                val message = messages.getOrNull(firstVisibleItemIndex) ?: return@noRippleClickable
                val indexInParent = parentMessage.indexOf(message)
                if (indexInParent == -1) return@noRippleClickable

                //跳转到指定位置
                scope.launch {
                    state.scrollToItem(indexInParent)

                    if (firstVisibleItemIndex == messages.size - 1) { //移动到最初位置
                        firstVisibleItemIndex = 0
                    } else { //移动到下一个位置
                        firstVisibleItemIndex += 1
                    }
                }
            },
    ) {
        Column {
            Row {
                Image(
                    painter = painterResource(R.mipmap.ic_chat_private_bubble_lock),
                    contentDescription = null,
                    modifier = Modifier.size(12.dp)
                )

                Spacer(Modifier.width(5.dp))

                Text(
                    text = stringResource(R.string.chat_lock_private_index_sub, firstVisibleItemIndex + 1, messages.size),
                    style = TextStyle(
                        fontSize = 11.sp,
                        lineHeight = 18.sp,
                        fontWeight = FontWeight.W400,
                        color = Color.White,
                    )
                )
            }

            Spacer(Modifier.height(2.dp))

            Text(
                text = stringResource(R.string.chat_lock_private_hint),
                style = TextStyle(
                    fontSize = 10.sp,
                    lineHeight = 13.sp,
                    fontWeight = FontWeight.W400,
                    color = TextGrayE5,
                )
            )
        }

        Spacer(Modifier.width(4.dp))

        AsyncImage(
            model = getMessageImage(messages.getOrNull(firstVisibleItemIndex)),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .border(0.5.dp, Color.White, RoundedCornerShape(4.dp))
                .size(23.dp)
                .clip(RoundedCornerShape(4.dp))
                .hazeEffect(DefaultHazeStyle) {
                    blurEnabled = true
                }
                .align(Alignment.CenterVertically)
        )
    }
}


fun getMessageImage(message: Message?): String? {
    if (message == null) return null

    return when(val content = message.content) {
        is PrivateMessageContent -> {
            val data = content.get(MediaMessageEntity::class.java)
            data?.url
        }

        is MultiPrivateMessageContent -> {
            val data = content.get(MultiMediaMessageEntity::class.java)
            data?.entities?.firstOrNull()?.url
        }

        else -> null
    }
}

@Preview
@Composable
private fun ChatMessageLockPrivateBubblePreview() {
    val messages = List(10) {
        Message.obtain("", Conversation.ConversationType.PRIVATE, PrivateMessageContent())
    }

    ChatMessageLockPrivateBubble(
        state = rememberLazyListState(),
        messages = messages,
        parentMessage = messages
    )
}