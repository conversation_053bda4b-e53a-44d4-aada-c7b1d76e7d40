package com.flutterup.app.screen.chat.state

import com.flutterup.app.model.GreetingItem
import com.flutterup.app.model.UserInfo
import com.flutterup.app.utils.chat.message.PrivateMessageLockStatus
import com.flutterup.app.utils.chat.message.isChatWithoutLimit
import com.flutterup.app.utils.chat.message.lockStatus
import com.flutterup.chat.message.content.BaseMessageType
import com.flutterup.chat.message.content.ConnectBaseMessageContent
import com.flutterup.gifts.entity.GiftEntity
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message


data class ChatMessageUiState(

    // region chat
    val isCustomerServiceConversation: Boolean = false,

    val mineUserinfo: UserInfo? = null,

    val otherUserinfo: UserInfo? = null,

    val conversation: Conversation? = null,

    val messages: List<Message> = emptyList(),

    val inputValue: String = "",

    // 添加时间戳强制触发重新组合
    val lastUpdateTime: Long = System.currentTimeMillis(),
    val blocked: Boolean = false,

    val isRefreshing: Boolean = false,
    val isLoadingMore: Boolean = false,
    val hasNoMoreData: Boolean = false,
    // endregion chat

    // region greeting
    val greetings: List<GreetingItem> = emptyList(),
    // endregion greeting

    // region private moments bubble
    val privateMomentsShowMinNum: Int = 0,
    val privateMomentsShowDelayTime: Int = 0,
    // endregion private moments bubble

    // region gift
    val currentPlayerGift: GiftEntity? = null,
    // endregion gift
) {

    val isOnlyNormalMoments: Boolean = mineUserinfo?.isAly() == true || isCustomerServiceConversation

    val isVip: Boolean = mineUserinfo?.right?.vip == 1

    val greetingEnable: Boolean = isNoneGreeting() && greetings.isNotEmpty()

    val lockedPrivateMoments: List<Message> = initLockedPrivateMoments()

    val isChatWithoutLimit: Boolean = containsChatWithoutLimit()

    /**
     * 是否未打招呼
     * 1. 有会话
     * 2. 消息为空 or 只有一条建联消息 or 用户没有发过消息
     */
    private fun isNoneGreeting(): Boolean {
        if (conversation == null || messages.isEmpty()) return false

        if (isCustomerServiceConversation) return false

        //只有一条建联消息
        if (messages.size == 1 && messages.first().content is ConnectBaseMessageContent) return true

        //用户没有发过消息
        if (messages.none { it.messageDirection == Message.MessageDirection.SEND }) return true

        return false
    }

    /**
     * 初始化锁定的私密照片
     * 1. 开关是否打开
     */
    private fun initLockedPrivateMoments(): List<Message> {
        val enable = privateMomentsShowMinNum != 0 && privateMomentsShowDelayTime != 0
        if (!enable) return emptyList()

        val lockedMessages = messages
            .filter {
                it.content is BaseMessageType.Private //私密消息
            }
            .filter {
                it.messageDirection == Message.MessageDirection.RECEIVE //是接收方
            }
            .filter {
                it.lockStatus() == PrivateMessageLockStatus.LOCKED //锁定状态
            }
            .filter {
                isShouldShowNotificationBubble(it) //应该显示提示气泡
            }

        if (lockedMessages.size >= privateMomentsShowMinNum) { //符合条件的message
            return lockedMessages
        }
        return emptyList()
    }

    /**
     * 是否应该显示提示气泡
     */
    private fun isShouldShowNotificationBubble(message: Message): Boolean {
        val sentTime = message.receivedTime
        val currentTime = System.currentTimeMillis()

        return currentTime - sentTime < privateMomentsShowDelayTime * 1000
    }

    private fun containsChatWithoutLimit(): Boolean {
        if (isVip) return false

        return messages.any { it.isChatWithoutLimit() }
    }
}