package com.flutterup.app.screen.relate.vm

import com.flutterup.app.model.WinkReadType
import com.flutterup.app.screen.relate.state.VisitorRandom
import com.flutterup.app.screen.relate.state.VisitorState
import com.flutterup.app.utils.UserMonitor
import com.flutterup.app.utils.UserUnreadMonitor
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class VisitorViewModel @Inject constructor(
    private val repository: VisitorRepository,
    private val winksRepository: WinksRepository,
    userMonitor: User<PERSON>onitor,
    userUnreadMonitor: UserUnreadMonitor
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(VisitorState.EMPTY)

    val uiState: StateFlow<VisitorState> = combine(
        _uiState,
        userUnreadMonitor.unreadCount,
        userMonitor.userInfoState
    ) { ui, unread, userInfo ->
        ui.copy(
            visitorNewCount = unread.visitorNewNum,
            userAvatar = userInfo?.headImage,
            visitorLatestAvatar = unread.headList.firstOrNull(),
            random = unread.headList.map { VisitorRandom(it) },
            isVip = userInfo?.right?.vip == 1
        )
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value
    )

    init {
        refresh()
    }

    fun refresh() {
        scope.launch {
            _uiState.update { it.copy(isRefreshing = true) }
            val result = repository.getVisitorList()
            result?.let { result ->
                _uiState.update { it.copy(visitors = result.list.orEmpty()) }
            }
        }.invokeOnCompletion {
            _uiState.update { it.copy(isRefreshing = false) }
            markAllAsRead(isUpdateUi = false)
        }
    }

    fun loadMore() {
        scope.launch {
            _uiState.update { it.copy(isLoadingMore = true) }
            val lastId = _uiState.value.visitors.lastOrNull()?.id
            val result = repository.getVisitorList(lastId)
            result?.let { result ->
                _uiState.update { it.copy(visitors = it.visitors + result.list.orEmpty()) }
            }
        }.invokeOnCompletion {
            _uiState.update { it.copy(isLoadingMore = false) }
        }
    }

    fun markAllAsRead(isUpdateUi: Boolean = true) {
        scope.launch {
            val readWinksResult = winksRepository.markAllWinksRead(WinkReadType.Visitor)
            if (readWinksResult && isUpdateUi) {
                _uiState.update { it.copy(visitors = it.visitors.map { entity -> entity.copy(read = 1) }) }
            }
        }
    }
}