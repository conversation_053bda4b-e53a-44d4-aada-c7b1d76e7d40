package com.flutterup.app.screen.profile.vm

import com.flutterup.app.model.Tag
import com.flutterup.app.model.UserInfo
import com.flutterup.app.network.ApiService
import com.flutterup.app.network.GlobalApiService
import com.flutterup.base.BaseRepository
import javax.inject.Inject

class ProfileRepository @Inject constructor(
    private val globalApiService: GlobalApiService,
    private val apiService: ApiService,
) : BaseRepository() {

    suspend fun getInterestList(): List<Tag> {
        val result = globalApiService.getInterestList()
        return result.data ?: emptyList()
    }

    suspend fun getMineProfileInfo(): UserInfo? {
        val result = apiService.getMineProfileInfo()
        if (result.isSuccess) {
            return result.data
        }
        return null
    }
}