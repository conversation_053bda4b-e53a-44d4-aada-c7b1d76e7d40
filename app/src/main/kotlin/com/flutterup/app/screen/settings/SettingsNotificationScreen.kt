@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.settings

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppTitleText
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.SwitchPrimary
import com.flutterup.app.design.theme.SwitchSecondary
import com.flutterup.app.design.theme.TextBlack333
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.settings.state.NotificationState
import com.flutterup.app.screen.settings.vm.SettingsNotificationViewModel

@Composable
fun SettingsNotificationScreen() {
    val navController = LocalNavController.current
    val viewModel: SettingsNotificationViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    SettingsNotificationContent(
        uiState = uiState,
        onBackClick = navController::popBackStack,
        onSwitchChange = { type, checked ->
            viewModel.updateNotification(type, checked)
        }
    )
}

@Composable
private fun SettingsNotificationContent(
    uiState: NotificationState,
    onBackClick: () -> Unit = {},
    onSwitchChange: (type: NotificationState.NotificationType, checked: Boolean) -> Unit = { _, _ -> },
) {
    val shape = RoundedCornerShape(16.dp)

    AppScaffold(
        title = { AppTitleText(stringResource(R.string.notification)) },
        onBackClick = onBackClick,
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) { paddingValues ->
        Box(Modifier.fillMaxSize()) {
            Image(
                painter = painterResource(R.mipmap.ic_common_top_bg),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth().align(Alignment.TopCenter)
            )

            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(top = 16.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp)
            ) {
                items(uiState.types) { data ->

                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp)
                            .height(50.dp)
                            .background(Color.White, shape),
                    ) {
                        Text(
                            text = stringResource(data.titleRes),
                            style = TextStyle(
                                fontSize = 16.sp,
                                fontWeight = FontWeight.W400,
                                color = TextBlack333,
                            ),
                            modifier = Modifier
                                .padding(start = 20.dp)
                                .align(Alignment.CenterStart)
                        )

                        Switch(
                            checked = data.checked(pushConfig = uiState.pushConfig),
                            enabled = !uiState.isLoading,
                            onCheckedChange = { onSwitchChange(data, it) },
                            modifier = Modifier
                                .align(Alignment.CenterEnd)
                                .padding(end = 20.dp)
                                .size(width = 36.dp, height = 20.dp),
                            thumbContent = {
                                Box(
                                    modifier = Modifier.size(20.dp)
                                )
                            },
                            colors = SwitchDefaults.colors(
                                checkedThumbColor = Color.White,
                                checkedBorderColor = Color.Transparent,
                                checkedTrackColor = SwitchPrimary,
                                checkedIconColor = Color.Unspecified,
                                uncheckedThumbColor = Color.White,
                                uncheckedTrackColor = SwitchSecondary,
                                uncheckedBorderColor = Color.Transparent,
                                uncheckedIconColor = Color.Unspecified,

                                disabledCheckedThumbColor = Color.White.copy(0.7f),
                                disabledCheckedBorderColor = Color.Transparent,
                                disabledCheckedTrackColor = SwitchPrimary.copy(0.7f),
                                disabledCheckedIconColor = Color.Unspecified,
                                disabledUncheckedThumbColor = Color.White.copy(0.7f),
                                disabledUncheckedTrackColor = SwitchSecondary.copy(0.7f),
                                disabledUncheckedBorderColor = Color.Transparent,
                                disabledUncheckedIconColor = Color.Unspecified,
                            )
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun SettingsNotificationScreenPreview() {
    AppTheme {
        SettingsNotificationContent(
            uiState = NotificationState()
        )
    }
}
