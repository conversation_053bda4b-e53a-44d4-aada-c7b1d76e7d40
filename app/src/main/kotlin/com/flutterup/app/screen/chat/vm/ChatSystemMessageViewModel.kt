package com.flutterup.app.screen.chat.vm

import com.flutterup.app.screen.chat.state.ChatSystemMessageUiState
import com.flutterup.app.utils.GlobalSettingsMonitor
import com.flutterup.app.utils.chat.core.ChatMessageMonitor
import com.flutterup.app.utils.chat.core.ChatOnReceivedMessageListener
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.imlib.model.ReceivedProfile
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ChatSystemMessageViewModel @Inject constructor(
    private val chatMessageMonitor: ChatMessageMonitor,
    private val settingsMonitor: GlobalSettingsMonitor,
    private val chatConversationRepository: ChatConversationRepository,
    private val chatMessageRepository: ChatMessageRepository,
) : BaseViewModel(), ChatOnReceivedMessageListener {

    private val _uiState = MutableStateFlow(ChatSystemMessageUiState())
    val uiState: StateFlow<ChatSystemMessageUiState> = _uiState.asStateFlow()

    init {
        chatMessageMonitor.addMessageListener(this)
        refresh()
    }

    fun refresh() {
        scope.launch {
            _uiState.update { it.copy(isRefreshing = true) }
            val messages = getMessages()
            _uiState.update { it.copy(messages = messages.orEmpty()) }
        }.invokeOnCompletion {
            _uiState.update { it.copy(isRefreshing = false) }
            markAllAsRead()
        }
    }
    fun refreshSilence() {
        scope.launch {
            val messages = getMessages()
            _uiState.update { it.copy(messages = messages.orEmpty()) }
        }.invokeOnCompletion {
            markAllAsRead()
        }
    }

    fun loadMore() {
        scope.launch {
            _uiState.update { it.copy(isLoadingMore = true) }
            val messages = getMessages(lastMessageOperationTime)
            _uiState.update { it.copy(messages = it.messages + messages.orEmpty()) }
        }.invokeOnCompletion {
            _uiState.update { it.copy(isLoadingMore = false) }
        }
    }

    suspend fun getConversation(): Conversation? {
        val systemAccount = settingsMonitor.noticeAccount.value ?: settingsMonitor.noticeAccount.first { it != null }
        if (systemAccount == null) return null

        val conversation = chatConversationRepository
            .getConversation(systemAccount, Conversation.ConversationType.SYSTEM)
            ?: Conversation.obtain(Conversation.ConversationType.SYSTEM, systemAccount, "")

        _uiState.update { it.copy(conversation = conversation) }
        return conversation
    }

    suspend fun getMessages(datetime: Long = 0L): List<Message>? {
        val conversation = _uiState.value.conversation ?: getConversation() ?: return null
        return chatMessageRepository.getMessages(conversation, datetime)
    }

    override fun onReceivedMessage(message: Message?, profile: ReceivedProfile?) {
        if (message == null) return

        if (isSystemMessage(message)) {
            refreshSilence()
        }
    }

    override fun onCleared() {
        chatMessageMonitor.removeMessageListener(this)
        super.onCleared()
    }

    private val lastMessageOperationTime: Long
        get() = _uiState.value.messages.lastOrNull()?.sentTime ?: 0L

    private fun isSystemMessage(message: Message): Boolean {
        return message.conversationType == Conversation.ConversationType.SYSTEM
            && message.senderUserId == settingsMonitor.noticeAccount.value
    }

    private fun markAllAsRead() {
        scope.launch {
            val conversation = _uiState.value.conversation ?: return@launch
            chatMessageRepository.markAllMessagesAsRead(conversation)
        }
    }
}