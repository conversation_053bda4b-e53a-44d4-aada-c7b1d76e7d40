package com.flutterup.app.screen.relate.vm

import com.flutterup.app.screen.relate.state.RelateHomeState
import com.flutterup.app.screen.relate.state.Visitors
import com.flutterup.app.screen.relate.state.WinksReceived
import com.flutterup.app.screen.relate.state.WinksSent
import com.flutterup.app.utils.UserUnreadMonitor
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import javax.inject.Inject


@HiltViewModel
class RelateHomeViewModel @Inject constructor(
    unreadMonitor: UserUnreadMonitor,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(RelateHomeState())

    val uiState: StateFlow<RelateHomeState> = combine(
        _uiState,
        unreadMonitor.unreadCount,
    ) { ui, unread ->
        ui.copy(
            titles = listOf(
                WinksReceived(unread.winksNewNum),
                WinksSent.EMPTY,
                Visitors(unread.visitorNewNum)
            )
        )
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value
    )

    fun updateCurrentPage(index: Int) {
        _uiState.update { it.copy(initialPage = index) }
    }
}