package com.flutterup.app.screen.common.vm

import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.ReportItem
import com.flutterup.app.screen.common.state.ReportUiState
import com.flutterup.app.utils.GlobalSettingsMonitor
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ReportViewModel @Inject constructor(
    private val settingsMonitor: GlobalSettingsMonitor,
    private val repository: ReportRepository,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(ReportUiState())

    val uiState = combine(
        _uiState,
        settingsMonitor.reportList
    ) { ui, reportList ->
        ui.copy(reportList = reportList.orEmpty())
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value
    )
    fun report(
        targetUserId: String,
        from: AppFrom,
        reportItem: ReportItem,
        onSuccess: () -> Unit = {},
    ) {
        scope.launch {
            val isSuccess = repository.reportUser(targetUserId, reportItem, from)
            if (isSuccess) {
                _uiState.update { it.copy(reported = true) }
                onSuccess()
            }
        }
    }
}