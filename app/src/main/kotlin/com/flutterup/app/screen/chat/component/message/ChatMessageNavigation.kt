package com.flutterup.app.screen.chat.component.message

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.AppDefaultNavigationIcon
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppAvatar
import com.flutterup.app.design.component.AppNearbyBorderStyle
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.TextBlack
import com.flutterup.app.model.UserInfo
import com.flutterup.app.screen.chat.state.ChatMessageUiState

@Composable
internal fun ChatMessageNavigation(
    uiState: ChatMessageUiState,
    onBackClick: () -> Unit = {},
    onProfileClick: () -> Unit = {},
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onBackClick) {
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.ic_arrow_back),
                contentDescription = null,
                tint = Color.Black
            )
        }

        AppAvatar(
            online = uiState.otherUserinfo?.online == 1,
            modifier = Modifier.noRippleClickable(onClick = onProfileClick)
        ) {
            AsyncImage(
                model = uiState.otherUserinfo?.headImage,
                contentDescription = null,
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .border(1.dp, Color.White, CircleShape)
                    .padding(1.dp)
                    .size(32.dp)
                    .clip(CircleShape)
            )
        }

        Spacer(Modifier.width(4.dp))

        Column(
            verticalArrangement = Arrangement.Center
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(7.dp)
            ) {
                Text(
                    text = uiState.otherUserinfo?.nickname.orEmpty(),
                    style = TextStyle(
                        fontSize = 12.sp,
                        lineHeight = 16.sp,
                        fontWeight = FontWeight(900),
                        color = TextBlack,
                    )
                )
                
                uiState.otherUserinfo?.age?.let { age ->
                    Text(
                        text = age.toString(),
                        style = TextStyle(
                            fontSize = 12.sp,
                            lineHeight = 16.sp,
                            fontWeight = FontWeight(900),
                            color = TextBlack,
                        )
                    )
                }
            }

            if (uiState.otherUserinfo?.location == 1) {
                AppNearbyBorderStyle(
                    text = stringResource(R.string.around)
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview
@Composable
private fun ChatMessageNavigationPreview() {
    val uiState = ChatMessageUiState(
        otherUserinfo = UserInfo(
            userId = "1",
            headImage = "",
            nickname = "Wendy",
            age = 18,
            online = 1,
            location = 1,
        )
    )

    AppScaffold(
        title = { },
        navigation = { ChatMessageNavigation(uiState) },
        rightNavigationContent = { },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) {

    }
}
