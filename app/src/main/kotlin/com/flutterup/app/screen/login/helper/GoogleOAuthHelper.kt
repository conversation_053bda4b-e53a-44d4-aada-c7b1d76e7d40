package com.flutterup.app.screen.login.helper

import java.net.URLEncoder
import java.security.SecureRandom
import java.util.Base64
import androidx.core.net.toUri
import java.security.MessageDigest

/**
 * Google OAuth WebView 登录帮助类
 */
object GoogleOAuthHelper {
    
    private const val GOOGLE_AUTH_URL = "https://accounts.google.com/o/oauth2/v2/auth"
    private const val REDIRECT_URI = "com.flutterup.app://oauth/callback"
    
    // OAuth 2.0 scopes
    private const val SCOPES = "openid email profile"
    
    /**
     * 构建 Google OAuth 授权 URL
     */
    fun buildAuthUrl(clientId: String): String {
        val state = generateRandomState()
        val codeVerifier = generateCodeVerifier()
        val codeChallenge = generateCodeChallenge(codeVerifier)
        
        val params = mapOf(
            "client_id" to clientId,
            "redirect_uri" to REDIRECT_URI,
            "response_type" to "code",
            "scope" to SCOPES,
            "state" to state,
            "code_challenge" to code<PERSON>hallenge,
            "code_challenge_method" to "S256",
            "access_type" to "offline",
            "prompt" to "select_account"
        )
        
        val queryString = params.entries.joinToString("&") { (key, value) ->
            "$key=${URLEncoder.encode(value, "UTF-8")}"
        }
        
//        return "$GOOGLE_AUTH_URL?$queryString"
        return "https://credman-web-test.glitch.me/"
    }
    
    /**
     * 检查 URL 是否为回调 URL
     */
    fun isCallbackUrl(url: String): Boolean {
        return url.startsWith(REDIRECT_URI)
    }
    
    /**
     * 从回调 URL 中提取授权码
     */
    fun extractAuthorizationCode(url: String): String? {
        return try {
            val uri = url.toUri()
            uri.getQueryParameter("code")
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 从回调 URL 中提取错误信息
     */
    fun extractError(url: String): String? {
        return try {
            val uri = url.toUri()
            uri.getQueryParameter("error")
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 生成随机状态字符串
     */
    private fun generateRandomState(): String {
        val bytes = ByteArray(32)
        SecureRandom().nextBytes(bytes)
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes)
    }
    
    /**
     * 生成 PKCE code verifier
     */
    private fun generateCodeVerifier(): String {
        val bytes = ByteArray(32)
        SecureRandom().nextBytes(bytes)
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes)
    }
    
    /**
     * 生成 PKCE code challenge
     */
    private fun generateCodeChallenge(codeVerifier: String): String {
        val bytes = codeVerifier.toByteArray(Charsets.US_ASCII)
        val digest = MessageDigest.getInstance("SHA-256")
        val hash = digest.digest(bytes)
        return Base64.getUrlEncoder().withoutPadding().encodeToString(hash)
    }
}

/**
 * Google 登录结果
 */
sealed class GoogleSignInResult {
    data class Success(val authorizationCode: String) : GoogleSignInResult()
    data class Error(val error: String) : GoogleSignInResult()
    object Cancelled : GoogleSignInResult()
}
