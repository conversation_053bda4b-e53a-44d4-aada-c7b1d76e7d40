package com.flutterup.app.screen.discover.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.flutterup.app.design.theme.ShimmerOnPrimary
import com.flutterup.app.screen.discover.state.DiscoverState
import com.valentinilk.shimmer.Shimmer
import com.valentinilk.shimmer.shimmer

@Composable
fun ShimmerContent(
    uiState: DiscoverState,
    shimmer: Shimmer,
    modifier: Modifier = Modifier,
    shimmerContent: @Composable () -> Unit = {
        Box(
            modifier = modifier
                .shimmer(shimmer)
                .background(ShimmerOnPrimary),
        )
    },
    content: @Composable () -> Unit = {}
) {
    if (uiState.isAllLoaded) {
        shimmerContent()
    } else {
        content()
    }
}