package com.flutterup.app.screen.login.vm

import com.flutterup.app.navigation.GlobalNavCenter
import com.flutterup.app.screen.login.state.LoginEmailUIState
import com.flutterup.base.BaseRepositoryViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class LoginEmailViewModel @Inject constructor(
    private val repository: LoginRepository,
    private val navCenter: GlobalNavCenter,
) : BaseRepositoryViewModel(repository) {
    private val _uiState = MutableStateFlow(LoginEmailUIState())
    val uiState: StateFlow<LoginEmailUIState> = combine(
        _uiState,
        loadingState
    ) { ui, loading ->
        ui.copy(isLoading = loading)
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value.copy(isLoading = loadingState.value)
    )

    fun updateEmail(email: String) = _uiState.update { it.copy(email = email) }

    fun updatePassword(password: String) = _uiState.update { it.copy(password = password) }

    fun togglePasswordVisibility() = _uiState.update { it.copy(isPasswordVisible = !it.isPasswordVisible) }


    fun login() {
        val email = uiState.value.email
        val password = uiState.value.password

        if (email.isEmpty() || password.isEmpty()) return

        scope.launchWithLoading {
            val result = repository.login(email, password)
            if (result.isSuccess) {
                navCenter.navigateHome()
            }
        }
    }
}