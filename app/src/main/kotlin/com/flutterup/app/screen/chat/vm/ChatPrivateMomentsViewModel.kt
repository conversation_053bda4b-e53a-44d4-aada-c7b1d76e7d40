package com.flutterup.app.screen.chat.vm

import android.net.Uri
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.screen.chat.state.ChatPrivateMomentsUiState
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ChatPrivateMomentsViewModel @Inject constructor(
    private val repository: ChatPrivateMomentsRepository,
) : BaseViewModel() {


    private val _uiState = MutableStateFlow(ChatPrivateMomentsUiState())

    val uiState: StateFlow<ChatPrivateMomentsUiState> = _uiState.asStateFlow()


    init {
        refreshSilence()
    }

    fun updateSelectedMedia(newSelectedValue: Boolean, item: MediaItemEntity) {
        if (newSelectedValue) {
            _uiState.update { it.copy(selectedMediaList = it.selectedMediaList + item) }
        } else {
            _uiState.update {
                it.copy(
                    selectedMediaList = it.selectedMediaList.filterNot { media ->
                        media.id == item.id
                    }
                )
            }
        }
    }

    fun add(uri: Uri) {
        scope.launchWithLoading(
            onLoadingChange = { isLoading -> _uiState.update { it.copy(isUploading = isLoading) } },
            block = {
                val addResult = repository.addPrivateMoment(uri)
                if (addResult != null) { //添加成功
                    _uiState.update { it.copy(mediaList = it.mediaList + addResult) }
                }
            }
        )
    }

    fun delete() {
        scope.launchWithLoading(
            onLoadingChange = { isLoading -> _uiState.update { it.copy(isDeleting = isLoading) } },
            block = {
                val deleteResult = repository.deletePrivateMoments(_uiState.value.selectedMediaList)
                if (deleteResult.isNotEmpty()) { //删除成功
                    val newMediaList = _uiState.value.mediaList.filterNot { media -> deleteResult.contains(media.id) }
                    val newSelectedMediaList = _uiState.value.selectedMediaList.filterNot { media -> deleteResult.contains(media.id) }

                    _uiState.update {
                        it.copy(
                            mediaList = newMediaList,
                            selectedMediaList = newSelectedMediaList,
                        )
                    }
                }
            }
        )
    }

    fun refresh() {
        scope.launchWithLoading(
            onLoadingChange = { isLoading -> _uiState.update { it.copy(isRefreshing = isLoading) } },
            block = {
                getPrivateMoments(true)
            }
        )
    }

    fun refreshSilence() {
        scope.launch {
            getPrivateMoments(true)
        }
    }

    fun loadMore() {
        scope.launchWithLoading(
            onLoadingChange = { isLoading -> _uiState.update { it.copy(isLoadingMore = isLoading) } },
            block = {
                getPrivateMoments(false)
            }
        )
    }

    private suspend fun getPrivateMoments(isRefresh: Boolean) {
        val mediaList = repository.getPrivateMoments(if (isRefresh) null else lastId)
        _uiState.update {
            if (isRefresh) {
                it.copy(mediaList = mediaList)
            } else {
                it.copy(mediaList = (it.mediaList + mediaList).distinctBy { media -> media.id })
            }
        }
    }

    private val lastId: Long?
        get() = _uiState.value.mediaList.lastOrNull()?.id
}