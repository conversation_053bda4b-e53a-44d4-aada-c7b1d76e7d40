package com.flutterup.app.screen.settings.vm

import com.flutterup.app.screen.settings.state.NotificationState
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseRepositoryViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import javax.inject.Inject


@HiltViewModel
class SettingsNotificationViewModel @Inject constructor(
    private val userMonitor: UserMonitor,
    private val accountRepository: AccountRepository
) : BaseRepositoryViewModel() {
    private val _uiState = MutableStateFlow(NotificationState(pushConfig = userMonitor.pushConfig))

    val uiState: StateFlow<NotificationState> = combine(
        _uiState,
        loadingState
    ) { ui, loading ->
        ui.copy(isLoading = loading)
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value.copy(isLoading = loadingState.value)
    )

    fun updateNotification(type: NotificationState.NotificationType, checked: Boolean) {
        val currentPushConfig = uiState.value.pushConfig ?: 0

        scope.launchWithLoading {
            val (isSuccess, newPushConfig) = accountRepository.updateNotificationSettings(currentPushConfig, type, checked)
            if (isSuccess) {
                userMonitor.updatePushConfig(newPushConfig)
                _uiState.update { it.copy(pushConfig = newPushConfig) }
            }
        }
    }
}