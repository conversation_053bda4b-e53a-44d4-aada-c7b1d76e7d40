@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.chat

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Badge
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.AppDefaultNavigationIcon
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppAvatar
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.LineSecondary
import com.flutterup.app.design.theme.TextBlack151
import com.flutterup.app.design.theme.TextBlack666
import com.flutterup.app.design.theme.TextGray6f7288
import com.flutterup.app.model.UserInfo
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.chat.state.ChatSystemConversationType
import com.flutterup.app.screen.chat.state.ChatSystemUiState
import com.flutterup.app.screen.chat.vm.ChatSystemConversationsViewModel
import com.flutterup.app.utils.chat.ChatMessageUtils
import com.flutterup.app.utils.chat.ChatUserCache
import com.flutterup.base.utils.DateUtils
import io.rong.imlib.model.Conversation

@Composable
fun ChatSystemConversationsScreen() {

    val navController = LocalNavController.current

    val viewModel = hiltViewModel<ChatSystemConversationsViewModel>()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    ChatSystemContent(
        uiState = uiState,
        onBackClick = navController::popBackStack,
        onConversationClick = { type, conversation ->
            when(type) {
                ChatSystemConversationType.SYSTEM -> navController.navigate(SystemMessageRoute)
                ChatSystemConversationType.CUSTOM_SERVICE -> navController.navigate(CustomerServiceRoute)
            }
        }
    )
}

@Composable
private fun ChatSystemContent(
    uiState: ChatSystemUiState,
    onBackClick: () -> Unit = {},
    onConversationClick: (ChatSystemConversationType, Conversation) -> Unit = { _, _ -> },
) {
    AppScaffold(
        title = { },
        navigation = {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                AppDefaultNavigationIcon(canGoBack = true, onBackClick = onBackClick)

                Text(
                    text = stringResource(R.string.system_message),
                    style = TextStyle(
                        fontSize = 17.sp,
                        lineHeight = 21.sp,
                        fontWeight = FontWeight.W800,
                        fontStyle = FontStyle.Italic,
                        color = Color.White,
                    )
                )
            }
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) {
        Image(
            painter = painterResource(R.mipmap.ic_common_top_bg),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier.fillMaxWidth()
        )

        LazyColumn (
            modifier = Modifier
                .padding(paddingValues = it)
                .fillMaxSize()
        ) {
            if (uiState.systemConversation != null) {
                item(uiState.systemConversation.targetId) {
                    ChatSystemItem(
                        conversation = uiState.systemConversation,
                        conversationType = ChatSystemConversationType.SYSTEM,
                        onConversationClick = onConversationClick
                    )
                }
            }

            if (uiState.customServiceConversation != null) {
                item {
                    ChatSystemItem(
                        conversation = uiState.customServiceConversation,
                        conversationType = ChatSystemConversationType.CUSTOM_SERVICE,
                        onConversationClick = onConversationClick
                    )
                }
            }
        }
    }
}

@Composable
private fun ChatSystemItem(
    conversation: Conversation,
    conversationType: ChatSystemConversationType,
    onConversationClick: (ChatSystemConversationType, Conversation) -> Unit = { _, _ -> },
) {
    var userinfo by remember { mutableStateOf<UserInfo?>(null) }

    LaunchedEffect(conversation.targetId) {
        userinfo = ChatUserCache.getUserInfoOrFetch(conversation.targetId)
    }
    
    Box(
        modifier = Modifier
            .wrapContentSize()
            .noRippleClickable(onClick = {
                onConversationClick(conversationType, conversation)
            })
    ) {
        Row(
            modifier = Modifier
                .padding(horizontal = 20.dp)
                .fillMaxSize()
                .height(75.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            AppAvatar {
                AsyncImage(
                    model = userinfo?.headImage,
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .clip(CircleShape)
                        .size(50.dp)
                )
            }

            Column(
                modifier = Modifier.weight(1f).padding(horizontal = 12.dp),
                verticalArrangement = Arrangement.Center,
            ) {
                Text(
                    text = userinfo?.nickname.orEmpty(),
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = 16.sp,
                        fontWeight = FontWeight.W900,
                        color = TextBlack151,
                    ),
                    maxLines = 1,
                )

                Spacer(Modifier.height(8.dp))

                Text(
                    text = ChatMessageUtils.convert(conversation.latestMessage),
                    style = TextStyle(
                        fontSize = 12.sp,
                        lineHeight = 16.sp,
                        fontWeight = FontWeight.W400,
                        color = TextBlack666,
                    ),
                    maxLines = 1,
                )
            }

            Column(
                modifier = Modifier
                    .padding(top = 18.dp)
                    .fillMaxHeight(),
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = DateUtils.timestamp2Ago(conversation.operationTime),
                    style = TextStyle(
                        fontSize = 12.sp,
                        lineHeight = 16.sp,
                        fontWeight = FontWeight.W400,
                        color = TextGray6f7288,
                    )
                )

                Spacer(Modifier.height(9.dp))

                if (conversation.unreadMessageCount > 0) {
                    Badge(
                        containerColor = Color(0xFF988DE2),
                        contentColor = Color.White,
                    ) {
                        Text(
                            text = if (conversation.unreadMessageCount > 99) "99+" else conversation.unreadMessageCount.toString(),
                            style = TextStyle(
                                fontSize = 10.sp,
                                lineHeight = 16.sp,
                                fontWeight = FontWeight.W900,
                            )
                        )
                    }
                }
            }
        }

        HorizontalDivider(
            modifier = Modifier
                .padding(horizontal = 20.dp)
                .fillMaxWidth()
                .align(Alignment.BottomStart),
            thickness = 1.dp,
            color = LineSecondary
        )
    }
}

@Preview
@Composable
private fun ChatSystemScreenPreview() {
    AppTheme {
        ChatSystemContent(
            uiState = ChatSystemUiState(
                systemConversation = Conversation.obtain(Conversation.ConversationType.SYSTEM, "1", "111"),
                customServiceConversation = Conversation.obtain(Conversation.ConversationType.PRIVATE, "2", "222"),
            )
        )
    }
}