package com.flutterup.app.screen.login.content

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldColors
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.PurpleTextFieldColors

@Composable
fun LoginEmailPwdTextField(
    value: String,
    keyboardType: KeyboardType,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    colors: TextFieldColors = PurpleTextFieldColors,
    isError: Boolean = false,
    isSecurity: Boolean = false,
    shape: Shape = RoundedCornerShape(8.dp),
    @StringRes placeholder: Int? = null,
    @StringRes supportingText: Int? = null,
    @DrawableRes leadingIcon: Int? = null,
    @DrawableRes trailingIcon: Int? = null,
    onLeadingIconClick: () -> Unit = {},
    onTrailingIconClick: () -> Unit = {},
) {
    val passwordVisualTransformation = remember { PasswordVisualTransformation() }

    TextField(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier,
        isError = isError,
        placeholder = if (placeholder != null) {
            {
                Text(
                    text = stringResource(placeholder),
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = 18.sp,
                        fontWeight = FontWeight.W400,
                    )
                )
            }
        } else null,
        supportingText = if (isError && supportingText != null) {
            {
                Text(
                    text = stringResource(supportingText),
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = 18.sp,
                        fontWeight = FontWeight.W400,
                    )
                )
            }
        } else null,
        leadingIcon = if (leadingIcon != null) {
            {
                Icon(
                    imageVector = ImageVector.vectorResource(id = leadingIcon),
                    contentDescription = null,
                    modifier = Modifier
                        .size(20.dp)
                        .noRippleClickable(onClick = onLeadingIconClick)
                )
            }
        } else null,
        trailingIcon = if (trailingIcon != null) {
            {
                Image(
                    painter = painterResource(id = trailingIcon),
                    contentDescription = null,
                    modifier = Modifier
                        .size(20.dp)
                        .noRippleClickable(onClick = onTrailingIconClick)
                )
            }
        } else null,
        visualTransformation = if (isSecurity) passwordVisualTransformation else VisualTransformation.None,
        keyboardOptions = KeyboardOptions(
            keyboardType = keyboardType,
            imeAction = if (keyboardType == KeyboardType.Password) ImeAction.Done else ImeAction.Next
        ),
        colors = colors,
        singleLine = true,
        shape = shape,
    )
}