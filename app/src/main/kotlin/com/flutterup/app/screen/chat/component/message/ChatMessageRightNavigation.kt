package com.flutterup.app.screen.chat.component.message

import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.flutterup.app.R
import com.flutterup.app.design.modifiers.noRippleClickable


@Preview
@Composable
internal fun ChatMessageRightNavigation(
    onClick: () -> Unit = {},
) {
    Icon(
        painter = painterResource(R.drawable.ic_chat_message_settings),
        contentDescription = null,
        tint = Color.Unspecified,
        modifier = Modifier.size(28.dp).noRippleClickable(onClick = onClick)
    )
}