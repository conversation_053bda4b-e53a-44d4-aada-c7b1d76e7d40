package com.flutterup.app.screen.chat.vm

import android.content.Context
import android.net.Uri
import com.flutterup.app.model.ExchangePacksEntity
import com.flutterup.app.model.ExchangePacksRequest
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.model.MediaMessageEntity
import com.flutterup.app.model.MultiMediaMessageEntity
import com.flutterup.app.network.ApiService
import com.flutterup.app.utils.chat.core.ChatUnreadMonitor
import com.flutterup.app.utils.chat.message.CHAT_SEND_ERROR_KEY
import com.flutterup.base.BaseRepository
import com.flutterup.base.utils.JsonUtils
import com.flutterup.base.utils.MimeType
import com.flutterup.base.utils.UriUtils
import com.flutterup.base.utils.resumeIfActive
import com.flutterup.base.utils.resumeWithExceptionIfActive
import com.flutterup.chat.core.ChatException
import com.flutterup.chat.message.content.GiftMessageContent
import com.flutterup.chat.message.content.MultiPrivateMessageContent
import com.flutterup.chat.message.content.PrivateMessageContent
import com.flutterup.gifts.entity.GiftEntity
import com.flutterup.gifts.entity.GiftResourceInfo
import dagger.hilt.android.qualifiers.ApplicationContext
import io.rong.common.FileUtils
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.RongIMClient.OperationCallback
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.HistoryMessageOption
import io.rong.imlib.model.HistoryMessageOption.PullOrder
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent
import io.rong.message.ImageMessage
import io.rong.message.SightMessage
import io.rong.message.TextMessage
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject

class ChatMessageRepository @Inject constructor(
    @ApplicationContext private val context: Context,
    private val apiService: ApiService,
    private val chatUnreadMonitor: ChatUnreadMonitor,
    private val jsonUtils: JsonUtils,
) : BaseRepository() {

    suspend fun openPrivateMessage(message: Message?): Boolean {
        if (message == null) return false

        val toMsg = message.uId
        val fromMsg: String?
        val id: Long?

        when(val content = message.content) {
            is PrivateMessageContent -> {
                val entity = content.get(MediaMessageEntity::class.java)
                fromMsg = entity?.messageId
                id = entity?.id
            }
            is MultiPrivateMessageContent -> {
                val entity = content.get(MultiMediaMessageEntity::class.java)
                fromMsg = entity?.messageId
                id = null
            }
            else -> {
                fromMsg = null
                id = null
            }
        }

        if (fromMsg == null) return false

        val result = apiService.openPrivateMessage(fromMsg, toMsg, id)
        return result.isSuccess
    }

    /**
     *
     * @param order 拉取顺序。
     * - DESCEND：降序，按消息发送时间递减的顺序，获取发送时间早于 dataTime 的消息，返回的列表中的消息按发送时间从新到旧排列。
     * - ASCEND： 升序，按消息发送时间递增的顺序，获取发送时间晚于 dataTime 的消息，返回的列表中的消息按发送时间从旧到新排列。
     */
    suspend fun getMessages(
        conversation: Conversation,
        datetime: Long,
        count: Int = 20,
        order: PullOrder = PullOrder.DESCEND,
    ): List<Message> {
        val options = HistoryMessageOption(
            datetime,
            count,
            order
        )

        return suspendCancellableCoroutine { cont ->
            RongCoreClient.getInstance().getMessages(
                conversation.conversationType,
                conversation.targetId,
                options
            ) { messageList, errorCode ->
                messageList?.let {
                    cont.resumeIfActive(messageList)
                }

                errorCode?.let {
                    cont.resumeWithExceptionIfActive(ChatException(errorCode))
                }
            }
        }
    }

    suspend fun markAllMessagesAsRead(conversation: Conversation): Boolean {
        val currentTimestamp = System.currentTimeMillis()

        return suspendCancellableCoroutine { continuation ->
            RongIMClient.getInstance().clearMessagesUnreadStatus(
                conversation.conversationType,
                conversation.targetId,
                currentTimestamp,
                object : OperationCallback() {
                    override fun onSuccess() {
                        continuation.resumeIfActive(true)

                        chatUnreadMonitor.refresh()
                    }

                    override fun onError(errorCode: RongIMClient.ErrorCode?) {
                        continuation.resumeIfActive(false)
                    }
                }
            )
        }
    }

    fun sendMessage(message: Message): Flow<Message?> = callbackFlow {
        RongCoreClient.getInstance().sendMessage(
            message,
            null,
            null,
            object : IRongCoreCallback.ISendMessageCallback {
                override fun onAttached(message: Message?) {
                    channel.trySend(message)
                }

                override fun onSuccess(message: Message?) {
                    channel.trySend(message)
                    channel.close() // 关闭Channel，消息发送完成
                }

                override fun onError(
                    message: Message?,
                    coreErrorCode: IRongCoreEnum.CoreErrorCode?
                ) {
                    writeSendError(message, coreErrorCode)
                    channel.trySend(message)
                    channel.close() // 关闭Channel，消息发送完成
                }
            })

        awaitClose {  } //ignore this
    }

    fun sendMediaMessage(message: Message): Flow<Message?> = callbackFlow {
        RongCoreClient.getInstance().sendMediaMessage(
            message,
            null,
            null,
            object : IRongCoreCallback.ISendMediaMessageCallback {
                override fun onProgress(
                    message: Message?,
                    progress: Int
                ) {
                    channel.trySend(message)
                }

                override fun onAttached(message: Message?) {
                    channel.trySend(message)
                }

                override fun onSuccess(message: Message?) {
                    channel.trySend(message)
                    channel.close() // 关闭Channel，消息发送完成
                }

                override fun onError(
                    message: Message?,
                    coreErrorCode: IRongCoreEnum.CoreErrorCode?
                ) {
                    writeSendError(message, coreErrorCode)
                    channel.trySend(message)
                    channel.close() // 关闭Channel，消息发送完成
                }

                override fun onCanceled(message: Message?) {
                    channel.trySend(message)
                    channel.close() // 关闭Channel，消息发送取消
                }
            }
        )

        awaitClose {  } //ignore this
    }

    suspend fun obtainTextMessage(conversation: Conversation, text: String): Message {
        val textMessage = TextMessage.obtain(text)
        val message = obtainMessage(conversation, textMessage)
        return message
    }

    suspend fun obtainMediaMessage(conversation: Conversation, uri: Uri): Message? {
        val mimeType = UriUtils.getMimeType(context, uri)

        return when(mimeType) {
            MimeType.IMAGE -> obtainImageMessage(conversation, uri)
            MimeType.VIDEO -> obtainVideoMessage(conversation, uri)
            else -> null
        }
    }

    suspend fun obtainPrivateMessage(conversation: Conversation, mediaList: List<MediaItemEntity>): Message? {
        if (mediaList.isEmpty()) return null

        val privateMessage = if (mediaList.size == 1) {
            val item = MediaMessageEntity(mediaList.first())
            val json = jsonUtils.toJson(item, MediaMessageEntity::class.java)
            PrivateMessageContent.obtain(json)
        } else {
            val multi = MultiMediaMessageEntity(mediaList)
            val json = jsonUtils.toJson(multi, MultiMediaMessageEntity::class.java)
            MultiPrivateMessageContent.obtain(json)
        }
        return obtainMessage(conversation, privateMessage)
    }

    suspend fun obtainGiftMessage(conversation: Conversation, gift: GiftEntity, exchangeId: String): Message {
        val giftResource = gift.toGiftMessage(exchangeId)
        val json = jsonUtils.toJson(giftResource, GiftResourceInfo::class.java)
        val giftMessage = GiftMessageContent.obtain(json)
        return obtainMessage(conversation, giftMessage)
    }

    suspend fun exchangeGift(giftEntity: GiftEntity): String? {
        val request = ExchangePacksRequest(
            id = giftEntity.giftId,
            num = 1,
            price = giftEntity.price ?: 0
        )
        val result = apiService.exchangePacks(request)
        return result.data?.id
    }

    private suspend fun obtainImageMessage(conversation: Conversation, uri: Uri, isFull: Boolean = true): Message {
        val imageMessage = ImageMessage.obtain(uri, isFull)
        val message = obtainMessage(conversation, imageMessage)
        return message
    }

    private suspend fun obtainVideoMessage(conversation: Conversation, uri: Uri): Message? {
        val (_, _, duration) = UriUtils.getUriMediaInfo(context, MimeType.VIDEO, uri)
        val durationSeconds = (duration / 1000).toInt()

        val videoMessage = when {
            FileUtils.uriStartWithFile(uri) -> SightMessage.obtain(uri, durationSeconds)
            FileUtils.uriStartWithContent(uri) -> {
                val tempFile = UriUtils.uriToFile(context, MimeType.VIDEO, uri) ?: return null
                val fileUri = Uri.fromFile(tempFile)
                SightMessage.obtain(fileUri, durationSeconds)
            }
            else -> null
        } ?: return null

        return obtainMessage(conversation, videoMessage)
    }

    private fun obtainMessage(conversation: Conversation, content: MessageContent): Message {
        val message = Message.obtain(conversation.targetId, conversation.conversationType, content)
        message.isCanIncludeExpansion = true
        message.sentStatus = Message.SentStatus.SENDING
        return message
    }

    private fun writeSendError(message: Message?, error: IRongCoreEnum.CoreErrorCode?) {
        if (message == null || error == null) return

        val expansion: HashMap<String, String> = if (message.expansion != null) {
            HashMap(message.expansion)
        } else {
            hashMapOf()
        }
        expansion[CHAT_SEND_ERROR_KEY] = error.code.toString()
        message.setExpansion(expansion)
    }

    private fun GiftEntity.toGiftMessage(exchangeId: String): GiftResourceInfo {
        return GiftResourceInfo(
            giftId = this.giftId,
            name = this.name,
            price = this.price,
            image = this.imageUrl,
            gif = this.gifUrl,
            video = this.videoUrl,
            exchangeId = exchangeId
        )
    }
}