package com.flutterup.app.screen.login.vm

import com.flutterup.app.model.Gender
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.model.Tag
import com.flutterup.app.model.UpdateProfileRequest
import com.flutterup.app.network.ApiService
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseRepository
import com.flutterup.base.utils.JsonUtils
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.zip
import java.time.LocalDate
import javax.inject.Inject

class LoginProfileRepository @Inject constructor(
    private val apiService: ApiService,
    private val userMonitor: UserMonitor,
    private val jsonUtils: JsonUtils,
) : BaseRepository() {

    suspend fun updateBaseProfile(
        nickname: String,
        gender: Gender,
        meet: Gender,
        birthday: LocalDate,
    ): Boolean {
        val request = UpdateProfileRequest(
            step = UpdateProfileRequest.STEP_ONE,
            nickName = nickname,
            sex = gender.value,
            sexuality = meet.value,
            birthday = birthday.toString()
        )

        val result = apiService.updateProfileInfo(request)
        if (result.isSuccess) {
            userMonitor.updateUserInfo(result.data?.userInfo)
        }
        return result.isSuccess
    }

    suspend fun updateOptionalProfile(
        mediaList: List<String>?,
        sign: String?,
        interests: List<Tag>?,
    ): Boolean {
        val mediaList = mediaList?.map {
            MediaItemEntity(type = MediaItemEntity.TYPE_IMAGE, url = it)
        }

        val request = UpdateProfileRequest(
            step = UpdateProfileRequest.STEP_TWO,
            mediaList = if (mediaList.isNullOrEmpty()) null else jsonUtils.toJsonList(mediaList, MediaItemEntity::class.java),
            sign = sign,
            tag = interests?.map { it.id }?.joinToString(",")
        )

        val result = apiService.updateProfileInfo(request)
        if (result.isSuccess) {
            userMonitor.updateUserInfo(result.data?.userInfo)
        }
        return result.isSuccess
    }
}