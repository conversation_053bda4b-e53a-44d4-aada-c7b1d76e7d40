package com.flutterup.app.screen.common.vm

import com.flutterup.app.network.ApiService
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseRepository
import javax.inject.Inject

class ProfileSharedRepository @Inject constructor(
    private val apiService: ApiService,
    private val userMonitor: UserMonitor
) : BaseRepository() {

    suspend fun updateMineUserinfo() {
        if (!userMonitor.isLogin) return

        val result = apiService.getMineProfileInfo()

        if (result.isSuccess) {
            userMonitor.updateUserInfo(result.data)
        }
    }

    suspend fun updateMineRights() {
        if (!userMonitor.isLogin) return

        val result = apiService.getMineProfileInfo()
        if (result.isSuccess) {
            result.data?.right?.let {
                userMonitor.updateUserRight(it)
            }
        }
    }
}