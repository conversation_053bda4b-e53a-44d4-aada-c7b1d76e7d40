@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.chat

import androidx.compose.animation.AnimatedContentScope
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.PurpleGradientPrimary
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.chat.component.ChatShortcutGreetingInput
import com.flutterup.app.screen.chat.state.MatchedUiState
import com.flutterup.app.screen.chat.vm.MatchedViewModel

@Composable
fun ReceivePingChatScreen(targetId: String) {
    val navController = LocalNavController.current

    //和match公用
    val viewModel: MatchedViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(targetId) {
        viewModel.init(targetId)
    }

    ReceivePingChatContent(
        uiState = uiState,
        onBackClick = navController::popBackStack,
        onSendClick = {
            viewModel.sendGreeting(it) {
                navController.popBackStack()
            }
        }
    )
}

@Composable
private fun ReceivePingChatContent(
    uiState: MatchedUiState,
    onBackClick: () -> Unit = {},
    onSendClick: (String) -> Unit = {},
) {

    AppScaffold(
        title = { },
        navigation = { },
        rightNavigationContent = {
            Icon(
                painter = painterResource(R.drawable.ic_white_close),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier
                    .size(50.dp)
                    .noRippleClickable(onClick = onBackClick)
                    .padding(15.dp)
            )
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) {

        // 原有背景图片（可选择保留或移除）
        Image(
            painter = painterResource(R.mipmap.ic_fullscreen_bg),
            contentDescription = null,
            contentScale = ContentScale.FillBounds,
            modifier = Modifier.fillMaxSize()
        )

        Box(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
        ) {
            Column(
                modifier = Modifier
                    .padding(top = 64.dp)
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Row(
                        modifier = Modifier.wrapContentSize(unbounded = false)
                    ) {
                        Box(
                            modifier = Modifier
                                .rotate(-15f)
                                .offset(y = (-40).dp)
                                .clip(RoundedCornerShape(10.dp))
                                .size(width = 140.dp, height = 195.dp)
                        ) {
                            AsyncImage(
                                model = uiState.userinfo?.headImage,
                                contentDescription = null,
                                contentScale = ContentScale.FillBounds,
                                modifier = Modifier.matchParentSize()
                            )
                        }

                        Box(
                            modifier = Modifier
                                .rotate(8f)
                                .clip(RoundedCornerShape(10.dp))
                                .size(width = 140.dp, height = 195.dp)
                        ) {
                            AsyncImage(
                                model = uiState.anotherUserinfo?.headImage,
                                contentDescription = null,
                                contentScale = ContentScale.FillBounds,
                                modifier = Modifier.matchParentSize()
                            )

                            if (uiState.anotherUserinfo?.location == 1) {
                                Box(
                                    modifier = Modifier
                                        .padding(top = 8.dp, end = 8.dp)
                                        .size(16.4.dp)
                                        .background(Color(0x80232323), CircleShape)
                                        .align(Alignment.TopEnd),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Icon(
                                        painter = painterResource(R.drawable.ic_visitor_nearby),
                                        contentDescription = null,
                                        tint = Color.White,
                                    )
                                }
                            }

                            Box(
                                modifier = Modifier
                                    .background(
                                        brush = Brush.verticalGradient(
                                            colors = listOf(
                                                PurpleGradientPrimary.copy(0f),
                                                PurpleGradientPrimary.copy(0.6f),
                                            )
                                        )
                                    )
                                    .padding(start = 8.dp, bottom = 8.dp)
                                    .fillMaxWidth()
                                    .wrapContentHeight()
                                    .align(Alignment.BottomStart),

                                ) {
                                Column(
                                    modifier = Modifier
                                        .align(Alignment.BottomStart)
                                        .padding(start = 8.dp, bottom = 8.dp)
                                        .fillMaxWidth()
                                        .wrapContentHeight()
                                ) {
                                    Row(verticalAlignment = Alignment.CenterVertically) {
                                        Text(
                                            text = "${uiState.anotherUserinfo?.nickname.orEmpty()} ${uiState.anotherUserinfo?.age.toString()}",
                                            style = TextStyle(
                                                fontSize = 14.65.sp,
                                                lineHeight = 12.21.sp,
                                                fontWeight = FontWeight.W900,
                                                color = Color.White,
                                            )
                                        )

                                        if (uiState.anotherUserinfo?.online == 1) {
                                            Icon(
                                                painter = painterResource(R.drawable.ic_online_dot),
                                                contentDescription = null,
                                                tint = Color.Unspecified,
                                                modifier = Modifier.padding(start = 8.dp).size(6.5.dp)
                                            )
                                        }
                                    }

                                    uiState.anotherUserinfo?.tags?.let { interests ->
                                        FlowRow(
                                            horizontalArrangement = Arrangement.spacedBy(10.dp),
                                            verticalArrangement = Arrangement.spacedBy(10.dp),
                                            maxLines = 1, //只有单行
                                        ) {
                                            for (interest in interests) {
                                                Box(
                                                    modifier = Modifier
                                                        .padding(horizontal = 5.dp, vertical = 2.dp)
                                                        .background(
                                                            color = Color.White.copy(alpha = 0.2f),
                                                            shape = RoundedCornerShape(size = 8.dp)
                                                        )
                                                ) {
                                                    Text(
                                                        text = interest,
                                                        style = TextStyle(
                                                            fontSize = 12.sp,
                                                            lineHeight = 12.sp,
                                                            fontWeight = FontWeight.W400,
                                                            color = Color.White,
                                                        )
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            Column(
                modifier = Modifier.align(Alignment.BottomCenter),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                Box {
                    Text(
                        text = stringResource(R.string.receive_ping_chat_title),
                        style = TextStyle(
                            fontSize = 30.sp,
                            lineHeight = 50.sp,
                            fontWeight = FontWeight.W800,
                            fontStyle = FontStyle.Italic,
                            brush = Brush.linearGradient(
                                colors = listOf(
                                    Color.White,
                                    Color(0xFFFFE0FC),
                                    Color.White
                                )
                            ),
                            drawStyle = Stroke(with(LocalDensity.current) { 2.dp.toPx() })
                        )
                    )

                    Text(
                        text = stringResource(R.string.receive_ping_chat_title),
                        style = TextStyle(
                            fontSize = 30.sp,
                            lineHeight = 50.sp,
                            fontWeight = FontWeight.W800,
                            fontStyle = FontStyle.Italic,
                            brush = Brush.linearGradient(
                                colors = listOf(
                                    Color(0xFF7C4AB6),
                                    Color(0xFFB648B7)
                                )
                            ),
                        )
                    )
                }

                Text(
                    text = stringResource(R.string.receive_ping_chat_desc),
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = 18.sp,
                        fontWeight = FontWeight.W400,
                        color = Color(0xFF3A3D40),
                    )
                )

                Spacer(modifier = Modifier.height(40.dp))

                ChatShortcutGreetingInput(
                    greetingList = uiState.greetingList,
                    onSendClick = onSendClick,
                    isLoading = uiState.isLoading,
                    refreshText = stringResource(R.string.new_one),
                    modifier = Modifier
                        .padding(bottom = 100.dp)
                        .padding(horizontal = 16.dp)
                )
            }
        }
    }
}

@Preview
@Composable
private fun ReceivePingChatScreenPreview() {
    AppTheme {
        ReceivePingChatContent(uiState = MatchedUiState())
    }
}