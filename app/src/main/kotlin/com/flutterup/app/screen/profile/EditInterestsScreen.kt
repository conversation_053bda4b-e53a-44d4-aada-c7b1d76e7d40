@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.profile

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.BorderPrimary
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.design.theme.TextBlack333
import com.flutterup.app.design.theme.TextGray999
import com.flutterup.app.model.Tag
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.profile.state.ProfileInterestsUiState
import com.flutterup.app.screen.profile.vm.SharedProfileInterestsViewModel

@Composable
fun EditInterestsScreen(
    viewModel: SharedProfileInterestsViewModel
) {
    val navController = LocalNavController.current

    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    EditInterestsContent(
        uiState = uiState,
        onBackClick = {
            viewModel.removeAllCurrentTags()
            navController.popBackStack()
        },
        onClick = { isSelected, tag ->
            if (isSelected) {
                viewModel.removeTag(tag)
            } else {
                if (!uiState.canAddMore) return@EditInterestsContent
                viewModel.addTag(tag)
            }
        },
        onFinishClick = {
            viewModel.confirmTags()
            navController.popBackStack()
        }
    )
}

@Composable
private fun EditInterestsContent(
    uiState: ProfileInterestsUiState,
    onBackClick: () -> Unit = {},
    onClick: (isSelected: Boolean, tag: Tag) -> Unit = { _, _ -> },
    onFinishClick: () -> Unit = {},
) {
    val scrollState = rememberScrollState()

    AppScaffold(
        title = { },
        canGoBack = true, //不允许返回
        onBackClick = onBackClick,
        modifier = Modifier.fillMaxSize(),
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_common_top_bg),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.TopCenter)
            )

            Column(
                modifier = Modifier
                    .padding(horizontal = 20.dp)
                    .fillMaxHeight(),
                verticalArrangement = Arrangement.SpaceBetween
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .verticalScroll(scrollState)
                ) {
                    Spacer(modifier = Modifier.height(36.dp + it.calculateTopPadding()))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = stringResource(R.string.profile_interest_label),
                            style = TextStyle(
                                fontSize = 18.sp,
                                lineHeight = 22.sp,
                                fontWeight = FontWeight.W700,
                                color = TextBlack333,
                            )
                        )

                        Text(
                            text = uiState.limitText,
                            style = TextStyle(
                                fontSize = 14.sp,
                                lineHeight = 16.sp,
                                fontWeight = FontWeight.W700,
                                color = TextGray999,
                            )
                        )
                    }

                    Spacer(modifier = Modifier.height(25.dp))

                    if (uiState.interests.isEmpty()) { //空列表，展示loading
                        CircularProgressIndicator(
                            modifier = Modifier
                                .size(100.dp)
                                .align(Alignment.CenterHorizontally),
                            color = Color.Black,
                        )
                    } else {
                        FlowRow(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            val itemSelectedModifier = Modifier
                                .background(
                                    color = PurplePrimary,
                                    shape = RoundedCornerShape(size = 8.dp)
                                )
                            val itemUnselectedModifier = Modifier
                                .border(
                                    width = 1.dp,
                                    color = BorderPrimary,
                                    shape = RoundedCornerShape(size = 8.dp)
                                )

                            val textModifier = Modifier.padding(horizontal = 12.dp, vertical = 10.dp)

                            val textSelectedStyle = TextStyle(
                                fontSize = 14.sp,
                                lineHeight = 18.sp,
                                fontWeight = FontWeight.W400,
                                color = Color.White,
                            )

                            val textUnselectedStyle = TextStyle(
                                fontSize = 14.sp,
                                lineHeight = 18.sp,
                                fontWeight = FontWeight.W400,
                                color = TextGray999,
                            )

                            uiState.interests.forEach { tag ->
                                val currentTag = uiState.currentInterests.find { currentTag -> tag.id == currentTag.id }
                                val isSelected = currentTag != null

                                Box(
                                    modifier = Modifier
                                        .noRippleClickable(
                                            onClick = { onClick(isSelected, tag) }
                                        )
                                        .then(
                                            if (isSelected) itemSelectedModifier
                                            else itemUnselectedModifier
                                        )
                                ) {
                                    Text(
                                        text = tag.title.orEmpty(),
                                        style = if (isSelected) textSelectedStyle else textUnselectedStyle,
                                        modifier = textModifier
                                    )
                                }
                            }
                        }
                    }
                }


                Column {
                    AppContinueButton(
                        onClick = onFinishClick,
                        text = stringResource(R.string.finish),
                        enabled = uiState.currentInterests.isNotEmpty(),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                            .padding(horizontal = 4.dp)
                    )

                    Spacer(modifier = Modifier.height(45.dp + it.calculateBottomPadding()))
                }
            }
        }
    }
}


@Preview
@Composable
private fun EditInterestsScreenPreview() {
    AppTheme {
        EditInterestsContent(
            ProfileInterestsUiState(
                limit = 6,
                interests = listOf(
                    Tag(id = 1, title = "test1"),
                    Tag(id = 2, title = "test2"),
                    Tag(id = 3, title = "aklkamdksl"),
                    Tag(id = 4, title = "naskdnksdnakd"),
                    Tag(id = 5, title = "sdjnkdnaskdjn"),
                    Tag(id = 6, title = "jknadndasnk")
                ),
            )
        )
    }
}