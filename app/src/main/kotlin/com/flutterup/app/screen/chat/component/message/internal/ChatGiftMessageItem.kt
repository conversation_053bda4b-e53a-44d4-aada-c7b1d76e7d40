package com.flutterup.app.screen.chat.component.message.internal

import androidx.activity.ComponentActivity
import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.preview.BooleanProvider
import com.flutterup.app.design.theme.TextBlack333
import com.flutterup.app.design.theme.TextGray999
import com.flutterup.app.model.UserInfo
import com.flutterup.app.screen.chat.vm.ChatGiftViewModel
import com.flutterup.app.utils.chat.message.UnwrapStatus
import com.flutterup.app.utils.chat.message.unwrapStatus
import com.flutterup.chat.message.content.GiftMessageContent
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message


@Composable
fun ChatGiftMessageItem(
    mine: UserInfo?,
    other: UserInfo?,
    message: Message,
    content: GiftMessageContent,
    onResendClick: (Message) -> Unit,
    onProfileClick: () -> Unit,
    onGiftClick: (Message) -> Unit
) {
    val context = LocalActivity.current as? ComponentActivity ?: return
    val viewModel = hiltViewModel<ChatGiftViewModel>(context)

    val giftMessageData = viewModel.getGiftImage(content)

    if (message.messageDirection == Message.MessageDirection.SEND) {
        ChatGiftMessageItemSender(
            mine = mine,
            message = message,
            giftName = giftMessageData.name,
            giftImage = giftMessageData.image,
            onResendClick = onResendClick,
            onGiftClick = onGiftClick
        )
    } else {
        ChatGiftMessageItemReceiver(
            other = other,
            message = message,
            giftName = giftMessageData.name,
            giftImage = giftMessageData.image,
            onProfileClick = onProfileClick,
            onGiftClick = onGiftClick
        )
    }
}

@Composable
private fun ChatGiftMessageItemSender(
    mine: UserInfo?,
    message: Message,
    giftName: String,
    giftImage: String,
    onResendClick: (Message) -> Unit,
    onGiftClick: (Message) -> Unit
) {
    ChatMessageRowSender(
        mine = mine,
        message = message,
        onResendClick = onResendClick
    ) {
        Box(
            modifier = Modifier
                .width(180.dp)
                .wrapContentHeight()
                .background(
                    color = Color(0xFF988DE2),
                    shape = RoundedCornerShape(
                        topStart = BUBBLE_SHAPE_SIZE,
                        topEnd = BUBBLE_SHAPE_SIZE,
                        bottomEnd = 0.dp,
                        bottomStart = BUBBLE_SHAPE_SIZE
                    )
                )
                .padding(10.dp)
                .noRippleClickable(onClick = { onGiftClick(message) })
        ) {
            Row {
                Column(
                    modifier = Modifier.weight(1f).padding(end = 10.dp),
                    verticalArrangement = Arrangement.spacedBy(5.dp)
                ) {
                    Text(
                        text = stringResource(R.string.sent_gift_title),
                        style = TextStyle(
                            fontSize = 12.sp,
                            lineHeight = 18.sp,
                            fontWeight = FontWeight.W400,
                            color = Color.White.copy(alpha = 0.8f),
                        )
                    )

                    Text(
                        text = giftName,
                        style = TextStyle(
                            fontSize = 14.sp,
                            lineHeight = 12.sp,
                            fontWeight = FontWeight.W400,
                            color = Color.White,
                        ),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                Box(
                    modifier = Modifier
                        .size(60.dp)
                        .background(
                            color = Color.White,
                            shape = RoundedCornerShape(10.dp)
                        )
                ) {
                    AsyncImage(
                        model = giftImage,
                        contentDescription = null,
                        contentScale = ContentScale.FillBounds,
                        modifier = Modifier.matchParentSize()
                    )
                }
            }
        }
    }
}

@Composable
private fun ChatGiftMessageItemReceiver(
    other: UserInfo?,
    message: Message,
    giftName: String,
    giftImage: String,
    onProfileClick: () -> Unit,
    onGiftClick: (Message) -> Unit
) {
    val status = message.unwrapStatus()


    ChatMessageRowReceiver(
        other = other,
        message = message,
        onProfileClick = onProfileClick
    ) {
        Box(
            modifier = Modifier
                .width(180.dp)
                .wrapContentHeight()
                .background(
                    color = Color.White,
                    shape = RoundedCornerShape(
                        topStart = BUBBLE_SHAPE_SIZE,
                        topEnd = BUBBLE_SHAPE_SIZE,
                        bottomEnd = BUBBLE_SHAPE_SIZE,
                        bottomStart = 0.dp
                    )
                )
                .padding(10.dp)
                .noRippleClickable(onClick = { onGiftClick(message) })
        ) {
            Row {
                Column(
                    modifier = Modifier.weight(1f).padding(end = 10.dp),
                    verticalArrangement = Arrangement.spacedBy(5.dp)
                ) {
                    Text(
                        text = stringResource(R.string.got_gift_title),
                        style = TextStyle(
                            fontSize = 12.sp,
                            lineHeight = 18.sp,
                            fontWeight = FontWeight.W400,
                            color = TextGray999,
                        )
                    )

                    Text(
                        text = giftName,
                        style = TextStyle(
                            fontSize = 14.sp,
                            lineHeight = 12.sp,
                            fontWeight = FontWeight.W400,
                            color = TextBlack333,
                        ),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.fillMaxWidth()
                    )

                    if (status == UnwrapStatus.UNWRAPPED) {
                        Box(
                            modifier = Modifier
                                .background(
                                    color = Color(0xFF988DE2),
                                    shape = RoundedCornerShape(size = 30.dp)
                                )
                                .padding(horizontal = 5.dp, vertical = 2.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(R.string.gift_unwrap),
                                style = TextStyle(
                                    fontSize = 10.sp,
                                    lineHeight = 16.sp,
                                    fontWeight = FontWeight.W400,
                                    color = Color.White,
                                )
                            )
                        }
                    }
                }


                Box(
                    modifier = Modifier
                        .size(60.dp)
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    Color(0xFFE7E4FF),
                                    Color(0xFFFDFDFF)
                                )
                            ),
                            shape = RoundedCornerShape(10.dp)
                        )
                ) {
                    AsyncImage(
                        model = giftImage,
                        contentDescription = null,
                        contentScale = ContentScale.FillBounds,
                        modifier = Modifier.matchParentSize()
                    )
                }
            }
        }
    }
}

private val BUBBLE_SHAPE_SIZE = 15.dp

@Preview
@Composable
private fun ChatGiftMessageItemPreview(
    @PreviewParameter(BooleanProvider::class) isSender: Boolean
) {
    val mine = UserInfo(userId = "1")
    val other = UserInfo(userId = "2")

    val content = GiftMessageContent()
    val message = Message.obtain("", Conversation.ConversationType.PRIVATE, content)

    message.messageDirection = if (isSender) Message.MessageDirection.SEND else Message.MessageDirection.RECEIVE
    message.setExpansion(hashMapOf("status" to "0"))

    ChatGiftMessageItem(mine, other, message, content, {}, {}, {})
}