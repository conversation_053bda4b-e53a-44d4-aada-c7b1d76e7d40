package com.flutterup.app.screen.chat

import androidx.activity.ComponentActivity
import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyGridItemScope
import androidx.compose.foundation.lazy.grid.LazyHorizontalGrid
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.SheetState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.component.AppBottomSheetDialog
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.component.pager.DotPagerIndicator
import com.flutterup.app.design.component.pager.DotPagerIndicatorDefaults
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.BorderPrimary
import com.flutterup.app.design.theme.LabelBackgroundPrimary
import com.flutterup.app.design.theme.PurpleTertiaryContainer
import com.flutterup.app.design.theme.TextBlack
import com.flutterup.app.design.theme.TextGray999
import com.flutterup.app.model.AppPaymentFrom
import com.flutterup.app.model.UserInfo
import com.flutterup.app.screen.LocalAppState
import com.flutterup.app.screen.chat.state.ChatGiftUiState
import com.flutterup.app.screen.chat.vm.ChatGiftViewModel
import com.flutterup.app.screen.chat.vm.GiftUIState
import com.flutterup.base.utils.toDecimalSubstring
import com.flutterup.gifts.entity.GiftEntity
import com.flutterup.gifts.entity.PreloadResult
import kotlin.math.min


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatGiftDialogScreen(
    isShown: Boolean,
    userInfo: UserInfo?,
    onDismissRequest: () -> Unit,
    sheetState: SheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
    onGiftSendClick: (GiftEntity) -> Unit = {},
) {
    val context = LocalActivity.current as? ComponentActivity ?: return
    val navCenter = LocalAppState.current.navCenter
    val viewModel = hiltViewModel<ChatGiftViewModel>(context)
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    AppBottomSheetDialog(
        isShown = isShown,
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        backgroundColor = Color.White,
        scrimColor = Color.Transparent,
        sheetState = sheetState,
        onDismissRequest = onDismissRequest,
        onConfirmRequest = {},
        cancelText = null,
        confirmText = null,
    ) {
        ChatGiftDialogContent(
            userInfo,
            uiState,
            onTopUpClick = {
                //TODO 修改为真实的from
                navCenter.navigateToPaymentDiamonds(from = AppPaymentFrom.UNKNOWN)
            },
            onGiftRetryClick = {
                viewModel.reloadGifts()
            },
            onGiftClick = { viewModel.updateCurrentGift(it) },
            onGiveGiftClick = onGiftSendClick
        )
    }
}

@Composable
private fun ChatGiftDialogContent(
    userInfo: UserInfo?,
    uiState: ChatGiftUiState,
    onTopUpClick: () -> Unit = {},
    onGiftRetryClick: () -> Unit = {},
    onGiftClick: (GiftEntity) -> Unit = {},
    onGiveGiftClick: (GiftEntity) -> Unit = {},
) {
    val diamond = userInfo?.right?.diamonds ?: 0f
    val backgroundShape = RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)

    Box(
        modifier = Modifier
            .border(width = 1.dp, color = Color.White, backgroundShape)
            .fillMaxWidth()
            .wrapContentHeight()
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color(0xFFEEC491),
                        Color(0xFFEBBFA1)
                    )
                ),
                shape = backgroundShape
            )
    ) {
        Column {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(46.dp)
                    .padding(start = 27.dp, end = 25.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(R.mipmap.ic_diamond),
                    contentDescription = null,
                    modifier = Modifier.size(25.dp)
                )

                Text(
                    text = diamond.toDecimalSubstring(isReduce = false),
                    style = TextStyle(
                        fontSize = 16.sp,
                        lineHeight = 12.14.sp,
                        fontWeight = FontWeight.W900,
                        color = Color.White,
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Visible,
                    modifier = Modifier
                        .padding(horizontal = 13.dp)
                        .weight(1f)
                )

                Box(
                    modifier = Modifier.size(80.dp, 30.dp).noRippleClickable(onClick = onTopUpClick)
                ) {
                    Image(
                        painter = painterResource(R.mipmap.ic_chat_diamon_topup),
                        contentDescription = null,
                        contentScale = ContentScale.FillBounds,
                        modifier = Modifier.matchParentSize()
                    )

                    Text(
                        text = stringResource(R.string.chat_diamond_top_up),
                        style = TextStyle(
                            fontSize = 16.sp,
                            lineHeight = 22.sp,
                            fontWeight = FontWeight.W900,
                            color = Color.White,
                        ),
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }

            Column(
                modifier = Modifier.background(Color.White, backgroundShape)
            ) {
                Row(
                    modifier = Modifier
                        .padding(top = 10.dp)
                        .padding(horizontal = 25.dp)
                        .fillMaxWidth()
                        .height(40.dp)
                        .background(PurpleTertiaryContainer, RoundedCornerShape(10.dp))
                        .padding(horizontal = 5.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (uiState.currentGift != null) {
                        AsyncImage(
                            model = uiState.currentGift.imageUrl,
                            contentDescription = null,
                            contentScale = ContentScale.FillBounds,
                            modifier = Modifier.size(29.dp)
                        )
                    }

                    if (uiState.currentGift != null) {
                        Text(
                            text = uiState.currentGift.desc.orEmpty(),
                            style = TextStyle(
                                fontSize = 12.sp,
                                lineHeight = 12.sp,
                                fontWeight = FontWeight.W400,
                                color = Color(0xFF988DE2),
                            ),
                            modifier = Modifier
                                .padding(horizontal = 10.dp)
                                .weight(1f)
                                .basicMarquee()
                        )
                    }
                }

                when(val giftUiState = uiState.giftUiState) {
                    GiftUIState.Error,
                    GiftUIState.Idle,
                    GiftUIState.PartialSuccess, -> {
                        Box(modifier = Modifier.fillMaxWidth().height(300.dp)) {
                            Column(
                                modifier = Modifier.align(Alignment.Center),
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Text(
                                    text = stringResource(R.string.gift_loading_failed),
                                    style = TextStyle(
                                        fontSize = 14.sp,
                                        lineHeight = 14.sp,
                                        fontWeight = FontWeight.W400,
                                        color = Color.Black,
                                    ),
                                )

                                AppContinueButton(
                                    onClick = onGiftRetryClick,
                                    text = stringResource(R.string.retry),
                                    modifier = Modifier
                                        .padding(top = 10.dp)
                                        .width(80.dp)
                                        .height(40.dp),
                                    contentPadding = PaddingValues(0.dp)
                                )
                            }
                        }
                    }
                    is GiftUIState.Preloading -> {
                        Box(modifier = Modifier.fillMaxWidth().height(300.dp)) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(50.dp).align(Alignment.Center),
                                color = BorderPrimary,
                            )
                        }
                    }
                    is GiftUIState.Success -> ChatGiftPager(
                        uiState = uiState,
                        giftUiState = giftUiState,
                        onGiftClick = onGiftClick,
                        onGiveGiftClick = onGiveGiftClick
                    )
                }
            }
        }
    }
}

@Composable
private fun ChatGiftPager(
    uiState: ChatGiftUiState,
    giftUiState: GiftUIState.Success,
    subRange: Int = PAGE_GIFT_SIZE,
    onGiftClick: (GiftEntity) -> Unit = {},
    onGiveGiftClick: (GiftEntity) -> Unit = {},
) {
    val data = giftUiState.result.cachedGiftsData
    val pageCount = if (subRange > 0) (data.size + subRange - 1) / subRange else 0
    val pagerState = rememberPagerState(initialPage = 0) { pageCount }

    Column {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxWidth()
        ) { index ->
            val start = index * subRange
            val end = min(start + subRange, data.size)

            LazyVerticalGrid(
                horizontalArrangement = Arrangement.spacedBy(10.dp),
                verticalArrangement = Arrangement.spacedBy(6.dp),
                columns = GridCells.Fixed(3),
//                maxItemsInEachRow = 3, //每行最多3个
//                maxLines = 2, //最多就2行
                modifier = Modifier.padding(horizontal = 25.dp).padding(top = 10.dp).fillMaxWidth()
            ) {
                items(data.subList(start, end)) { gift ->
                    ChatGiftItem(
                        uiState = uiState,
                        gift = gift,
                        onGiftClick = onGiftClick,
                        onGiveGiftClick = onGiveGiftClick
                    )
                }
            }
        }

        DotPagerIndicator(
            pageCount = pageCount,
            selectedPage = pagerState.currentPage,
            modifier = Modifier
                .padding(top = 14.dp, bottom = 10.dp)
                .align(Alignment.CenterHorizontally),
            config = DotPagerIndicatorDefaults.copy(
                orientation = Orientation.Horizontal,
                spaceBetween = 10.dp,
                unselectedColor = Color(0x33474747),
                selectedColor = LabelBackgroundPrimary
            )
        )
    }
}

@Composable
private fun LazyGridItemScope.ChatGiftItem(
    uiState: ChatGiftUiState,
    gift: GiftEntity,
    onGiftClick: (GiftEntity) -> Unit = {},
    onGiveGiftClick: (GiftEntity) -> Unit = {},
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1f)
            .noRippleClickable { onGiftClick(gift) },
        shape = RoundedCornerShape(15.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F4FF))
    ) {
        if (uiState.currentGift?.giftId == gift.giftId) { //选中中的gift
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .border(1.dp, LabelBackgroundPrimary, RoundedCornerShape(15.dp))
            ) {
                Column(
                    modifier = Modifier.matchParentSize(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    AsyncImage(
                        model = gift.gifUrl,
                        contentDescription = null,
                        contentScale = ContentScale.FillBounds,
                        modifier = Modifier.size(48.dp)
                    )

                    Text(
                        text = stringResource(R.string.gift_price_sub, gift.price ?: 0),
                        style = TextStyle(
                            fontSize = 10.sp,
                            lineHeight = 12.sp,
                            fontWeight = FontWeight.W300,
                            color = TextGray999,
                        )
                    )
                }

                Box(
                    modifier = Modifier
                        .fillMaxHeight(0.33f)
                        .fillMaxWidth()
                        .background(LabelBackgroundPrimary, RoundedCornerShape(bottomStart = 15.dp, bottomEnd = 15.dp))
                        .align(Alignment.BottomCenter)
                        .noRippleClickable { onGiveGiftClick(gift) },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = stringResource(R.string.give),
                        style = TextStyle(
                            fontSize = 14.sp,
                            lineHeight = 12.sp,
                            fontWeight = FontWeight.W900,
                            color = Color.White
                        )
                    )
                }
            }
        } else {
            AsyncImage(
                model = gift.imagePath,
                contentDescription = null,
                contentScale = ContentScale.FillBounds,
                modifier = Modifier.size(64.dp).align(Alignment.CenterHorizontally)
            )

            Text(
                text = gift.name.orEmpty(),
                style = TextStyle(
                    fontSize = 10.sp,
                    lineHeight = 12.sp,
                    fontWeight = FontWeight.W300,
                    color = TextBlack,
                ),
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )

            Row(
                modifier = Modifier
                    .padding(top = 4.dp)
                    .wrapContentSize()
                    .background(color = Color(0xFFDBA47D), shape = RoundedCornerShape(size = 12.dp))
                    .padding(horizontal = 5.dp, vertical = 3.dp)
                    .align(Alignment.CenterHorizontally),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(R.mipmap.ic_diamond),
                    contentDescription = null,
                    modifier = Modifier.size(10.dp)
                )

                Text(
                    text = gift.price.toString(),
                    style = TextStyle(
                        fontSize = 10.sp,
                        lineHeight = 12.sp,
                        fontWeight = FontWeight.W900,
                        color = Color.White,
                    ),
                    modifier = Modifier.padding(start = 2.dp)
                )
            }
        }
    }
}

private const val PAGE_GIFT_SIZE = 6

@Preview
@Composable
private fun ChatGiftDialogScreenPreview() {
    Surface {
        Box(
            modifier = Modifier.fillMaxSize().background(color = Color.Black),
            contentAlignment = Alignment.BottomCenter
        ) {
            ChatGiftDialogContent(
                userInfo = UserInfo(),
                uiState = ChatGiftUiState(
                    currentGift = GiftEntity(
                        giftId = "gift0",
                        name = "gift0",
                        price = 10,
                        imageUrl = "",
                        gifUrl = "",
                        videoUrl = "",
                        imagePath = null,
                        gifPath = null,
                        videoPath = null,
                        desc = "I'm a copywriter.I'm a copywriter.",
                        version = 1
                    ),
                    giftUiState = GiftUIState.Success(
                        PreloadResult.Success(
                            successfulGifts = emptyList(),
                            failedGifts = emptyList(),
                            cachedGiftsData = List(30) {
                                GiftEntity(
                                    giftId = "gift$it",
                                    name = "gift$it",
                                    price = 10,
                                    imageUrl = "",
                                    gifUrl = "",
                                    videoUrl = "",
                                    imagePath = null,
                                    gifPath = null,
                                    videoPath = null,
                                    desc = "I'm a copywriter.I'm a copywriter.",
                                    version = 1
                                )
                            })
                    )
                )
            )
        }
    }
}