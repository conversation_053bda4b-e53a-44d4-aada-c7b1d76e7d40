package com.flutterup.app.screen.discover.state

import com.flutterup.app.model.DiscoverItemEntity
import com.flutterup.app.model.DiscoverListEntity
import com.flutterup.base.BaseState

data class DiscoverState(
    val data: DiscoverListEntity,
    val headimg: String? = null,
    val nickname: String? = null,
    override val isLoading: Boolean = false,
) : BaseState {

    val discoverList: List<DiscoverItemEntity> = data.matchList

    val swipeEmptyList: List<String>? = data.privacyList

    val isAllLoaded = discoverList.isNotEmpty() && discoverList.all { it.userId == null }

    companion object {
        val EMPTY = DiscoverState(DiscoverListEntity(
            matchList = List(3) { DiscoverItemEntity() }
        ))
    }
}