@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.settings

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.component.AppTextField
import com.flutterup.app.design.component.AppTitleText
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.TextBlack333
import com.flutterup.app.design.theme.TextGray999
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.settings.component.DeleteAccountReasonList
import com.flutterup.app.screen.settings.state.DeleteAccountState
import com.flutterup.app.screen.settings.vm.DeleteAccountViewModel


@Composable
fun DeleteAccountScreen() {
    val navController = LocalNavController.current
    val viewModel: DeleteAccountViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    DeleteAccountContent(
        uiState = uiState,
        onBackClick = navController::popBackStack,
        onReasonSelected = { viewModel.updateSelectedReasonIndex(it) },
        onCustomReasonChange = { viewModel.updateCustomReason(it) },
        onSubmitClick = { viewModel.submit() }
    )
}

@Composable
private fun DeleteAccountContent(
    uiState: DeleteAccountState,
    onBackClick: () -> Unit = {},
    onReasonSelected: (Int) -> Unit = {},
    onCustomReasonChange: (String) -> Unit = {},
    onSubmitClick: () -> Unit = {},
) {
    AppScaffold(
        title = { AppTitleText(text = stringResource(R.string.delete_account)) },
        onBackClick = onBackClick,
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) { paddingValues ->
        Box {
            Image(
                painter = painterResource(R.mipmap.ic_common_top_bg),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier.fillMaxWidth()
            )

            Column(
                modifier = Modifier
                    .padding(paddingValues)
                    .fillMaxSize()
            ) {
                Text(
                    text = stringResource(R.string.delete_account_subtitle),
                    style = TextStyle(
                        fontSize = 17.sp,
                        lineHeight = 34.sp,
                        fontWeight = FontWeight.W500,
                        color = TextBlack333,
                    ),
                    modifier = Modifier.padding(horizontal = 20.dp).fillMaxWidth()
                )

                DeleteAccountReasonList(
                    items = uiState.reasonList,
                    selectedReasonIndex = uiState.selectedReasonIndex,
                    onReasonSelected = onReasonSelected
                )

                if (uiState.isCustomReasonSelected) {
                    AppTextField(
                        value = uiState.customReason,
                        onValueChange = onCustomReasonChange,
                        textStyle = TextStyle(
                            fontSize = 14.sp,
                            lineHeight = 20.sp,
                            fontWeight = FontWeight.W400,
                            color = TextBlack333,
                        ),
                        contentPadding = PaddingValues(horizontal = 20.dp, vertical = 16.dp),
                        modifier = Modifier
                            .padding(horizontal = 16.dp)
                            .fillMaxWidth()
                            .defaultMinSize(minHeight = 80.dp),
                        placeholder = {
                            Text(
                                text = stringResource(R.string.delete_account_input_placeholder),
                                style = TextStyle(
                                    fontSize = 14.sp,
                                    lineHeight = 20.sp,
                                    fontWeight = FontWeight.W400,
                                    color = TextGray999,
                                )
                            )
                        },
                    )
                }
            }

            AppContinueButton(
                text = stringResource(R.string.submit),
                enabled = uiState.isEnabled,
                isLoading = uiState.isLoading,
                onClick = { if (!uiState.isLoading) onSubmitClick() },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 32.dp)
                    .padding(horizontal = 24.dp)
                    .align(Alignment.BottomCenter)
            )
        }
    }
}




@Preview
@Composable
private fun DeleteAccountScreenPreview() {
    AppTheme {
        DeleteAccountContent(
            uiState = DeleteAccountState(
                reasonList = listOf("1", "2", "3", "4", "5", "6", "7", "8", "9", "10"),
                selectedReasonIndex = 9,
            )
        )
    }
}