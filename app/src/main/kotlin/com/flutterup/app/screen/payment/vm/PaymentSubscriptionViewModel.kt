package com.flutterup.app.screen.payment.vm

import com.flutterup.app.screen.payment.state.PaymentSubscriptUiState
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class PaymentSubscriptionViewModel @Inject constructor(
    private val userMonitor: UserMonitor,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(PaymentSubscriptUiState())

    val uiState = combine(
        _uiState,
        userMonitor.userInfoState,
        loadingState
    ) { ui, userInfo, loading ->
        ui.copy(
            isVip = userInfo?.right?.vip == 1,
            isLoading = loading
        )
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value.copy(isLoading = loadingState.value)
    )

    fun updateSelected(selectedIndex: Int) {
        _uiState.update { it.copy(selected = selectedIndex) }
    }

    fun pay() {

    }

    fun logout() {
        userMonitor.logout()
    }
}
