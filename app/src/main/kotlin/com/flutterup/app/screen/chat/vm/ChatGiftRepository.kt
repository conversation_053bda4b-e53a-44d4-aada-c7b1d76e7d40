package com.flutterup.app.screen.chat.vm

import com.flutterup.app.network.ApiService
import com.flutterup.base.BaseRepository
import com.flutterup.gifts.entity.GiftResourceInfo
import javax.inject.Inject

class ChatGiftRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {

    suspend fun getGiftList(): List<GiftResourceInfo>? {
        val result = apiService.getGiftList()
        return result.data
    }
}