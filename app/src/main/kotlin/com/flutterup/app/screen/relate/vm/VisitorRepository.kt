package com.flutterup.app.screen.relate.vm

import com.flutterup.app.model.VisitorEntity
import com.flutterup.app.network.ApiService
import com.flutterup.base.BaseRepository
import javax.inject.Inject

class VisitorRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {

    suspend fun getVisitorList(lastId: Long? = null): VisitorEntity? {
        val result = apiService.getVisitorList(lastId)
        if (result.isSuccess) {
            return result.data
        }
        return null
    }
}