package com.flutterup.app.screen.profile.vm

import com.flutterup.app.model.UserInfo
import com.flutterup.app.network.ApiService
import com.flutterup.base.BaseRepository
import javax.inject.Inject

class OtherProfileRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {

    suspend fun getOtherProfileInfo(userId: String): UserInfo? {
        val result = apiService.getAnotherProfileInfo(userId)
        if (result.isSuccess) {
            return result.data
        }
        return null
    }
}