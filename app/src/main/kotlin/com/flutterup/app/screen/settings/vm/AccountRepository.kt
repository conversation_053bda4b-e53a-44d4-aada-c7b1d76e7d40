package com.flutterup.app.screen.settings.vm

import com.flutterup.app.model.ChangePasswordEntity
import com.flutterup.app.model.UserNotificationEntity
import com.flutterup.app.network.ApiService
import com.flutterup.app.screen.settings.state.NotificationState.NotificationType
import com.flutterup.base.BaseRepository
import javax.inject.Inject

class AccountRepository @Inject constructor(
    private val apiService: ApiService
) : BaseRepository() {

    suspend fun changePassword(password: String): Boolean {
        val result = apiService.changePassword(ChangePasswordEntity(password))

        return result.isSuccess
    }

    suspend fun deleteAccount(): Boolean {
        val result = apiService.deleteAccount()

        return result.isSuccess
    }

    suspend fun updateNotificationSettings(
        currentPushConfig: Int,
        type: NotificationType,
        checked: Boolean
    ): Pair<Boolean, Int> {
        // 拿到对应的bit值
        val bitValue = type.mask

        // 用位运算处理对应开关
        val newConfig = if (checked) {
            currentPushConfig or bitValue     // 开：二进制这位设为1
        } else {
            currentPushConfig and bitValue.inv() // 关：二进制这位设为0
        }

        // 调用接口
        val success = updateNotificationSettings(newConfig)
        return success to newConfig
    }

    suspend fun updateNotificationSettings(config: Int): Boolean {
        val result = apiService.updateNotificationSettings(config)
        return result.isSuccess
    }
}