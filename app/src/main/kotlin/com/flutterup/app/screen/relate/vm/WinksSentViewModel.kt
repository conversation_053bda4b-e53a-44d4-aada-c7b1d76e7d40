package com.flutterup.app.screen.relate.vm

import com.flutterup.app.model.WinkType
import com.flutterup.app.screen.relate.state.WinksSentState
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseRepositoryViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class WinksSentViewModel @Inject constructor(
    private val winksRepository: WinksRepository,
    private val userMonitor: UserMonitor,
) : BaseRepositoryViewModel(winksRepository) {

    private val _uiState = MutableStateFlow(WinksSentState())

    val uiState: StateFlow<WinksSentState> = _uiState.asStateFlow()


    init {
        refresh()
    }

    fun refresh() {
        scope.launch {
            _uiState.update { it.copy(isRefreshing = true) }
            val result = winksRepository.getWinksList(WinkType.Sent)
            result?.let { result ->
                _uiState.update { it.copy(winks = result.list.orEmpty()) }
            }
        }.invokeOnCompletion {
            _uiState.update { it.copy(isRefreshing = false) }
        }
    }

    fun loadMore() {
        scope.launch {
            _uiState.update { it.copy(isLoadingMore = true) }
            val lastId = _uiState.value.winks.lastOrNull()?.id
            val result = winksRepository.getWinksList(WinkType.Sent, lastId)
            result?.let { result ->
                _uiState.update { it.copy(winks = it.winks + result.list.orEmpty()) }
            }
        }.invokeOnCompletion {
            _uiState.update { it.copy(isLoadingMore = false) }
        }
    }
}