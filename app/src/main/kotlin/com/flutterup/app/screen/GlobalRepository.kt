package com.flutterup.app.screen

import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import android.provider.OpenableColumns
import com.flutterup.app.model.Tag
import com.flutterup.app.model.UpgradeEntity
import com.flutterup.app.model.UploadResponse
import com.flutterup.app.network.GlobalApiService
import com.flutterup.base.AppDirs
import com.flutterup.base.BaseRepository
import com.flutterup.base.Dirs
import com.flutterup.base.utils.DateUtils
import com.flutterup.base.utils.FileUtils
import com.flutterup.network.AppClient
import com.flutterup.network.AppClients
import com.flutterup.network.DownloadApiService
import com.flutterup.network.impl.NetworkServiceProvider
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import javax.inject.Inject
import kotlin.io.readBytes

class GlobalRepository @Inject constructor(
    @ApplicationContext private val context: Context,
    @Dirs(AppDirs.APP_CACHE) private val fileDir: File,
    @AppClient(AppClients.DOWNLOAD) private val networkService: NetworkServiceProvider,
    private val globalApiService: GlobalApiService,
) : BaseRepository() {
    suspend fun uploadUri(uri: Uri): String? {
        val contentResolver = context.contentResolver
        val inputStream: InputStream? = contentResolver.openInputStream(uri)
        val mimeType: String? = contentResolver.getType(uri)
        val fileName = getFileNameByMineType(uri, contentResolver, mimeType)

        if (inputStream != null) {
            try {
                // 将 InputStream 转换为 RequestBody
                // 注意：readBytes() 会将整个文件读入内存，对大文件不友好
                val requestBody = inputStream.readBytes().toRequestBody(mimeType?.toMediaTypeOrNull())
                inputStream.close() // 关闭流

                val urlResponse = getUploadUrl(fileName) ?: return null
                globalApiService.uploadFile(urlResponse.uploadUrl, requestBody)
                return urlResponse.cdnUrl
            } finally {
                try {
                    inputStream.close()
                } catch (_: IOException) { // ignore this
                }
            }
        }
        return null
    }

    suspend fun downloadToFile(url: String, fileName: String): File? = withContext(Dispatchers.IO) {
        val destFile = File(fileDir, fileName)
        try {
            destFile.parentFile?.mkdirs()

            networkService[DownloadApiService::class.java].download(url).body()?.byteStream()?.use { input ->
                FileOutputStream(destFile).use { output ->
                    input.copyTo(output)
                }
            }
            destFile
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    suspend fun checkAppUpgrade(): UpgradeEntity? {
        val result = globalApiService.checkUpdate()
        if (result.isSuccess) {
            return result.data
        }
        return null
    }

    private suspend fun getUploadUrl(fileName: String): UploadResponse? {
        val result = globalApiService.getUploadUrl(fileName)
        return result.data
    }

    private fun getFileNameByMineType(uri: Uri, contentResolver: ContentResolver, mimeType: String?): String {
        val postfix = mimeTypeToPostfix(mimeType)

        // 获取文件名
        var filename = getFileName(uri, contentResolver)
        if (filename.isNullOrEmpty()) {
            val sb = StringBuilder(DateUtils.formatTimestamp(System.currentTimeMillis(), DateUtils.defaultFileDateFormat))
            if (postfix != null) {
                sb.append(".")
                sb.append(postfix)
            }
            filename = sb.toString()
        }
        return filename
    }

    private fun mimeTypeToPostfix(mimeType: String?): String? {
        if (mimeType == null) return null

        return when {
            mimeType.startsWith("image/") -> FileUtils.IMAGE_SUFFIX
            mimeType.startsWith("video/") -> FileUtils.VIDEO_SUFFIX
            else -> null
        }
    }

    private fun getFileName(uri: Uri, contentResolver: ContentResolver): String? {
        var result: String? = null
        if (uri.scheme == ContentResolver.SCHEME_CONTENT) {
            contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                    if (nameIndex != -1) {
                        result = cursor.getString(nameIndex)
                    }
                }
            }
        }
        if (result == null) {
            result = uri.path
            val cut = result?.lastIndexOf('/')
            if (cut != -1 && cut != null) {
                result = result.substring(cut + 1)
            }
        }
        return result
    }
}