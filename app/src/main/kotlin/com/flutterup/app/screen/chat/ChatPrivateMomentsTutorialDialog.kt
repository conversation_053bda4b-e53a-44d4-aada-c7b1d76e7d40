
package com.flutterup.app.screen.chat

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.component.AppBottomSheetDialog
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.insets.safeArea
import com.flutterup.app.design.theme.TextBlack
import com.flutterup.app.screen.LocalNavController


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatPrivateMomentsTutorialDialog() {
    val navController = LocalNavController.current
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)


    AppBottomSheetDialog(
        isShown = true,
        onDismissRequest = navController::popBackStack,
        onConfirmRequest = {},
        sheetState = sheetState,
        cancelText = null,
        confirmText = null,
        modifier = Modifier.windowInsetsPadding(WindowInsets.safeArea)
    ) {
        ChatPrivateMomentsTutorialContent(
            onContinueClick = {
                navController.popBackStack()
                navController.navigate(ChatPrivateMomentsRoute)
            },
        )
    }
}

@Composable
private fun ChatPrivateMomentsTutorialContent(
    onContinueClick: () -> Unit = {},
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .clip(RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp))
            .background(Color.White)
    ) {
        Image(
            painter = painterResource(R.mipmap.ic_common_top_bg),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier.fillMaxWidth()
        )

        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_private_moments_tutorial),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier.size(260.dp)
            )

            Column(
                modifier = Modifier
                    .padding(top = 25.dp)
                    .padding(horizontal = 40.dp)
                    .fillMaxWidth()
            ) {
                Text(
                    text = stringResource(R.string.private_moments_tutorial_title1),
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W900,
                        color = TextBlack,
                    )
                )

                Text(
                    text = stringResource(R.string.private_moments_tutorial_desc1),
                    style = TextStyle(
                        fontSize = 10.sp,
                        fontWeight = FontWeight.W400,
                        color = TextBlack,
                    ),
                    modifier = Modifier.padding(top = 5.dp)
                )


                Text(
                    text = stringResource(R.string.private_moments_tutorial_title2),
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W900,
                        color = TextBlack,
                    ),
                    modifier = Modifier.padding(top = 10.dp)
                )

                Text(
                    text = stringResource(R.string.private_moments_tutorial_desc2),
                    style = TextStyle(
                        fontSize = 10.sp,
                        fontWeight = FontWeight.W400,
                        color = TextBlack,
                    ),
                    modifier = Modifier.padding(top = 5.dp)
                )

                Text(
                    text = stringResource(R.string.private_moments_tutorial_title3),
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W900,
                        color = TextBlack,
                    ),
                    modifier = Modifier.padding(top = 10.dp)
                )

                Text(
                    text = stringResource(R.string.private_moments_tutorial_desc3),
                    style = TextStyle(
                        fontSize = 10.sp,
                        fontWeight = FontWeight.W400,
                        color = TextBlack,
                    ),
                    modifier = Modifier.padding(top = 5.dp)
                )
            }


            AppContinueButton(
                onClick = onContinueClick,
                enabled = true,
                isLoading = false,
                text = stringResource(R.string.confirm),
                modifier = Modifier
                    .padding(top = 26.dp)
                    .padding(horizontal = 24.dp)
                    .fillMaxWidth()
                    .height(50.dp)
            )

            Spacer(Modifier.height(30.dp))
        }
    }
}

@Preview
@Composable
private fun ChatPrivateMomentsTutorialDialogPreview() {
    Surface {
        Box(
            modifier = Modifier.fillMaxSize().background(color = Color.Black),
            contentAlignment = Alignment.BottomCenter
        ) {
            ChatPrivateMomentsTutorialContent()
        }
    }
}