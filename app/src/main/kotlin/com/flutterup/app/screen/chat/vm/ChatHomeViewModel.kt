package com.flutterup.app.screen.chat.vm

import android.content.Context
import com.flutterup.app.R
import com.flutterup.app.model.UserInfo
import com.flutterup.app.screen.chat.state.ChatConversation
import com.flutterup.app.screen.chat.state.ChatHomeUiState
import com.flutterup.app.screen.common.vm.PermissionRepository
import com.flutterup.app.utils.GlobalSettingsMonitor
import com.flutterup.app.utils.UserUnreadMonitor
import com.flutterup.app.utils.chat.ChatConversationComparator
import com.flutterup.app.utils.chat.ChatConversationUtils
import com.flutterup.app.utils.chat.core.ChatMessageMonitor
import com.flutterup.app.utils.chat.core.ChatOnReceivedMessageListener
import com.flutterup.app.utils.chat.core.ChatUnreadMonitor
import com.flutterup.base.BaseRepositoryViewModel
import com.flutterup.base.utils.Timber
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.imlib.model.ReceivedProfile
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ChatHomeViewModel
@Inject constructor(
    @ApplicationContext private val context: Context,
    private val chatHomeRepository: ChatHomeRepository,
    private val chatConversationRepository: ChatConversationRepository,
    private val permissionRepository: PermissionRepository,
    private val unreadMonitor: ChatUnreadMonitor,
    private val userUnreadMonitor: UserUnreadMonitor,
    private val chatMessageMonitor: ChatMessageMonitor,
    private val settingsMonitor: GlobalSettingsMonitor,
) :
    BaseRepositoryViewModel(chatHomeRepository, chatConversationRepository, permissionRepository),
    ChatOnReceivedMessageListener {

    companion object {
        private const val MAX_TOP_CONVERSATION_COUNT = 5 //最多置顶5个
    }

    private val _uiState = MutableStateFlow(ChatHomeUiState())
    private val _newConnections = MutableStateFlow<List<UserInfo>>(emptyList())
    private val _conversations = MutableStateFlow<List<ChatConversation>>(emptyList())

    private val _conversationTopValue = _conversations.map { conversations ->
        conversations.count { it.conversation.isTop }
    } .stateIn(
        scope,
        SharingStarted.Eagerly,
        0
    )

    val uiState: StateFlow<ChatHomeUiState> = combine(
        _uiState,
        _newConnections,
        _conversations,
        unreadMonitor.totalSystemMessagesCount,
        userUnreadMonitor.unreadCount,
    ) { ui, newConnections, conversations, totalSystemMessagesCount, userUnreadCount ->

        val filteredConversations = conversations.filterNot { conversation ->
            newConnections.any { it.userId == conversation.conversation.targetId }
        }

        ChatHomeUiState(
            newConnections = newConnections,
            totalSystemMessagesCount = totalSystemMessagesCount,
            userUnreadCount = userUnreadCount,
            conversations = filteredConversations,

            isRefreshing = ui.isRefreshing,
            isLoadingMore = ui.isLoadingMore,
            hasNoMoreData = ui.hasNoMoreData,
            isCloseNotificationBanner = ui.isCloseNotificationBanner,
        )
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value
    )

    init {
        chatMessageMonitor.addMessageListener(this)
        refresh()
    }

    override fun onReceivedMessage(
        message: Message?,
        profile: ReceivedProfile?
    ) {
        if (message == null) return

        if (_newConnections.value.any { it.userId == message.senderUserId }) { //判断是否是新连接的消息
            getNewConnections() //重新刷match
        }

        //重新刷会话列表
        getConversations(timestamp = 0L)
    }

    fun upgradeNotificationPermission(enable: Boolean) {
        scope.launch {
            permissionRepository.upgradePermissionStatus(noticeStatus = if (enable) PermissionRepository.PERMISSION_ON else PermissionRepository.PERMISSION_OFF)
        }
    }

    fun getNewConnections(): Job {
        return scope.launch {
            val result = chatHomeRepository.getNewConnections()
            _newConnections.update { result ?: emptyList() } //空也会更新
        }
    }

    fun getConversations(timestamp: Long = lastConversationOperationTime): Job {
        return scope.launch {
            val customServiceAccount = settingsMonitor.customerServiceAccount.value
            val noticeAccount = settingsMonitor.noticeAccount.value

            val excludeList = listOfNotNull(
                customServiceAccount,
                noticeAccount,
            )

            val conversations = chatConversationRepository.getConversations(timestamp)
            val filteredConversations = conversations?.filterNot {
                //筛选掉
                //1. 提示账号
                //2. 系统账号
                //3. 已经存在的id
                it.targetId in excludeList
            }

            val newConversations = mutableListOf<ChatConversation>()

            for (conversation in _conversations.value) {
                //新筛选后列表里有
                if (filteredConversations?.any { it.targetId == conversation.conversation.targetId } == true) {
                    continue
                }

                newConversations.add(conversation)
            }

            filteredConversations?.forEach { conversation ->
                val existUnreadReceivedGift = ChatConversationUtils.isExistUnreadReceivedGift(conversation)
                newConversations.add(ChatConversation(conversation, existUnreadReceivedGift))
            }
            _conversations.update { newConversations.sortedWith(ChatConversationComparator) }
        }
    }

    fun refresh() {
        scope.launch {
            _uiState.update { it.copy(isRefreshing = true) }

            val jobConnection = getNewConnections()
            val jobConversation = getConversations(timestamp = 0L)

            jobConnection.join()
            jobConversation.join()
        }.invokeOnCompletion {
            _uiState.update { it.copy(isRefreshing = false) }
        }
    }

    fun refreshSilence() {
        getNewConnections()
        getConversations(timestamp = 0L)
    }

    fun loadMore() {
        scope.launch {
            _uiState.update { it.copy(isLoadingMore = true) }

            val jobConversation = getConversations()

            jobConversation.join()
        }.invokeOnCompletion {
            _uiState.update { it.copy(isLoadingMore = false) }
        }
    }

    fun topOrUntopConversation(conversation: Conversation, onComplete: () -> Unit = {}) {
        if (conversation.isTop) {
            untopConversation(conversation, onComplete)
        } else {
            topConversation(conversation, onComplete)
        }
    }

    fun deleteConversation(conversation: Conversation, onComplete: () -> Unit = {}) {
        scope.launch {
            val isDeleted = ChatConversationUtils.deleteConversation(conversation, true)
            if (isDeleted) {
                _conversations.update { conversations -> conversations.filterNot { it.conversation.targetId == conversation.targetId } }
            }
        }.invokeOnCompletion { onComplete() }
    }

    override fun onCleared() {
        chatMessageMonitor.removeMessageListener(this)
        super.onCleared()
    }

    private fun topConversation(conversation: Conversation, onComplete: () -> Unit) {
        if (_conversationTopValue.value >= MAX_TOP_CONVERSATION_COUNT) {
            //最多置顶5个
            Timber.showToast(context.getString(R.string.top_chat_limit))
            return
        }

        scope.launch {
            val isSuccess = ChatConversationUtils.topOrUntopConversation(conversation, true)
            if (isSuccess) {
                val newConversation = ChatConversationUtils.getConversation(conversation)
                updateConversationList(newConversation)
            }
        }.invokeOnCompletion { onComplete() }
    }

    private fun untopConversation(conversation: Conversation, onComplete: () -> Unit) {
        scope.launch {
            val isSuccess = ChatConversationUtils.topOrUntopConversation(conversation, false)
            if (isSuccess) {
                val newConversation = ChatConversationUtils.getConversation(conversation)
                updateConversationList(newConversation)
            }
        }.invokeOnCompletion { onComplete() }
    }

    private suspend fun updateConversationList(newConversation: Conversation?) {
        if (newConversation == null) return

        val index = _conversations.value.indexOfFirst { it.conversation.targetId == newConversation.targetId }
        val chatConversation = ChatConversation(newConversation, ChatConversationUtils.isExistUnreadReceivedGift(newConversation))

        val newList = _conversations.value.toMutableList()
        if (index == -1) {
            newList.add(chatConversation)
        } else {
            newList[index] = chatConversation
        }
        _conversations.update { newList.sortedWith(ChatConversationComparator) }
    }

    fun closeNotificationBanner() {
        _uiState.update { it.copy(isCloseNotificationBanner = true) }
    }

    fun resetNotificationBanner() {
        _uiState.update { it.copy(isCloseNotificationBanner = false) }
    }

    private val lastConversationOperationTime: Long
        get() = _conversations.value.lastOrNull()?.conversation?.operationTime ?: 0L
}