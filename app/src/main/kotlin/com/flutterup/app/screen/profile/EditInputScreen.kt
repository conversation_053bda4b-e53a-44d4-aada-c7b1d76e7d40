@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.profile

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppTextField
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.design.theme.WhiteTextFieldColors
import com.flutterup.app.screen.LocalNavController


@Composable
fun EditInputScreen(
    sharedKey: String,
    title: String?,
    hint: String?,
    text: String?,
    limit: Int,
) {
    val navController = LocalNavController.current

    EditInputContent(
        title = title,
        hint = hint,
        text = text,
        limit = limit,
        onBackClick = navController::popBackStack,
        onConfirmClick = {
            navController.previousBackStackEntry?.savedStateHandle?.set(sharedKey, it)
            navController.popBackStack()
        }
    )
}

@Composable
private fun EditInputContent(
    title: String?,
    hint: String?,
    text: String?,
    limit: Int,
    onBackClick: () -> Unit = {},
    onConfirmClick: (content: String) -> Unit = {},
) {
    var content by remember { mutableStateOf(text ?: "") }

    AppScaffold(
        title = {
            if (title != null) {
                Text(
                    text = title,
                    style = TextStyle(
                        fontSize = 17.sp,
                        lineHeight = 21.sp,
                        fontWeight = FontWeight.W700,
                        color = Color.White,
                    )
                )
            }
        },
        onBackClick = onBackClick,
        rightNavigationContent = {
            Text(
                text = stringResource(R.string.save),
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 20.sp,
                    fontWeight = FontWeight(400),
                    color = Color.White.copy(0.85f),
                    textAlign = TextAlign.Center,
                ),
                modifier = Modifier
                    .background(color = PurplePrimary, shape = RoundedCornerShape(20.dp))
                    .noRippleClickable(onClick = { onConfirmClick(content) })
                    .padding(horizontal = 16.dp, vertical = 5.dp)
            )
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) {
        Image(
            painter = painterResource(R.mipmap.ic_common_top_bg),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .fillMaxWidth()
        )

        AppTextField(
            value = content,
            onValueChange = { text -> content = text },
            modifier = Modifier
                .padding(horizontal = 20.dp)
                .padding(top = it.calculateTopPadding())
                .fillMaxWidth(),
            colors = WhiteTextFieldColors,
            limitNum = limit,
            placeholder = {
                Text(
                    text = hint ?: "",
                    style = TextStyle(
                        fontSize = 16.sp,
                        lineHeight = 28.sp,
                        fontWeight = FontWeight.W400,
                    )
                )
            }
        )
    }
}

@Preview
@Composable
private fun EditInputScreenPreview() {
    AppTheme {
        EditInputContent(
            title = "test",
            hint = "hint",
            text = "text",
            limit = 100,
        )
    }
}