package com.flutterup.app.screen.chat.component.message

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.TextBlack

@Composable
fun ChatMessageWithoutLimit(
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
) {
    var cardHeight by remember { mutableIntStateOf(0) }

    Box(modifier) {
        Column(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .fillMaxWidth()
                .wrapContentHeight()
                .border(1.dp, Color.White, RoundedCornerShape(20.dp))
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color(0xFFDFCDFF),
                            Color.White
                        ),
                        endY = cardHeight * 0.5f
                    ),
                    shape = RoundedCornerShape(20.dp)
                )
                .padding(horizontal = 20.dp, vertical = 12.dp)
                .onSizeChanged {
                    cardHeight = it.height
                },
            verticalArrangement = Arrangement.spacedBy(7.dp)
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(R.mipmap.ic_chat_without_limit_logo),
                    contentDescription = null,
                    modifier = Modifier.size(46.dp)
                )

                Column(verticalArrangement = Arrangement.spacedBy(3.dp)) {
                    Text(
                        text = stringResource(R.string.chat_without_limit_title),
                        style = TextStyle(
                            fontSize = 14.sp,
                            lineHeight = 22.sp,
                            fontWeight = FontWeight.W500,
                            color = TextBlack,
                        )
                    )

                    Text(
                        text = stringResource(R.string.chat_without_limit_desc),
                        style = TextStyle(
                            fontSize = 12.sp,
                            lineHeight = 22.sp,
                            fontWeight = FontWeight.W400,
                            color = TextBlack,
                        )
                    )
                }
            }

            Box(
                modifier = Modifier.fillMaxWidth().height(44.dp)
                    .noRippleClickable(onClick = onClick)
            ) {
                Image(
                    painter = painterResource(R.mipmap.ic_chat_without_limit_btn),
                    contentDescription = null,
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier.matchParentSize()
                )

                Text(
                    text = stringResource(R.string.chat_without_limit_btn),
                    style = TextStyle(
                        fontSize = 20.sp,
                        lineHeight = 22.sp,
                        fontWeight = FontWeight.W900,
                        color = Color(0xFF8F5F3C),
                    ),
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }
}

@Preview
@Composable
private fun ChatMessageWithoutLimitPreview() {
    ChatMessageWithoutLimit()
}