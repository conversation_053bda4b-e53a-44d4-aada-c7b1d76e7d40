package com.flutterup.app.screen.chat.vm

import com.flutterup.app.model.ChatBlockStatus
import com.flutterup.app.network.ApiService
import com.flutterup.app.utils.chat.ChatUserInfoCache
import com.flutterup.base.BaseRepository
import com.flutterup.base.utils.Timber
import com.flutterup.base.utils.resumeIfActive
import com.flutterup.base.utils.resumeWithExceptionIfActive
import com.flutterup.chat.core.ChatException
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Conversation.ConversationType
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject


class ChatConversationRepository @Inject constructor(
    private val apiService: ApiService,
) : BaseRepository() {

    private val count: Int = 10
    private val conversationTypes: Array<ConversationType> = arrayOf(ConversationType.PRIVATE)

    /**
     * 获取会话列表
     */
    suspend fun getConversations(timestamp: Long = 0L): List<Conversation>? = suspendCancellableCoroutine { cont ->
        RongCoreClient.getInstance().getConversationListByPage(object : IRongCoreCallback.ResultCallback<List<Conversation>>() {
            override fun onSuccess(t: List<Conversation>?) {
                //请求成功
                ChatUserInfoCache.fetchUserinfoList(t?.map { it.targetId }) //去更新数据
                cont.resumeIfActive(t)
            }

            override fun onError(e: IRongCoreEnum.CoreErrorCode?) {
                //请求失败
                cont.resumeWithExceptionIfActive(ChatException(e))
            }
        }, timestamp, count, *conversationTypes)
    }

    suspend fun getConversation(
        targetId: String,
        conversationType: ConversationType,
    ): Conversation? = suspendCancellableCoroutine { continuation ->
        RongCoreClient.getInstance().getConversation(
            conversationType,
            targetId,
            object : IRongCoreCallback.ResultCallback<Conversation>() {
                override fun onSuccess(t: Conversation?) {
                    continuation.resumeIfActive(t)
                }

                override fun onError(e: IRongCoreEnum.CoreErrorCode?) {
                    continuation.resumeWithExceptionIfActive(ChatException(e))
                }
            })
    }

    suspend fun blockOrUnblockUser(targetId: String, action: ChatBlockStatus): Boolean {
        return when (action) {
            ChatBlockStatus.Block -> blockOrUnblockFormBackend(targetId, action) && blockChat(targetId)
            ChatBlockStatus.Unblock -> blockOrUnblockFormBackend(targetId, action) && unblockChat(targetId)
        }
    }

    suspend fun blocked(targetId: String) = suspendCancellableCoroutine { cont ->
        RongIMClient.getInstance().getBlacklistStatus(targetId, object : RongIMClient.ResultCallback<RongIMClient.BlacklistStatus>() {
            override fun onSuccess(status: RongIMClient.BlacklistStatus?) {
                val blocked = status == RongIMClient.BlacklistStatus.IN_BLACK_LIST
                cont.resumeIfActive(blocked)
            }

            override fun onError(e: RongIMClient.ErrorCode?) {
                cont.resumeIfActive(false)
            }
        })
    }

    private suspend fun blockOrUnblockFormBackend(targetId: String, action: ChatBlockStatus): Boolean {
        val result = apiService.blockOrUnblockUser(targetId, action.value)
        result.message?.let {
            Timber.showToast(it)
        }
        return result.isSuccess
    }

    private suspend fun blockChat(targetId: String) = suspendCancellableCoroutine { cont ->
        RongIMClient.getInstance().addToBlacklist(targetId, object : RongIMClient.OperationCallback() {
            override fun onSuccess() {
                cont.resumeIfActive(true)
            }

            override fun onError(errorCode: RongIMClient.ErrorCode?) {
                cont.resumeIfActive(false)
            }
        })
    }

    private suspend fun unblockChat(targetId: String) = suspendCancellableCoroutine { cont ->
        RongIMClient.getInstance().removeFromBlacklist(targetId, object : RongIMClient.OperationCallback() {
            override fun onSuccess() {
                cont.resumeIfActive(true)
            }

            override fun onError(errorCode: RongIMClient.ErrorCode?) {
                cont.resumeIfActive(false)
            }
        })
    }
}