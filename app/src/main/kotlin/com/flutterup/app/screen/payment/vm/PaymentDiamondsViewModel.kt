package com.flutterup.app.screen.payment.vm

import com.flutterup.app.screen.payment.state.PaymentDiamondsUiState
import com.flutterup.base.BaseViewModel
import com.flutterup.base.utils.Timber
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class PaymentDiamondsViewModel @Inject constructor(

) : BaseViewModel() {

    /**
     * todo 替换成非测试代码
     */
    private val _uiState = MutableStateFlow(PaymentDiamondsUiState.TEST)

    val uiState = combine(
        _uiState,
        loadingState
    ) { ui, loading ->
        ui.copy(isLoading = loading)
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value.copy(isLoading = loadingState.value)
    )

    fun updateCurrentDiamondsPackId(id: String?) {
        _uiState.update { it.copy(currentDiamondsPackId = id) }
    }

    fun pay() {
        Timber.showToast("点击了购买, 商品ID：${uiState.value.currentDiamondsPackId}")
    }
}