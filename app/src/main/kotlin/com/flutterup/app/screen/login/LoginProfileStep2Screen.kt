@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.login

import android.app.Activity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.extension.rememberPickVisualMediaRequest
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.IndicatorTransparentTextFieldColors
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.model.Tag
import com.flutterup.app.screen.GlobalViewModel
import com.flutterup.app.screen.ImageCropperActivity
import com.flutterup.app.screen.login.content.LoginProfileInterests
import com.flutterup.app.screen.login.content.LoginProfilePhoto
import com.flutterup.app.screen.login.content.LoginProfileTextField
import com.flutterup.app.screen.login.state.LoginProfileStep2UIState
import com.flutterup.app.screen.login.vm.LoginProfileStep2ViewModel
import com.flutterup.app.screen.profile.state.ProfileInterestsUiState
import com.flutterup.app.screen.profile.vm.SharedProfileInterestsViewModel
import com.flutterup.base.utils.FileUtils
import java.io.File


@Composable
fun LoginProfileStep2Screen() {
    val context = LocalContext.current

    val viewModel: LoginProfileStep2ViewModel = hiltViewModel()
    val globalViewModel: GlobalViewModel = hiltViewModel()
    val sharedProfileInterestsViewModel: SharedProfileInterestsViewModel = hiltViewModel()

    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val profileUiState by sharedProfileInterestsViewModel.uiState.collectAsStateWithLifecycle()

    var occupiedIndex by remember { mutableIntStateOf(-1) }

    val launcher = rememberPickVisualMediaRequest { uri ->
        if (uri == null || occupiedIndex == -1) return@rememberPickVisualMediaRequest

        globalViewModel.uploadFileFromUri(
            uri = uri,
            onStart = {
                viewModel.startMediaLoading(occupiedIndex)
            },
            onSuccess = {
                viewModel.addMedia(occupiedIndex, it)
            },
            onComplete = {
                viewModel.endMediaLoading(occupiedIndex)
                occupiedIndex = -1
            }
        )
    }

    val cropperLauncher = rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        if (it.resultCode != Activity.RESULT_OK) {
            return@rememberLauncherForActivityResult
        }
        val data = it.data ?: return@rememberLauncherForActivityResult
        val output = ImageCropperActivity.getResult(data)
        val uri = File(output).toUri()

        globalViewModel.uploadFileFromUri(
            uri = uri,
            onStart = {
                viewModel.startMediaLoading(occupiedIndex)
            },
            onSuccess = { url ->
                viewModel.updateMedia(occupiedIndex, url)
            },
            onComplete = {
                viewModel.endMediaLoading(occupiedIndex)
                occupiedIndex = -1
            }
        )
    }

    LoginProfileStep2Content(
        uiState = uiState,
        profileUiState = profileUiState,
        onSkipClick = { viewModel.complete(true) },
        onContinueClick = { viewModel.complete(false) },
        onSignChange = viewModel::updateSign,
        onInterestsVisibleClick = viewModel::updateInterestsVisible,
        onInterestsChangeValue = viewModel::updateInterest,
        onItemClick = { index ->
            val item = uiState.mediaStatuses.getOrNull(index) ?: return@LoginProfileStep2Content

            if (item is LoginProfileStep2UIState.MediaStatus.Idle) { //无照片状态
                occupiedIndex = uiState.firstIdleIndex //更新index为最近的一个未设置照片

                launcher.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
            } else if (item is LoginProfileStep2UIState.MediaStatus.Success) { //已经有照片
                occupiedIndex = index //更新index为当前点击的index

                globalViewModel.downloadToFile(
                    url = item.url,
                    fileName = FileUtils.generateFileName(FileUtils.IMAGE_SUFFIX),
                    onSuccess = {
                        cropperLauncher.launch(ImageCropperActivity.createInstance(context, it.absolutePath))
                    }
                )
            }
        },
    )
}

@Composable
private fun LoginProfileStep2Content(
    uiState: LoginProfileStep2UIState,
    profileUiState: ProfileInterestsUiState,
    onBackClick: () -> Unit = {},
    onSkipClick: () -> Unit = {},
    onSignChange: (String) -> Unit = {},
    onInterestsVisibleClick: () -> Unit = {},
    onInterestsChangeValue: (isSelected: Boolean, tag: Tag) -> Unit = { _, _ -> },
    onContinueClick: () -> Unit = {},
    onItemClick: (index: Int) -> Unit = {},
) {
    AppScaffold(
        title = { },
        canGoBack = false, //不允许返回
        onBackClick = onBackClick,
        modifier = Modifier.fillMaxSize(),
        rightNavigationContent = {
            LoginProfileStep2Skip(
                modifier = Modifier.width(50.dp).height(25.dp),
                onClick = onSkipClick
            )
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_common_top_bg),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.TopCenter)
            )

            Column(Modifier.fillMaxSize()) {
                Column (Modifier.weight(1f).verticalScroll(rememberScrollState())) {
                    Spacer(
                        modifier = Modifier.padding(top = it.calculateTopPadding())
                    )

                    LoginProfilePhoto(
                        medias = uiState.mediaStatuses,
                        padding = PaddingValues(horizontal = 16.dp),
                        onClick = onItemClick
                    )

                    Spacer(modifier = Modifier.height(10.dp))

                    LoginProfileTextField(
                        value = uiState.sign,
                        onValueChange = onSignChange,
                        placeholder = R.string.profile_introduction_label,
                        enabled = true,
                        limitNum = 500,
                        colors = IndicatorTransparentTextFieldColors,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 20.dp),
                        labelContent = {
                            Text(
                                text = stringResource(R.string.profile_introduction_label),
                                style = TextStyle(
                                    fontSize = 16.sp,
                                    lineHeight = 28.sp,
                                    fontWeight = FontWeight.W500,
                                    color = Color.Black,
                                )
                            )
                        }
                    )

                    Spacer(modifier = Modifier.height(20.dp))

                    LoginProfileStep2InterestContent(
                        uiState = uiState,
                        interestsUiState = profileUiState,
                        onInterestsVisibleClick = onInterestsVisibleClick,
                        onInterestsChangeValue = onInterestsChangeValue
                    )

                    Spacer(modifier = Modifier.height(40.dp))
                }


                AppContinueButton(
                    enabled = true, //默认就可以结束进行下一步
                    isLoading = uiState.isLoading,
                    onClick = onContinueClick,
                    text = stringResource(R.string.finish),
                    modifier = Modifier
                        .padding(bottom = 70.dp)
                        .fillMaxWidth()
                        .height(50.dp)
                        .padding(horizontal = 24.dp)
                )
            }
        }
    }
}

@Composable
private fun LoginProfileStep2Skip(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    OutlinedButton(
        onClick = onClick,
        modifier = modifier.fillMaxSize().wrapContentHeight().noRippleClickable(onClick = onClick),
        shape = RoundedCornerShape(30.dp),
        border = BorderStroke(1.dp, PurplePrimary),
        contentPadding = PaddingValues(top = 0.dp, bottom = 2.dp),
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = Color.Transparent,
            contentColor = PurplePrimary,
            disabledContainerColor = Color.Transparent,
            disabledContentColor = PurplePrimary,
        )
    ) {
        Text(
            text = stringResource(R.string.skip),
            style = TextStyle(
                fontSize = 14.sp,
                lineHeight = 22.sp,
                fontWeight = FontWeight.W400,
                color = PurplePrimary,
                textAlign = TextAlign.Center,
            )
        )
    }
}

@Composable
private fun LoginProfileStep2InterestContent(
    uiState: LoginProfileStep2UIState,
    interestsUiState: ProfileInterestsUiState,
    onInterestsVisibleClick: () -> Unit,
    onInterestsChangeValue: (isSelected: Boolean, tag: Tag) -> Unit
) {
    LoginProfileInterests(
        modifier = Modifier.fillMaxWidth().padding(horizontal = 20.dp),
        visible = uiState.interestListVisible,
        selectedInterests = uiState.interests,
        interests = interestsUiState.interests,
        onVisibleClick = onInterestsVisibleClick,
        onInterestClick = onInterestsChangeValue,
    )
}


@Preview
@Composable
private fun LoginProfileStep2Preview() {
    AppTheme {
        LoginProfileStep2Content(
            uiState = LoginProfileStep2UIState(),
            profileUiState = ProfileInterestsUiState()
        )
    }
}