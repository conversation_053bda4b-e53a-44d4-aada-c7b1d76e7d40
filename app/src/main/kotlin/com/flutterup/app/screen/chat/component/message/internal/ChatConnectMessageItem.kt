package com.flutterup.app.screen.chat.component.message.internal

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.model.UserInfo


@Composable
fun ChatConnectMessageItem(
    mine: UserInfo?,
    other: UserInfo?,
    modifier: Modifier = Modifier
) {

    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            modifier = Modifier.padding(top = 30.dp)
        ) {
            AsyncImage(
                model = other?.headImage,
                contentDescription = null,
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .size(60.dp)
                    .clip(CircleShape)
                    .zIndex(0f)
            )

            AsyncImage(
                model = mine?.headImage,
                contentDescription = null,
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .offset(x = (-15).dp)
                    .border(1.dp, Color.White, CircleShape)
                    .padding(1.dp)
                    .size(60.dp)
                    .clip(CircleShape)
            )
        }

        Row(
            modifier = Modifier.padding(top = 10.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_chat_connect),
                contentDescription = null,
                modifier = Modifier.size(45.dp, 15.5.dp)
            )

            Box {
                Text(
                    text = stringResource(R.string.wink_match),
                    style = TextStyle(
                        fontSize = 26.sp,
                        lineHeight = 26.sp,
                        fontWeight = FontWeight.W900,
                        fontStyle = FontStyle.Italic,
                        brush = Brush.linearGradient(
                            colors = listOf(
                                Color.White,
                                Color(0xFFFFE0FC),
                                Color.White
                            )
                        ),
                        drawStyle = Stroke(with(LocalDensity.current) { 2.dp.toPx() })
                    )
                )

                Text(
                    text = stringResource(R.string.wink_match),
                    style = TextStyle(
                        fontSize = 26.sp,
                        lineHeight = 26.sp,
                        fontWeight = FontWeight.W900,
                        fontStyle = FontStyle.Italic,
                        brush = Brush.linearGradient(
                            colors = listOf(
                                Color(0xFF7C4AB6),
                                Color(0xFFB648B7)
                            )
                        ),
                    )
                )
            }

            Image(
                painter = painterResource(R.mipmap.ic_chat_connect),
                contentDescription = null,
                modifier = Modifier.size(45.dp, 15.5.dp).rotate(180f)
            )
        }
    }
}


@Preview
@Composable
private fun ChatConnectMessageItemPreview() {
    val mine = UserInfo(userId = "1")
    val other = UserInfo(userId = "2")

    Surface {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            ChatConnectMessageItem(mine, other, Modifier.align(Alignment.CenterHorizontally))
        }
    }
}