package com.flutterup.app.screen.chat.vm

import android.content.Context
import androidx.lifecycle.viewModelScope
import coil3.SingletonImageLoader
import coil3.request.ImageRequest
import com.flutterup.app.screen.chat.state.ChatGiftUiState
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseViewModel
import com.flutterup.base.utils.JsonUtils
import com.flutterup.base.utils.Timber
import com.flutterup.chat.message.content.GiftMessageContent
import com.flutterup.gifts.GiftPreloadManager
import com.flutterup.gifts.entity.GiftEntity
import com.flutterup.gifts.entity.GiftResourceInfo
import com.flutterup.gifts.entity.PreloadProgress
import com.flutterup.gifts.entity.PreloadResult
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ChatGiftViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userMonitor: UserMonitor,
    private val giftPreloadManager: GiftPreloadManager,
    private val giftRepository: ChatGiftRepository,
) : BaseViewModel() {

    companion object {
        private const val TAG = "ChatGiftViewModel"
    }

    private var giftList: List<GiftResourceInfo>? = null

    private val _uiState = MutableStateFlow(ChatGiftUiState())

    private val giftUiState: StateFlow<GiftUIState> = giftPreloadManager.preloadProgress
        .map { progress ->
            when (progress) {
                is PreloadProgress.Idle -> GiftUIState.Idle
                is PreloadProgress.InProgress -> GiftUIState.Preloading(progress)
                is PreloadProgress.Completed -> when (val result = progress.result) {
                    is PreloadResult.Success -> GiftUIState.Success(result)
                    is PreloadResult.PartialSuccess -> GiftUIState.PartialSuccess
                    is PreloadResult.Error -> GiftUIState.Error
                }
            }
        }.stateIn(
            scope,
            SharingStarted.Eagerly,
            GiftUIState.Idle
        )


    val uiState: StateFlow<ChatGiftUiState> = combine(
        _uiState,
        giftUiState,
    ) { ui, giftUiState ->
        ui.copy(giftUiState = giftUiState)
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value
    )

    init {
        initGifts()
    }

    private fun initGifts() {
        viewModelScope.launch {
            if (!userMonitor.isLogin) return@launch

            try {
                giftList = giftRepository.getGiftList()

                giftList?.let {
                    giftPreloadManager.preloadGifts(it, onComplete = ::coilPreloadGiftResource)
                }
            } catch (e: Exception) {
                //不抛异常，静默处理
                Timber.d(TAG, "initGifts error", e)
            }
        }
    }

    fun reloadGifts() {
        if (giftPreloadManager.isPreloading()) {
            Timber.d(TAG, "reloadGifts: isPreloading=true, return")
            return //正在预加载中，直接返回
        }
        initGifts()
    }

    fun updateCurrentGift(gift: GiftEntity) {
        _uiState.update { it.copy(currentGift = gift) }
    }

    fun getGiftImage(messageContent: GiftMessageContent): GiftMessageData {
        val data = messageContent.get(GiftResourceInfo::class.java) ?: return GiftMessageData.EMPTY

        val cacheResult = (uiState.value.giftUiState as? GiftUIState.Success) ?: return GiftMessageData.EMPTY
        val cacheGift = cacheResult.result.cachedGiftsData.firstOrNull { it.giftId == data.giftId }

        return GiftMessageData(
            name = cacheGift?.name ?: data.name.orEmpty(),
            image = cacheGift?.imagePath ?: data.image.orEmpty(),
            gif = cacheGift?.gifPath ?: data.gif.orEmpty(),
            video = cacheGift?.videoPath ?: data.video.orEmpty(),
        )
    }

    private fun coilPreloadGiftResource(result: PreloadResult) {
        if (!result.isCompleteSuccess) return //预加载失败返回

        val imageLoader = SingletonImageLoader.get(context)

        result.cachedGiftsData.forEach {
            val imageRequest = ImageRequest.Builder(context)
                .data(it.imagePath)
                .build()

            val gifRequest = ImageRequest.Builder(context)
                .data(it.gifPath)
                .build()

            imageLoader.enqueue(imageRequest)
            imageLoader.enqueue(gifRequest)
        }
    }
}

sealed interface GiftUIState {
    data object Idle : GiftUIState

    data class Preloading(val progress: PreloadProgress.InProgress) : GiftUIState

    data object PartialSuccess : GiftUIState

    data class Success(val result: PreloadResult.Success) : GiftUIState

    data object Error : GiftUIState
}

data class GiftMessageData(
    val name: String,
    val image: String,
    val gif: String,
    val video: String,
) {

    companion object {
        val EMPTY = GiftMessageData("", "", "", "")
    }
}