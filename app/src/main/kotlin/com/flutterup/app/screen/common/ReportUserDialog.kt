@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.design.component.AppBottomSheetDialog
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.insets.safeArea
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.TextBlack666
import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.ReportItem
import com.flutterup.app.model.UserActionType
import com.flutterup.app.screen.common.state.ReportUiState
import com.flutterup.app.screen.common.vm.ProfileSharedViewModel
import com.flutterup.app.screen.common.vm.ReportViewModel


@Composable
fun ReportUserDialog(
    targetUserId: String,
    from: AppFrom = AppFrom.Unknown,
    sharedViewModel: ProfileSharedViewModel? = null,
    onCancelClick: () -> Unit = {},
) {
    val viewModel = hiltViewModel<ReportViewModel>()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    AppBottomSheetDialog(
        isShown = true,
        onDismissRequest = onCancelClick,
        onConfirmRequest = {},
        sheetState = sheetState,
        cancelText = null,
        confirmText = null,
        modifier = Modifier.windowInsetsPadding(WindowInsets.safeArea)
    ) {
        ReportUserDialogContent(
            uiState = uiState,
            onCancelClick = onCancelClick,
            onReportClick = {
                viewModel.report(targetUserId, from, it) {
                    sharedViewModel?.sendActionEvent(targetUserId, UserActionType.Dislike)
                }
            },
        )
    }
}

@Composable
private fun ReportUserDialogContent(
    uiState: ReportUiState,
    onCancelClick: () -> Unit = {},
    onReportClick: (ReportItem) -> Unit = {},
) {
    Box(
        modifier = Modifier
            .background(Color.White, RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
            .fillMaxWidth()
            .clip(RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
            .padding(bottom = 33.dp)
    ) {
        Column {
            Text(
                text = stringResource(R.string.report_title),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight.W500,
                    color = TextBlack666,
                    textAlign = TextAlign.Center,
                ),
                modifier = Modifier
                    .padding(top = 30.dp)
                    .padding(horizontal = 20.dp)
                    .fillMaxWidth()
            )

            Spacer(Modifier.height(23.dp))

            if (uiState.reportList.isNotEmpty()) {
                LazyRow (
                    modifier = Modifier.fillMaxWidth().padding(start = 16.dp, end = 13.dp),
                    horizontalArrangement = Arrangement.spacedBy(14.dp)
                ) {
                    itemsIndexed(uiState.reportList) { index, item ->
                        val resourceIndex = index % 3
                        val backgroundColor = BrushList[resourceIndex]
                        val iconResource = IconResourceList[resourceIndex]

                        ReportItemContent(
                            backgroundColor = backgroundColor,
                            iconResource = iconResource,
                            item = item,
                            onReportClick = onReportClick
                        )
                    }
                }
            }

            if (uiState.reported) {
                Spacer(Modifier.height(16.dp))

                Text(
                    text = stringResource(R.string.report_thx),
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W500,
                        color = TextBlack666,
                    ),
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
            }

            AppContinueButton(
                onClick = onCancelClick,
                enabled = true,
                isLoading = false,
                text = stringResource(R.string.cancel),
                modifier = Modifier
                    .padding(horizontal = 24.dp)
                    .padding(top = 37.dp)
                    .fillMaxWidth()
                    .height(50.dp)
            )
        }
    }
}

@Composable
private fun ReportItemContent(
    backgroundColor: Brush,
    iconResource: Int,
    item: ReportItem,
    onReportClick: (ReportItem) -> Unit,
) {
    Column(
        modifier = Modifier
            .size(106.dp, 104.dp)
            .background(CardBackgroundColor, RoundedCornerShape(16.dp))
            .noRippleClickable(onClick = { onReportClick(item) }),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .background(backgroundColor, RoundedCornerShape(14.dp))
                .size(53.dp)
        ) {
            Box(
                modifier = Modifier
                    .background(
                        brush = Brush.verticalGradient(listOf(Color.White.copy(0.3f), Color.White)),
                        shape = RoundedCornerShape(4.dp)
                    )
                    .size(23.dp, 31.dp)
                    .align(Alignment.Center),
                contentAlignment = Alignment.Center,
            ) {
                Image(
                    painter = painterResource(iconResource),
                    contentDescription = null,
                )
            }
        }

        Spacer(Modifier.height(7.dp))

        Text(
            text = item.title.orEmpty(),
            style = TextStyle(
                fontSize = 12.sp,
                fontWeight = FontWeight.W500,
                color = TextBlack666,
                textAlign = TextAlign.Center
            ),
        )
    }
}

private val CardBackgroundColor = Color(0xFFF3F0FF)

private val BrushList = listOf(
    Brush.verticalGradient(listOf(Color(0xFF796ADE), Color(0xFFBAAFFF))),
    Brush.verticalGradient(listOf(Color(0xFFCF77E6), Color(0xFFEDC9FF))),
    Brush.verticalGradient(listOf(Color(0xFFDBA47D), Color(0xFFFFF5CB))),
)

private val TransparentBrush = Brush.verticalGradient(listOf(Color.White.copy(0.3f), Color.White))

private val IconResourceList = listOf(
    R.mipmap.ic_report_1,
    R.mipmap.ic_report_2,
    R.mipmap.ic_report_3
)


@Preview
@Composable
private fun ReportUserDialogPreview() {
    Surface {
        Box(
            modifier = Modifier.fillMaxSize().background(color = Color.Gray),
            contentAlignment = Alignment.BottomCenter
        ) {
            ReportUserDialogContent(
                uiState = ReportUiState(
                    reportList = List(3) {
                        ReportItem(
                            title = "test$it",
                            id = it.toLong(),
                        )
                    },
                    reported = true
                )
            )
        }
    }
}