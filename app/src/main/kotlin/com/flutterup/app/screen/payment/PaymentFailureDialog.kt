@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.payment

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.component.AppBottomSheetDialog
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.insets.safeArea
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.TextBlack
import com.flutterup.app.design.theme.TextBlack666

@Composable
fun PaymentFailureDialog(
    onChangeAccountClick: () -> Unit = {},
    onChangeGooglePlayClick: () -> Unit = {},
    onCancelClick: () -> Unit = {},
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    AppBottomSheetDialog(
        isShown = true,
        onDismissRequest = onCancelClick,
        onConfirmRequest = {},
        sheetState = sheetState,
        cancelText = null,
        confirmText = null,
        modifier = Modifier.windowInsetsPadding(WindowInsets.safeArea)
    ) {
        PaymentFailureDialogContent(
            onChangeAccountClick = onChangeAccountClick,
            onChangeGooglePlayClick = onChangeGooglePlayClick,
        )
    }
}

@Composable
private fun PaymentFailureDialogContent(
    onChangeAccountClick: () -> Unit = {},
    onChangeGooglePlayClick: () -> Unit = {},
) {

    Box(
        modifier = Modifier
            .background(Color.White, RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
            .fillMaxWidth()
            .clip(RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
            .padding(bottom = 33.dp)
    ) {
        Column {
            Text(
                text = stringResource(R.string.repeat_account_title),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight.W500,
                    color = TextBlack666,
                    textAlign = TextAlign.Center,
                ),
                modifier = Modifier
                    .padding(top = 30.dp)
                    .padding(horizontal = 20.dp)
                    .fillMaxWidth()
            )

            Spacer(Modifier.height(23.dp))

            Row(
                modifier = Modifier.fillMaxWidth().padding(start = 18.dp, end = 14.dp),
                horizontalArrangement = Arrangement.spacedBy(7.dp)
            ) {
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .height(82.dp)
                        .background(BackgroundColor, RoundedCornerShape(16.dp))
                        .padding(vertical = 11.dp, horizontal = 10.dp)
                        .noRippleClickable(onClick = onChangeAccountClick),
                    verticalArrangement = Arrangement.spacedBy(6.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {

                    Text(
                        text = stringResource(R.string.switch_account_title),
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight.W400,
                            color = TextBlack,
                        )
                    )

                    Text(
                        text = stringResource(R.string.switch_account_desc),
                        style = TextStyle(
                            fontSize = 10.sp,
                            fontWeight = FontWeight.W300,
                            color = TextBlack,
                            textAlign = TextAlign.Center,
                        )
                    )
                }

                Column(
                    modifier = Modifier
                        .weight(1f)
                        .height(82.dp)
                        .background(BackgroundColor, RoundedCornerShape(16.dp))
                        .padding(vertical = 11.dp, horizontal = 10.dp)
                        .noRippleClickable(onClick = onChangeGooglePlayClick),
                    verticalArrangement = Arrangement.spacedBy(6.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {

                    Text(
                        text = stringResource(R.string.switch_payment_title),
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight.W400,
                            color = TextBlack,
                        )
                    )

                    Text(
                        text = stringResource(R.string.switch_payment_desc),
                        style = TextStyle(
                            fontSize = 10.sp,
                            fontWeight = FontWeight.W300,
                            color = TextBlack,
                            textAlign = TextAlign.Center,
                        )
                    )
                }
            }
        }
    }
}

private val BackgroundColor = Color(0xFFF3F0FF)

@Preview
@Composable
private fun PaymentFailureDialogPreview() {
    Surface {
        Box(
            modifier = Modifier.fillMaxSize().background(color = Color.Gray),
            contentAlignment = Alignment.BottomCenter
        ) {
            PaymentFailureDialogContent(

            )
        }
    }
}