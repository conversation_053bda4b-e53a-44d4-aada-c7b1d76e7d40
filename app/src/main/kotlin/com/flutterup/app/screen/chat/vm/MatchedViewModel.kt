package com.flutterup.app.screen.chat.vm

import com.flutterup.app.screen.chat.state.MatchedUiState
import com.flutterup.app.screen.profile.vm.OtherProfileRepository
import com.flutterup.app.utils.GlobalSettingsMonitor
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MatchedViewModel @Inject constructor(
    private val chatMatchRepository: ChatMatchRepository,
    private val otherProfileRepository: OtherProfileRepository,
    private val userMonitor: UserMonitor,
    private val settingsMonitor: GlobalSettingsMonitor,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(MatchedUiState())

    val uiState: StateFlow<MatchedUiState> = combine(
        _uiState,
        userMonitor.userInfoState,
        settingsMonitor.greetingList,
    ) { ui, userinfo, greetingList ->
        ui.copy(
            userinfo = userinfo,
            greetingList = greetingList.orEmpty(),
        )
    }.stateIn(
        scope,
        SharingStarted.Eagerly,
        _uiState.value
    )

    fun init(targetId: String) {
        scope.launch {
            val userinfo = otherProfileRepository.getOtherProfileInfo(targetId)
            _uiState.update { it.copy(anotherUserinfo = userinfo) }
        }
    }

    fun sendGreeting(greeting: String, onSuccess: () -> Unit = {}) {
        val userId = uiState.value.anotherUserinfo?.userId ?: return

        scope.launchWithLoading {
            val isSuccess = chatMatchRepository.sendGreetingMessage(
                userId = userId,
                message = greeting,
            )

            if (isSuccess) {
                onSuccess()
            }
        }
    }
}