package com.flutterup.app.screen.payment.state

import com.flutterup.billinghelper.model.PaymentDiamondsItem
import com.flutterup.base.BaseState
import org.jetbrains.annotations.TestOnly

data class PaymentDiamondsUiState(
    val diamonds: Float = 0.0f,

    val currentDiamondsPackId: String? = null,

    val diamondsPacks: List<PaymentDiamondsItem> = emptyList(),

    val description1: String? = null,

    val description2: String? = null,

    override val isLoading: Boolean = false,
) : BaseState {


    companion object {

        @TestOnly
        val TEST = PaymentDiamondsUiState(
            diamonds = 100f,
            currentDiamondsPackId = "1",
            diamondsPacks = listOf(
                PaymentDiamondsItem(
                    id = "1",
                    diamond = 40f,
                    name = "Mini Pack",
                    price = "3.99",
                    unit = "USD",
                    prodId = "1",
                    icon = "",
                    hot = 0,
                    popular = 0,
                ),
                PaymentDiamondsItem(
                    id = "2",
                    diamond = 110f,
                    name = "Basic Pack",
                    price = "9.99",
                    unit = "USD",
                    prodId = "2",
                    icon = "",
                    hot = 0,
                    popular = 0,
                ),
                PaymentDiamondsItem(
                    id = "3",
                    diamond = 240f,
                    name = "Standard Pack",
                    price = "19.99",
                    unit = "USD",
                    prodId = "3",
                    icon = "",
                    hot = 0,
                    popular = 1,
                ),
                PaymentDiamondsItem(
                    id = "4",
                    diamond = 520f,
                    name = "Plus Pack",
                    price = "39.99",
                    unit = "USD",
                    prodId = "4",
                    icon = "",
                    hot = 1,
                    popular = 0,
                ),
                PaymentDiamondsItem(
                    id = "5",
                    diamond = 850f,
                    name = "Premium Pack",
                    subName = "The more you buy\nThe more you get free",
                    price = "59.99",
                    unit = "USD",
                    prodId = "5",
                    icon = "",
                    hot = 0,
                    popular = 0,
                ),
                PaymentDiamondsItem(
                    id = "6",
                    diamond = 1500f,
                    name = "Ultimate Pack",
                    price = "99.99",
                    unit = "USD",
                    prodId = "6",
                    icon = "",
                    hot = 0,
                    popular = 0,
                ),
            ),
            description1 = "Feel free to cancel your subscription whenever you like.Your subscription fee will be charged to your Google Play account and will auto-renew 24 hours before the current period ends. You can disable auto-renewal anytime in your Google Play account settings. No refunds are available once the subscription is active, ",
            description2 = "Feel free to cancel your subscription whenever you like.Your subscription fee will be charged to your Google Play account and will auto-renew 24 hours before the current period ends. You can disable auto-renewal anytime in your Google Play account settings. No refunds are available once the subscription is active, "
        )
    }
}