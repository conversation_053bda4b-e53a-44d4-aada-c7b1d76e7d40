@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.chat

import androidx.activity.result.PickVisualMediaRequest
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyGridItemScope
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.AppDefaultNavigationIcon
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.component.AppLoadMoreIndicator
import com.flutterup.app.design.component.AppRadioButton
import com.flutterup.app.design.extension.rememberPickVisualMediaRequest
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.LabelBackgroundPrimary
import com.flutterup.app.design.theme.LinePrimary
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.chat.state.ChatPrivateMomentsUiState
import com.flutterup.app.screen.chat.vm.ChatMessageViewModel
import com.flutterup.app.screen.chat.vm.ChatPrivateMomentsViewModel
import com.flutterup.base.compose.refresh.RefreshLoadMoreBox
import com.flutterup.base.compose.refresh.rememberPullToLoadMoreState


@Composable
fun ChatPrivateMomentsScreen(sharedViewModel: ChatMessageViewModel, ) {
    val navController = LocalNavController.current

    val viewModel: ChatPrivateMomentsViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    val launcher = rememberPickVisualMediaRequest { uri ->
        if (uri == null) return@rememberPickVisualMediaRequest
        viewModel.add(uri)
    }

    ChatPrivateMomentsContent(
        uiState = uiState,
        onRefresh = { viewModel.refresh() },
        onLoadMore = { viewModel.loadMore() },
        onBackClick = navController::popBackStack,
        onDeleteClick = { viewModel.delete() },
        onAddClick = {
            launcher.launch(PickVisualMediaRequest())
        },
        onMediaItemClick = { newSelectedValue, item ->
            viewModel.updateSelectedMedia(newSelectedValue, item)
        },
        onMediaSendClick = {
            sharedViewModel.sendPrivateMoments(uiState.selectedMediaList)
            navController.popBackStack()
        }
    )
}

@Composable
private fun ChatPrivateMomentsContent(
    uiState: ChatPrivateMomentsUiState,
    onRefresh: () -> Unit = {},
    onLoadMore: () -> Unit = {},
    onBackClick: () -> Unit = {},
    onDeleteClick: () -> Unit = {},
    onAddClick: () -> Unit = {},
    onMediaItemClick: (Boolean, MediaItemEntity) -> Unit = { _, _ -> },
    onMediaSendClick: () -> Unit = {},
) {
    val pullRefreshState = rememberPullToRefreshState()
    val pullToLoadMoreState = rememberPullToLoadMoreState()

    AppScaffold(
        title = { },
        navigation = {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                AppDefaultNavigationIcon(canGoBack = true, onBackClick = onBackClick)

                Text(
                    text = stringResource(R.string.chat_private_moments),
                    style = TextStyle(
                        fontSize = 17.sp,
                        lineHeight = 21.sp,
                        fontWeight = FontWeight.W800,
                        fontStyle = FontStyle.Italic,
                        color = Color.White,
                    )
                )
            }
        },
        rightNavigationContent = {
            if (uiState.isDeleting) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    strokeWidth = 2.dp,
                    color = LinePrimary,
                )
            } else {
                Icon(
                    painter = painterResource(R.drawable.ic_purple_delete),
                    contentDescription = null,
                    tint = Color.Unspecified,
                    modifier = Modifier.noRippleClickable(onClick = onDeleteClick)
                )
            }
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) { paddingValues ->
        Image(
            painter = painterResource(R.mipmap.ic_common_top_bg),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier.fillMaxWidth()
        )

        Column(
            modifier = Modifier
                .padding(top = paddingValues.calculateTopPadding())
                .consumeWindowInsets(paddingValues)
                .fillMaxSize()
        ) {
            RefreshLoadMoreBox(
                isRefreshing = uiState.isRefreshing,
                isLoadingMore = uiState.isLoadingMore,
                hasNoMoreData = uiState.hasNoMoreData,
                onRefresh = onRefresh,
                onLoadMore = onLoadMore,
                pullRefreshState = pullRefreshState,
                pullLoadMoreState = pullToLoadMoreState,
                loadMoreIndicator = {
                    AppLoadMoreIndicator(
                        isLoadingMore = uiState.isLoadingMore,
                        hasNoMoreData = uiState.hasNoMoreData,
                        state = pullToLoadMoreState
                    )
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                LazyVerticalGrid(
                    columns = GridCells.Fixed(3),
                    horizontalArrangement = Arrangement.spacedBy(10.dp),
                    verticalArrangement = Arrangement.spacedBy(10.dp),
                    contentPadding = PaddingValues(horizontal = 25.dp)
                ) {
                    item {
                        AddBox(
                            uiState = uiState,
                            onClick = onAddClick
                        )
                    }

                    items(uiState.mediaList, key = { it.id ?: it.url }) {
                        MediaItem(
                            item = it,
                            isSelected = uiState.selectedMediaList.any { media -> it.id == media.id },
                            canSelectMore = uiState.canSelectedMore,
                            currentSelectedType = uiState.currentSelectedType,
                            onClick = onMediaItemClick,
                        )
                    }
                }
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(78.dp)
                    .padding(bottom = paddingValues.calculateBottomPadding())
                    .background(Color.White, RoundedCornerShape(topStart = 25.dp, topEnd = 25.dp))
                    .padding(horizontal = 16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = stringResource(R.string.private_moments_selected_sub, uiState.selectedImageCount, uiState.selectedVideoCount),
                    style = TextStyle(
                        fontSize = 12.sp,
                        lineHeight = 16.sp,
                        fontWeight = FontWeight.W400,
                        color = Color(0xCC1A1A1A),
                    )
                )

                AppContinueButton(
                    onClick = onMediaSendClick,
                    enabled = uiState.selectedMediaList.isNotEmpty(),
                    isLoading = uiState.isLoading,
                    modifier = Modifier.size(78.dp, 44.dp),
                    contentPadding = PaddingValues(0.dp),
                    content = {
                        Icon(
                            painter = painterResource(R.drawable.ic_upload),
                            contentDescription = null,
                            tint = Color.Unspecified
                        )
                    }
                )
            }
        }
    }
}

@Composable
private fun AddBox(
    uiState: ChatPrivateMomentsUiState,
    onClick: () -> Unit = {},
) {
    Box(
        modifier = Modifier
            .border(1.dp, LabelBackgroundPrimary, RoundedCornerShape(8.dp))
            .fillMaxWidth()
            .aspectRatio(3f / 4f)
            .noRippleClickable(
                enabled = !uiState.isUploading,
                onClick = onClick
            )
    ) {
        Box(
            modifier = Modifier
                .background(
                    brush = Brush.linearGradient(
                        colors = listOf(
                            Color(0x33F28FFF),
                            Color.White.copy(0.12f),
                            Color.White.copy(0.08f)
                        )
                    ),
                    shape = RoundedCornerShape(8.dp)
                )
                .matchParentSize()
        )

        if (uiState.isUploading) {
            CircularProgressIndicator(
                modifier = Modifier.size(24.dp).align(Alignment.Center),
                strokeWidth = 2.dp,
                color = PurplePrimary,
            )
        } else {
            Icon(
                painter = painterResource(R.drawable.ic_chat_input_more),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}

@Composable
private fun LazyGridItemScope.MediaItem(
    item: MediaItemEntity,
    isSelected: Boolean,
    canSelectMore: Boolean,
    currentSelectedType: Int?,
    onClick: (Boolean, MediaItemEntity) -> Unit = { _, _ -> }
) {

    /**
     * 满足2个条件
     * 可以选中更多 or 是选中状态的
     * 当前选中类型和媒体类型一致 or 当前选中媒体类型为空
     */
    val enable = (canSelectMore || isSelected)
            && (currentSelectedType == null || currentSelectedType == item.type)

    Box(
        modifier = Modifier
            .animateItem()
            .noRippleClickable(
                enabled = enable,
                onClick = { onClick(!isSelected, item) }
            )
            .fillMaxWidth()
            .aspectRatio(3f / 4f)
    ) {
        AsyncImage(
            model = item.imageUrl,
            contentDescription = null,
            contentScale = ContentScale.FillBounds,
            modifier = Modifier
                .matchParentSize()
                .clip(RoundedCornerShape(8.dp))
        )

        if (!enable) {
            Box(
                modifier = Modifier
                    .matchParentSize()
                    .background(Color.Black.copy(0.3f), RoundedCornerShape(8.dp))
            )
        }

        AppRadioButton(
            selected = isSelected,
            modifier = Modifier
                .padding(top = 4.dp, end = 4.dp)
                .size(16.dp)
                .align(Alignment.TopEnd),
            onSelectedChange = {},
            tint = Color.White.copy(0.45f),
            tintSelected = Color.Unspecified
        )

        if (item.type == MediaItemEntity.TYPE_VIDEO) {
            Icon(
                painter = painterResource(R.drawable.ic_media_video),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier
                    .padding(top = 5.dp, start = 5.dp)
                    .align(Alignment.TopStart)
            )
        }
    }
}

@Preview
@Composable
private fun ChatPrivateMomentsScreenPreview() {
    AppTheme {
        ChatPrivateMomentsContent(
            uiState = ChatPrivateMomentsUiState(
                mediaList = List(20) {
                    MediaItemEntity(
                        id = it.toLong(),
                        type = (0..1).random(),
                        url = ""
                    )
                }
            )
        )
    }
}