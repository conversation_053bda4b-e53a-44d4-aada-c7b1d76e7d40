package com.flutterup.app.screen.login.state

import android.util.Patterns
import com.flutterup.base.BaseState

data class LoginEmailUIState(
    val email: String = "",
    val password: String = "",
    val isPasswordVisible: <PERSON><PERSON><PERSON> = false, //密码是否可见
    override val isLoading: <PERSON>olean = false,
) : BaseState {

    val isEmailValid: <PERSON>olean
        get() = isEmailValid(email)

    val isPasswordValid: Boolean
        get() = isPasswordValid(password)

    val isLoginEnabled: <PERSON><PERSON><PERSON>
        get() = isEmailValid && isPasswordValid && email.isNotEmpty() && password.isNotEmpty()


    /**
     * 检查邮箱格式是否正确
     */
    private fun isEmailValid(email: String): Bo<PERSON>an {
        if (email.isEmpty()) return true

        return Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }

    /**
     * 检查密码格式是否正确
     */
    private fun isPasswordValid(password: String): <PERSON><PERSON><PERSON> {
        if (password.isEmpty()) return true

        return password.length >= 8 && password.any { it.isLetter() } && password.any { it.isDigit() }
    }
}