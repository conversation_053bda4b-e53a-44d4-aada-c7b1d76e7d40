package com.flutterup.app.screen.chat.vm

import android.content.Context
import android.net.Uri
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.network.ApiService
import com.flutterup.app.screen.GlobalRepository
import com.flutterup.base.BaseRepository
import com.flutterup.base.utils.MimeType.*
import com.flutterup.base.utils.UriUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext
import javax.inject.Inject

class ChatPrivateMomentsRepository @Inject constructor(
    @ApplicationContext private val context: Context,
    private val apiService: ApiService,
    private val globalRepository: GlobalRepository
) : BaseRepository() {

    suspend fun getPrivateMoments(
        lastId: Long? = null,
        from: MomentsFrom = MomentsFrom.CHAT,
    ): List<MediaItemEntity> {
        val result = apiService.getPrivateMoments(from.value, lastId)
        return result.data ?: emptyList()
    }

    suspend fun deletePrivateMoments(entities: List<MediaItemEntity>): List<Long> {
        val deleteSuccessList = mutableListOf<Long>()
        val deleteResultList = entities
            .mapNotNull { it.id }
            .map { deletePrivateMoments(it) }
            .awaitAll()

        for ((id, isSuccess) in deleteResultList) {
            if (isSuccess) {
                deleteSuccessList.add(id)
            }
        }

        return deleteSuccessList
    }

    private suspend fun deletePrivateMoments(id: Long): Deferred<Pair<Long, Boolean>> = withContext(Dispatchers.IO) {
        async {
            val result = runCatching { apiService.deletePrivateMoment(id) }
            id to (result.getOrNull()?.isSuccess ?: false)
        }
    }

    suspend fun addPrivateMoment(uri: Uri): MediaItemEntity? {
        val enableResult = apiService.canAddPrivateMoment()

        if (!enableResult.isSuccess) return null //不允许添加

        val generateUrl = globalRepository.uploadUri(uri) ?: return null
        return addPrivateMoment(generateUrl, uri)
    }

    private suspend fun addPrivateMoment(url: String, uri: Uri): MediaItemEntity? {
        val mimeType = UriUtils.getMimeType(context, uri)

        val type = when(mimeType) {
            IMAGE -> MediaItemEntity.TYPE_IMAGE
            VIDEO -> MediaItemEntity.TYPE_VIDEO
            else -> return null
        }
        val fileSize = UriUtils.getFileSize(context, uri)
        val (width, height, duration) = UriUtils.getUriMediaInfo(context, mimeType, uri)

        val result = apiService.addPrivateMoment(
            url = url,
            type = type,
            width = width,
            height = height,
            size = fileSize,
            duration = duration
        )
        val id = result.data?.id ?: return null
        return MediaItemEntity(
            id = id,
            type = type,
            url = url,
            width = width,
            height = height,
            size = fileSize,
            duration = duration
        )
    }

    enum class MomentsFrom(val value: Int) {
        LIST(1),

        CHAT(2),

        ADD_PRIVATE_MSG(3);
    }
}