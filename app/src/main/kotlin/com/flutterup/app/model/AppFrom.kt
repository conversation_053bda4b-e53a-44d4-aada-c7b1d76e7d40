package com.flutterup.app.model

enum class AppFrom(val value: Int) {
    Unknown(0),

    Discover(1),  //首页划卡

    WinkReceived(2),  //被wink

    Visitor(3), //访客

    WinkSent(4), //wink过的

    Chat(5), //聊天

    Profile(6), //个人主页

    PingChat(7), //ping chat
    ;

    companion object {
        fun fromValue(value: Int): AppFrom {
            return entries.firstOrNull { it.value == value } ?: Unknown
        }
    }
}