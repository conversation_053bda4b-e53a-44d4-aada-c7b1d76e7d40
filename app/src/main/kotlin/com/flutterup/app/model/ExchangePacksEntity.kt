package com.flutterup.app.model

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass


@Keep
@JsonClass(generateAdapter = true)
data class ExchangePacksEntity(

    @Json(name = "id")
    val id: String? = null
)


/**
 * @param id 权益/礼物id
 * @param num 数量，gift默认永远为1
 * @param price 价格
 */
@Keep
@JsonClass(generateAdapter = true)
data class ExchangePacksRequest(
    @Json(name = "product_id")
    val id: String,

    @Json(name = "num")
    val num: Int,

    @Json(name = "price")
    val price: Int,
)