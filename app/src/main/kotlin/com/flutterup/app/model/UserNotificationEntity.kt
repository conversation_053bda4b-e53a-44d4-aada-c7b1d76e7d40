package com.flutterup.app.model

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@Keep
@JsonClass(generateAdapter = true)
data class UserNotificationEntity(
    /**
     * Bitmask:
     * 0x01: newmsg,
     * 0x02: newmatch,
     * 0x04: newlick,
     * 0x08: cupidchat
     */
    @Json(name = "config") val config: Long? = null // Consider Int
) {


    companion object {
        const val SETTINGS_NEW_MESSAGE_ALERT = 1 shl 0

        const val SETTINGS_NEW_CONNECTION_ALERT = 1 shl 1

        const val SETTINGS_NEW_WINKS_ALERT = 1 shl 2

        const val SETTINGS_NEW_VISITOR_ALERT = 1 shl 3
    }
}