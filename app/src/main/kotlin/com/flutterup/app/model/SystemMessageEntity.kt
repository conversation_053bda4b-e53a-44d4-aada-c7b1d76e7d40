package com.flutterup.app.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.flutterup.network.Action
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
@JsonClass(generateAdapter = true)
data class SystemMessageEntity(

    @Json(name = "push_type")
    val pushType: Int? = null,

    @<PERSON><PERSON>(name = "user_id") //用户id
    val userId: String? = null,

    @<PERSON><PERSON>(name = "nick_name") //用户昵称
    val nickname: String? = null,

    @<PERSON><PERSON>(name = "headimg") //用户头像
    val headimg: String? = null,


    @<PERSON><PERSON>(name = "sign") //用户签名
    val sign: String? = null,


    @J<PERSON>(name = "age")
    val age: Int? = null,

    @<PERSON><PERSON>(name = "online")
    val online: Int? = null,

    @<PERSON><PERSON>(name = "nearby")
    val nearby: Int? = null,

    @<PERSON><PERSON>(name = "auto_msg")
    val autoMsg: String? = null,

    @Json(name = "msg")
    val msg: String? = null,

    @<PERSON>son(name = "tags")
    val tags: List<String>? = null,

    @J<PERSON>(name = "action")
    val action: Action? = null,

    @Json(name = "media_list")
    val mediaList: List<MediaItemEntity>? = null
) : Parcelable {

    companion object {
        const val PUSH_TYPE_WINKS_YOU = 1

        const val PUSH_TYPE_CUPID_CHAT = 2

        const val PUSH_TYPE_MATCHED = 3

        const val PUSH_TYPE_VISITOR = 4

        const val PUSH_TYPE_RECEIVE_PING_CHAT = 5
    }
}