package com.flutterup.app.model

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@Keep
@JsonClass(generateAdapter = true)
data class UpgradeEntity(
    @<PERSON><PERSON>(name="url") val url: String? = null,
    @<PERSON><PERSON>(name="content") val content: String? = null,
    @<PERSON><PERSON>(name="app_down") val downloadType: Int? = null, // 1: app downloads, 0: redirect to URL. Consider <PERSON>olean or En<PERSON>
    @Json(name="version") val version: String? = null,
    @<PERSON><PERSON>(name="type") val type: Int? = null // 0: no update, 1: suggest, 2: force. Consider Int or Enum
) {

    companion object {
        const val DOWNLOAD_FROM_APP = 1
        const val DOWNLOAD_REDIRECT_URL = 0

        const val NO_UPDATE = 0
        const val SUGGEST_UPDATE = 1
        const val FORCE_UPDATE = 2
    }
}