package com.flutterup.app.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class UserActionEntity(
    @Json(name = "right") val right: UserRightsEntity
)


/**
 *  1: dislike, 2: like
 */
enum class UserActionType(val value: Int) {
    Dislike(1),
    Like(2);
}

/**
 * 0: default, 1: connected card
 */
enum class UserActionCardType(val value: Int) {
    Default(0),

    Connected(1);
}