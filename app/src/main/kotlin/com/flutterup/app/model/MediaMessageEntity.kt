package com.flutterup.app.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.flutterup.app.model.MediaItemEntity.Companion.TYPE_IMAGE
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
@JsonClass(generateAdapter = true)
data class MultiMediaMessageEntity(
    @Json(name = "original_msgid")
    val messageId: String = "",

    @Json(name = "type")
    val type: Int = TYPE_IMAGE,

    @<PERSON>son(name = "media_ids")
    val idList: String = "",

    @<PERSON>son(name = "num")
    val size: Int = 0,

    @Json(name = "list")
    var entities: List<MediaMessageEntity> = emptyList()
) : Parcelable {
    constructor(entities: List<MediaItemEntity>) : this(
        idList = entities.joinToString(",") { it.id.toString() },
        size = entities.size,
        entities = entities.map { MediaMessageEntity(it) },
        type = entities.first().type
    )
}


@Keep
@Parcelize
@JsonClass(generateAdapter = true)
data class MediaMessageEntity(
    @Json(name = "id")
    val id: Long = 0,

    @Json(name = "original_msgid")
    val messageId: String = "",

    @Json(name = "url")
    val url: String = "",

    @Json(name = "thumb_url")
    val thumbUrl: String = "",

    @Json(name = "type")
    val type: Int = TYPE_IMAGE,

    @Json(name = "duration")
    val duration: Int = 0,

    @Json(name = "localStatus")
    val localStatus: Int = 0,

    @Json(name = "addSrvId")
    val addSrvId: Long = 0,

    @Json(name = "actionType")
    val actionType: Int = 0,

    @Json(name = "favorite")
    val favorite: Boolean = false,

    @Json(name = "status")
    val status: Int = 0,

    @Json(name = "next_send_time")
    val nextSendTime: Long = 0,

    @Json(name = "send_type")
    val sendType: Int = 0,

    @Json(name = "is_private")
    val isPrivate: Int = 0,

    @Json(name = "imId")
    val imId: String? = "",
) : Parcelable {

    val imageUrl: String get() = if (type == TYPE_IMAGE) url else thumbUrl.ifEmpty { url }


    constructor(entity: MediaItemEntity) : this(
        id = entity.id ?: 0,
        url = entity.url,
        thumbUrl = entity.thumbUrl.orEmpty(),
        type = entity.type,
        duration = entity.duration?.toInt() ?: 0,
    )
}