package com.flutterup.app.model

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@Keep
@JsonClass(generateAdapter = true)
data class DataSyncMessageEntity(
    @<PERSON>son(name = "event")
    val event: String,

    @<PERSON>son(name = "data")
    val data: UserOnlineDataSyncEntity? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class UserOnlineEntity(
    @Json(name = "user_id") val userId: String,
)

@Keep
@JsonClass(generateAdapter = true)
data class UserOnlineDataSyncEntity(
    @Json(name = "online")
    val online: Int = 0,

    @<PERSON><PERSON>(name = "im_uids")
    val onlineUserIds: List<String>? = null
)