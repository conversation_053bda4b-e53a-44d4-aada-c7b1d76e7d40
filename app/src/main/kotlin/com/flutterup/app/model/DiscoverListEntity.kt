package com.flutterup.app.model

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@Keep
@JsonClass(generateAdapter = true)
data class DiscoverListEntity(
    @Json(name = "match_list") val matchList: List<DiscoverItemEntity> = emptyList(),

    @Json(name = "privacy_list") val privacyList: List<String>? = null, // List of user IDs hidden due to privacy?

    @Json(name = "rights") val rights: UserRightsEntity? = null
) {
    val lastId: String?
        get() = matchList.lastOrNull()?.userId
}

@Keep
data class DiscoverItemEntity(
    @J<PERSON>(name = "user_id") val userId: String? = null,

    @<PERSON><PERSON>(name = "nick_name") val nickname: String? = null,

    @<PERSON><PERSON>(name = "headimg") val headimg: String? = null,

    @<PERSON><PERSON>(name = "age") val age: Int? = null,

    @<PERSON><PERSON>(name = "birthday") val birthday: String? = null,

    /**
     * 0: Offline
     * 1: Online
     */
    @<PERSON>son(name = "online") val online: Int? = null, //0不在线 1在线

    @<PERSON><PERSON>(name = "sign") val intro: String? = null,

    @<PERSON><PERSON>(name = "tags") val interests: List<String>? = null,

    @Json(name = "location") val location: Int? = null, // Distance or ID? Consider Int

    @Json(name = "media_list") val mediaList: List<MediaItemEntity> = emptyList(), // Assumed from CommonModels.kt

    /**
     * 0: Default
     * 1: Connected but inactive (7d)
     * 2: Connected but no icebreak (5d)
     */
    @Json(name = "type") val type: Int? = null, // Consider Enum
)