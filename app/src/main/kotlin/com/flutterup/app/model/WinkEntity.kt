package com.flutterup.app.model

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass


enum class WinkType(val value: Int) {
    Received(1),
    Sent(3);
}

enum class WinkReadType(val value: Int) {
    Received(1001),

    Visitor(1002),

    Sent(3);
}

@Keep
@JsonClass(generateAdapter = true)
data class WinksEntity(
    @Json(name = "list") val list: List<WinkItemEntity>? = null
)

@Keep
@JsonClass(generateAdapter = true)
data class WinkItemEntity(
    @Json(name = "id") val id: Long,

    @<PERSON><PERSON>(name = "user_id") val userId: String? = null,

    @<PERSON><PERSON>(name = "nick_name") val nickname: String? = null,

    @Json(name = "media_list") val mediaList: List<MediaItemEntity>? = null, // Assumed from CommonModels.kt

    @Json(name = "age") val age: Int? = null,

    /** 0: Not nearby, 1: Nearby */
    @Json(name = "nearby") val nearby: Int? = null, // Corrected name, Consider Int/Boolean

    @Json(name = "location") val location: Int? = null, // Distance or ID? Consider Int

    /** 0: Offline, 1: Online */
    @Json(name = "online") val online: Int? = null, // Consider Int/Boolean/Enum

    /** Time until expiration (server calculated string) */
    @Json(name = "time_out") val timeout: String? = null,

    /** 1: Read, 0: Unread */
    @Json(name = "read") val read: Int? = null, // Consider Int/Boolean

    @Json(name = "headimg") val headimg: String? = null,
)