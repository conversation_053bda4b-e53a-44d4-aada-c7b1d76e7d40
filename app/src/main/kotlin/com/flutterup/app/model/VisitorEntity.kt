package com.flutterup.app.model

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass


@Keep
@JsonClass(generateAdapter = true)
data class VisitorEntity(
    @Json(name = "list") val list: List<VisitorItemEntity>? = null
)

@Keep
@JsonClass(generateAdapter = true)
data class VisitorItemEntity(
    @Json(name = "id") val id: Long,

    @<PERSON>son(name = "user_id") val userId: String? = null,

    @Json(name = "nick_name") val nickname: String? = null,

    @Json(name = "headimg") val headimg: String? = null,

    @<PERSON><PERSON>(name = "age") val age: Int? = null,

    /** 0: Not nearby, 1: Nearby */
    @<PERSON><PERSON>(name = "nearby") val nearby: Int? = null, // Consider Int/Boolean

    @Json(name = "location") val location: Int? = null, // Distance or ID? Consider Int

    /** 0: Offline, 1: Online */
    @J<PERSON>(name = "online") val online: Int? = null, // Consider Int/Boolean/Enum

    @Json(name = "view_count") val viewCount: Int? = null, // Consider Int

    /** 1: Read, 0: Unread */
    @Json(name = "read") val read: Int? = null, // Consider Int/Boolean
)