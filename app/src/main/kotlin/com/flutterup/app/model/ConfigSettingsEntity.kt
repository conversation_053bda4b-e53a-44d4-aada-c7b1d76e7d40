package com.flutterup.app.model

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@Keep
@JsonClass(generateAdapter = true)
data class ConfigSettingsEntity(
    @<PERSON>son(name="report_list") val reportList: List<ReportItem>? = null,
    @Json(name="greeting_list") val greetingList: List<GreetingItem>? = null,
    @Json(name="sys_account") val sysAccount: SysAccountEntity? = null,
    @Json(name="html") val html: HtmlConfigEntity? = null,
    @<PERSON><PERSON>(name="online_api_interval") val onlineApiInterval: Long? = null,
    @<PERSON>son(name="ppv_tooltip_show_min_num") val ppvTooltipShowMinNum: Int? = null,
    @<PERSON><PERSON>(name="ppv_tooltip_show_delay_time") val ppvTooltipShowDelayTime: Int? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class ReportItem(
    @Json(name="title") val title: String? = null,
    @Json(name="id") val id: Long? = null
)

@Keep
@JsonClass(generateAdapter = true)
data class GreetingItem(
    @Json(name="title") val title: String? = null,
    @Json(name="id") val id: Long? = null
)


@Keep
@JsonClass(generateAdapter = true)
data class SysAccountEntity(
    @Json(name="notice_account") val noticeAccount: String? = null,
    @Json(name="push_account") val pushAccount: String? = null,
    @Json(name="service_account") val serviceAccount: String? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class HtmlConfigEntity(
    @Json(name="terms") val terms: String? = null,
    @Json(name="privacy") val privacy: String? = null,
    @Json(name="childSafe") val childSafe: String? = null,
)