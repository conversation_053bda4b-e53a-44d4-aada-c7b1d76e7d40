package com.flutterup.app.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize


@Keep
@JsonClass(generateAdapter = true)
data class UpdateProfileResult(
    @Json(name = "user")
    val userInfo: UserInfo? = null
)

@Keep
@JsonClass(generateAdapter = true)
@Parcelize
data class UpdateProfileRequest(
    /**
     * 编辑的信息类型
     * 13 第一页
     * 14 第二页
     * 15 第三页
     */
    @Json(name = "step")
    val step: Int? = null,

    /**
     * 昵称
     */
    @<PERSON><PERSON>(name = "nick_name")
    val nickName: String? = null,

    /**
     * 生日
     */
    @J<PERSON>(name = "brithday")
    val birthday: String? = null,

    /**
     * 性别
     */
    @Json(name = "sex")
    val sex: Int? = null,

    /**
     * 性向
     */
    @<PERSON><PERSON>(name = "sexuality")
    val sexuality: Int? = null,

    /**
     * 签名
     */
    @Json(name = "sign")
    val sign: String? = null,

    /**
     * 标签
     */
    @Json(name = "tag")
    val tag: String? = null,

    /**
     * 媒体列表
     */
    @Json(name = "media_list")
    val mediaList: String? = null
) : Parcelable {
    companion object {
        const val STEP_ONE = 16
        const val STEP_TWO = 17

        const val EDIT_NICKNAME = 1 //编辑昵称
        const val EDIT_BIRTHDAY = 2 //编辑生日

        const val EDIT_GENDER = 3 //编辑性别

        const val EDIT_MEET = 4 //编辑性向

        const val EDIT_DESC = 5 //编辑签名

        const val EDIT_INTERESTS = 6 //编辑标签

        const val EDIT_AVATAR = 7 //编辑头像

        @Deprecated("Use EDIT_AVATAR instead")
        const val EDIT_MEDIA = 9 //编辑媒体
    }
}