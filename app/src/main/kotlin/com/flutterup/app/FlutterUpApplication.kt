package com.flutterup.app

import android.app.ActivityManager
import android.os.Build.VERSION.SDK_INT
import coil3.ImageLoader
import coil3.PlatformContext
import coil3.SingletonImageLoader
import coil3.disk.DiskCache
import coil3.disk.directory
import coil3.gif.AnimatedImageDecoder
import coil3.gif.GifDecoder
import coil3.video.VideoFrameDecoder
import com.flutterup.app.utils.AppActivityManager
import com.flutterup.app.utils.GlobalExceptionHandler
import com.flutterup.base.BaseApplication
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineExceptionHandler

@HiltAndroidApp
class FlutterUpApplication : BaseApplication(), SingletonImageLoader.Factory {

    override fun onCreate() {
        super.onCreate()
        registerActivityLifecycleCallbacks(AppActivityManager)
    }

    override fun exceptionHandler(): CoroutineExceptionHandler {
        return GlobalExceptionHandler
    }

    override fun newImageLoader(context: PlatformContext): ImageLoader {
        return ImageLoader.Builder(context)
            .diskCache {
                DiskCache.Builder()
                    .directory(context.cacheDir.resolve("app_cache").resolve("coil"))
                    .build()
            }
            .components {
                add(VideoFrameDecoder.Factory())
                if (SDK_INT >= 28) {
                    add(AnimatedImageDecoder.Factory())
                } else {
                    add(GifDecoder.Factory())
                }
            }
            .build()
    }
}