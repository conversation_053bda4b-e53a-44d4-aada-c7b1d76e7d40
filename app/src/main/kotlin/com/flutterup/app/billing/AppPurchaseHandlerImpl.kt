package com.flutterup.app.billing

import com.flutterup.app.network.ApiService
import com.flutterup.billinghelper.model.PurchaseEntity
import com.flutterup.billinghelper.handler.PurchaseHandler
import javax.inject.Inject
import javax.inject.<PERSON>ton


@Singleton
class AppPurchaseHandlerImpl @Inject constructor(
    private val apiService: ApiService
) : PurchaseHandler {

    override suspend fun verifyPurchase(purchase: PurchaseEntity): <PERSON><PERSON><PERSON> {
        return false
    }

    override suspend fun onVerificationFailed(
        purchase: PurchaseEntity,
        error: Throwable?
    ) {
    }

    override suspend fun onVerificationSuccess(purchase: PurchaseEntity) {
    }
}