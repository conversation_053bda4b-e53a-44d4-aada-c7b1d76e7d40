package com.flutterup.app.billing

import com.flutterup.app.model.AppPaymentFrom
import com.flutterup.app.model.ProductItem
import com.flutterup.app.model.StoreProductEntity
import com.flutterup.app.network.ApiService
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.utils.LocaleUtils
import com.flutterup.billinghelper.utils.BillingUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton


@Singleton
class AppBillingProduct @Inject constructor(
    private val apiService: ApiService,
    private val userMonitor: UserMonitor,
    private val scope: CoroutineScope,
) {
    private var subscription: StoreProductEntity? = null
    private var inapp: StoreProductEntity? = null
    private var products: List<ProductItem> = emptyList()

    private val _productStateFlow: MutableStateFlow<ProductState> = MutableStateFlow(ProductState.Idle)
    val productStateFlow: StateFlow<ProductState> = _productStateFlow.asStateFlow()

    fun init() {
        if (_productStateFlow.value != ProductState.Idle) return

        if (!userMonitor.isLogin) {
            initProductsSureLogin()
            return
        }

        initProducts()
    }

    private fun initProductsSureLogin() {
        scope.launch {
            userMonitor.userInfoState.filter { it?.token != null }
                .take(1)
                .collect { initProducts() }
        }
    }

    private fun initProducts() {
        _productStateFlow.update { ProductState.Loading }

        scope.launch {
            val subscription = getProduct(TYPE_SUBSCRIPTION)
            val diamonds = getProduct(TYPE_DIAMONDS)

            <EMAIL> = subscription
            <EMAIL> = diamonds
            <EMAIL> = subscription?.shops.orEmpty() + diamonds?.shops.orEmpty()

            _productStateFlow.update { ProductState.Success(subscription, diamonds, products) }
        }
    }

    /**
     * @param type 0: subscription, 1: flashchat, 2: private video, 3: private photo, 4: diamonds
     */
    private suspend fun getProduct(type: Int): StoreProductEntity? {
        val result = apiService.getProductList(
            type = type,
            from = AppPaymentFrom.UNKNOWN.value,
            isoCode = LocaleUtils.currentLocale.isO3Country
        )

        return result.data
    }

    sealed interface ProductState {
        object Idle : ProductState
        object Loading : ProductState

        data class Success(
            val subscription: StoreProductEntity?,
            val inapp: StoreProductEntity?,
            val products: List<ProductItem>
        ) : ProductState
    }
}

private const val TYPE_SUBSCRIPTION = 0
private const val TYPE_DIAMONDS = 4
