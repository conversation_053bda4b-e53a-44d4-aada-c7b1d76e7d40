package com.flutterup.app.network

import android.content.res.Resources
import android.os.Build
import com.flutterup.app.BuildConfig
import com.flutterup.app.network.environment.AppEnvironmentProvider
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.utils.NetworkUtils
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import java.util.TimeZone
import javax.inject.Inject
import kotlin.collections.set

class HeaderInterceptor @Inject constructor(
    private val appEnvironmentProvider: AppEnvironmentProvider,
    private val userMonitor: UserMonitor
) : Interceptor {

    private val defaultHeaders = mapOf(
        "Content-Type" to "application/json;charset=utf-8",
        "Accept" to "application/json"
    )

    override fun intercept(chain: Interceptor.Chain): Response {
        val request: Request = chain.request()
        // 新的请求
        val requestBuilder: Request.Builder = request.newBuilder()
        requestBuilder.method(request.method, request.body)

        val requestHost = request.url.host
        //只有自己域名才会加请求头
        if (requestHost == appEnvironmentProvider.host) {
            //添加公共参数,添加到header中
            getHeaders().forEach {
                requestBuilder.addHeader(it.key, it.value)
            }
        }

        val newRequest: Request = requestBuilder.build()
        return chain.proceed(newRequest)
    }

    private fun getHeaders() = defaultHeaders + anotherHeaders()

    private fun anotherHeaders(): MutableMap<String, String> {
        val headers = mutableMapOf<String, String>()

        headers["UserId"] = userMonitor.userId.orEmpty()
        headers["Authorization"] = userMonitor.token.orEmpty()
//        headers["version-name"] = BuildConfig.VERSION_NAME
//        headers["version-code"] = BuildConfig.VERSION_CODE.toString()
//        headers["app-name"] = BuildConfig.ALIAS
//        headers["app-channel"] = BuildConfig.ALIAS

        headers["version-name"] = "1.0.2"
        headers["version-code"] = BuildConfig.VERSION_CODE.toString()
        headers["app-name"] = "localuv"
        headers["app-channel"] = "localuv"


        headers["mb-brand"] = Build.BRAND.orEmpty()
        headers["mb_models"] = Build.MODEL.orEmpty()
        headers["ob-osversion"] = Build.VERSION.RELEASE.orEmpty()

        headers["dev-id"] = appEnvironmentProvider.deviceId
        headers["ad-id"] = appEnvironmentProvider.adid

        headers["local"] = Resources.getSystem().configuration.locales.get(0).language
        headers["time-zero"] = TimeZone.getDefault().id
        headers["x-proxy-f"] = NetworkUtils.isVpnOrProxyActive().toString()

        headers["platfrom"] = "android"
        headers["X-Encrypted"] = DecodeResponseInterceptor.isEncoded.toString() //请求响应加密

        return headers
    }
}