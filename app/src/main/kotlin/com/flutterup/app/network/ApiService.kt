package com.flutterup.app.network

import com.flutterup.app.model.ChangePasswordEntity
import com.flutterup.app.model.CheckOrderEntity
import com.flutterup.app.model.DiscoverListEntity
import com.flutterup.app.model.ExchangePacksEntity
import com.flutterup.app.model.ExchangePacksRequest
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.model.PrivateMomentAddResult
import com.flutterup.app.model.UpdateProfileRequest
import com.flutterup.app.model.UpdateProfileResult
import com.flutterup.app.model.UserActionEntity
import com.flutterup.app.model.UserCountEntity
import com.flutterup.app.model.UserInfo
import com.flutterup.app.model.UserOnlineEntity
import com.flutterup.app.model.VisitorEntity
import com.flutterup.app.model.WinksEntity
import com.flutterup.app.model.GeneratorOrderEntity
import com.flutterup.app.model.StoreProductEntity
import com.flutterup.gifts.entity.GiftResourceInfo
import com.flutterup.network.BaseResponse
import retrofit2.http.Body
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface ApiService {
    /**
     * Sign in with Email
     */
    @FormUrlEncoded
    @POST("/signin/email")
    suspend fun signInWithEmail(
        @Field("email") email: String,
        @Field("pwd") password: String
    ) : BaseResponse<UserInfo>


    /**
     * 判断是否要拦截用户
     */
    @POST("/user/block")
    suspend fun checkUserAlert() : BaseResponse<Any>


    /**
     * 编辑个人资料
     * @param request
     * @see [com.flutterup.app.model.UpdateProfileRequest]
     */
    @POST("/user/editmineprofile")
    suspend fun updateProfileInfo(@Body request: UpdateProfileRequest) : BaseResponse<UpdateProfileResult>

    /**
     * 获取自己的个人资料
     */
    @POST("/user/mineinfo")
    suspend fun getMineProfileInfo(): BaseResponse<UserInfo>


    /**
     * 获取用户各种列表的数量数据
     */
    @POST("/user/listdetail")
    suspend fun getUserListNum(): BaseResponse<UserCountEntity>

    /**
     * 获取滑卡列表
     */
    @FormUrlEncoded
    @POST("/swipe/list")
    suspend fun getSwipeUserList(
        @Field("last_ids") lastId: String? = null,
        @Field("size") size: Int = 20,
        @Field("tab") tab: Int? = null
    ) : BaseResponse<DiscoverListEntity>


    /**
     * 获取其他用户的个人资料
     */
    @FormUrlEncoded
    @POST("/user/otherinfo")
    suspend fun getAnotherProfileInfo(@Field("user_id") userId: String): BaseResponse<UserInfo>

    /**
     * 使用ids，获取用户信息列表
     */
    @FormUrlEncoded
    @POST("/user/infos")
    suspend fun getAnotherProfileInfoList(@Field("user_ids") userIds: String): BaseResponse<List<UserInfo>>

    /**
     * 对用户进行选择操作
     * @param type 1: dislike, 2: like
     * @param cardFlag 0: default, 1: connected card
     */
    @FormUrlEncoded
    @POST("/user/operate")
    suspend fun userAction(
        @Field("user_id") userId: String,
        @Field("type") type: Int,
        @Field("from") from: Int? = null,
        @Field("card_flag") cardFlag: Int = 0
    ) : BaseResponse<UserActionEntity>


    /**
     * 获取用户喜欢/被喜欢列表
     * @param tag 1: WLM, 3: I Like
     */
    @FormUrlEncoded
    @POST("/user/likelist")
    suspend fun getWinksList(
        @Field("tag") tag: Int,
        @Field("last_id") lastId: Long? = null,
    ): BaseResponse<WinksEntity>

    /**
     * 已读某种类型的喜欢列表
     */
    @FormUrlEncoded
    @POST("/user/readlike")
    suspend fun markAllWinksRead(@Field("from") from: Int): BaseResponse<Any>


    /**
     * 获取访客列表
     */
    @FormUrlEncoded
    @POST("/user/visitors")
    suspend fun getVisitorList(@Field("last_id") lastId: Long?): BaseResponse<VisitorEntity>


    /**
     * 重设密码
     */
    @POST("/user/changepwd")
    suspend fun changePassword(@Body body: ChangePasswordEntity) : BaseResponse<Any>

    /**
     * 删除账号
     */
    @POST("/user/remove")
    suspend fun deleteAccount() : BaseResponse<Any>

    /**
     * 举报用户
     */
    @FormUrlEncoded
    @POST("/user/report")
    suspend fun reportUser(
        @Field("user_id") userId: String,
        @Field("report_id") reportId: Long? = null,
        @Field("from") from: Int? = null,
    ): BaseResponse<Any>


    /**
     * app状态上报
     */
    @FormUrlEncoded
    @POST("/user/activestatus")
    suspend fun reportAppStatus(@Field("status") status: Int): BaseResponse<Any>


    /**
     * 更新通知设置
     */
    @FormUrlEncoded
    @POST("/user/upgradenotify")
    suspend fun updateNotificationSettings(@Field("config") config: Int): BaseResponse<Any>


    /**
     * 获取在线用户列表
     */
    @POST("/user/onlinelist")
    suspend fun getOnlineUserList(): BaseResponse<List<UserOnlineEntity>>

    @FormUrlEncoded
    @POST("/user/upgradepermission")
    suspend fun upgradePermissionStatus(
        @Field("location_status") locationStatus: Int? = null,

        @Field("notice_status") noticeStatus: Int? = null,

        @Field("states") states: String? = null,

        @Field("longitude") longitude: Double? = null,

        @Field("latitude") latitude: Double? = null,

        @Field("country") country: String? = null,

        @Field("time_zero") timeZero: String? = null
    ): BaseResponse<Any>



    /**
     * 获取新聊天用户列表
     */
    @POST("/user/newchaters")
    suspend fun getNewConnections(): BaseResponse<List<UserInfo>>

    /**
     * 快捷聊天
     */
    @FormUrlEncoded
    @POST("/user/instantchat")
    suspend fun sendInstantChatMessage(
        @Field("user_id") userId: String,
        @Field("msg") message: String,
        @Field("from") from: Int? = null,
    ): BaseResponse<Any>

    @FormUrlEncoded
    @POST("/user/greeting")
    suspend fun sendGreetingMessage(
        @Field("user_id") userId: String,
        @Field("msg") message: String
    ): BaseResponse<Any>


    @FormUrlEncoded
    @POST("/global/firstgreeting")
    suspend fun triggerCustomerServiceGreeting(@Field("target_id") targetId: String): BaseResponse<Any>

    /**
     * 获取用户私密媒体列表
     */
    @FormUrlEncoded
    @POST("/intimate/albumlist")
    suspend fun getPrivateMoments(
        @Field("from") from: Int = 0,
        @Field("last_id") lastId: Long? = null,
        @Field("media_type") mediaType: Int? = null,
        @Field("tag") tag: Long? = null,
    ): BaseResponse<List<MediaItemEntity>>

    /**
     * 用户添加请求
     */
    @POST("/intimate/albumcanadd")
    suspend fun canAddPrivateMoment(): BaseResponse<Any>

    /**
     * 添加用户私密媒体
     */
    @FormUrlEncoded
    @POST("/intimate/albumadd")
    suspend fun addPrivateMoment(
        @Field("url") url: String,
        @Field("type") type: Int? = null,
        @Field("width") width: Long? = null,
        @Field("height") height: Long? = null,
        @Field("size") size: Long? = null,
        @Field("duration") duration: Long? = null
    ): BaseResponse<PrivateMomentAddResult>


    /**
     * 删除用户私密媒体
     */
    @FormUrlEncoded
    @POST("/intimate/albumdel")
    suspend fun deletePrivateMoment(@Field("id") id: Long): BaseResponse<Any>

    /**
     * 查看用户私密媒体
     */
    @FormUrlEncoded
    @POST("/intimate/open")
    suspend fun openPrivateMessage(
        @Field("from_msgid") fromMsgId: String,
        @Field("to_msgid") toMsgId: String,
        @Field("media_id") mediaId: Long? = null,
    ): BaseResponse<Any>

    /**
     * 拉黑或者取消拉黑用户
     */
    @FormUrlEncoded
    @POST("/account/block")
    suspend fun blockOrUnblockUser(
        @Field("user_id") userId: String,
        @Field("type") type: Int,
    ): BaseResponse<Any>

    /**
     * 获取礼物兑换列表
     */
    @POST("/gift/list/user")
    suspend fun getGiftList(): BaseResponse<List<GiftResourceInfo>>

    /**
     * 兑换权益/礼物
     * 1. pp/pv/fc
     * 2. 礼物
     */
    @POST("/appmoney/exchange/user")
    suspend fun exchangePacks(@Body request: ExchangePacksRequest): BaseResponse<ExchangePacksEntity>

    @FormUrlEncoded
    @POST("/pay/productlist")
    suspend fun getProductList(
        // 0: subscription, 1: flashchat, 2: private video, 3: private photo, 4: diamonds
        @Field("type") type: Int,
        @Field("from") from: Int,
        @Field("isoCode") isoCode: String? = null,
    ): BaseResponse<StoreProductEntity>


    @FormUrlEncoded
    @POST("/pay/createorder")
    suspend fun generateOrder(
        @Field("prod_id") prodId: String,
        // 1 android, 2 ios
        @Field("pay_plat") payPlatform: Int = 1
    ): BaseResponse<GeneratorOrderEntity>


    @FormUrlEncoded
    @POST("/pay/orderresult")
    suspend fun checkOrder(
        @Field("order_id") orderId: String? = null,
        @Field("third_id") purchaseToken: String? = null,
        @Field("call_data") originalJson: String? = null,
        @Field("event_from") from: Int? = null,
    ): BaseResponse<CheckOrderEntity>
}