package com.flutterup.app.network

import com.flutterup.app.navigation.GlobalNavCenter
import com.flutterup.app.network.environment.AppEnvironmentProvider
import com.flutterup.base.utils.JsonUtils
import com.flutterup.base.utils.Timber
import com.flutterup.network.BaseResponse
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.squareup.moshi.adapter
import okhttp3.Interceptor
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import okio.Buffer
import okio.GzipSource
import java.nio.charset.StandardCharsets
import javax.inject.Inject

class NavigateResponseInterceptor @Inject constructor(
    private val jsonUtils: JsonUtils,
    private val appEnvironmentProvider: AppEnvironmentProvider,
    private val navCenter: GlobalNavCenter,
) : Interceptor {

    companion object {
        private const val TAG = "NavigateResponseInterceptor"
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)


        val requestHost = request.url.host
        if (!response.isSuccessful || requestHost != appEnvironmentProvider.host) {
            return response
        }

        val responseBody = response.body

        // 不修改原始响应
        val source = responseBody.source()
        source.request(Long.MAX_VALUE)
        var buffer = source.buffer.clone()

        // 处理gzip压缩的情况
        val contentEncoding = response.header("Content-Encoding")
        if (contentEncoding.equals("gzip", true)) {
            GzipSource(buffer.clone()).use { gzippedBody ->
                buffer = Buffer()
                buffer.writeAll(gzippedBody)
            }
        }

        val contentType = responseBody.contentType()
        val charset = contentType?.charset(StandardCharsets.UTF_8) ?: StandardCharsets.UTF_8
        val responseBodyString = buffer.readString(charset)

        try {
            val type = Types.newParameterizedType(BaseResponse::class.java, Any::class.java)
            val adapter: JsonAdapter<BaseResponse<String>> = jsonUtils.moshi.adapter(type)
            val networkResponse = jsonUtils.fromJson(responseBodyString, adapter)

            val action = networkResponse?.action
            if (action != null) {
                navCenter.navigate(action)
            }
        } catch (_: Exception) {
        }

        // 重新创建ResponseBody，确保响应内容不被消费
        val newResponseBody = responseBodyString.toResponseBody(contentType)
        return response.newBuilder().body(newResponseBody).build()
    }
}