package com.flutterup.app.network

import com.flutterup.app.model.ConfigSettingsEntity
import com.flutterup.app.model.Tag
import com.flutterup.app.model.UpgradeEntity
import com.flutterup.app.model.UploadResponse
import com.flutterup.network.BaseResponse
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Url

interface GlobalApiService {

    @POST("/global/setup")
    suspend fun getConfig(): BaseResponse<ConfigSettingsEntity>

    @FormUrlEncoded
    @POST("/global/getuploadurl")
    suspend fun getUploadUrl(
        @Field("file_name") fileName: String
    ): BaseResponse<UploadResponse>


    @POST("/global/tags")
    suspend fun getInterestList(): BaseResponse<List<Tag>>

    @POST("/global/upgrade")
    suspend fun checkUpdate(): BaseResponse<UpgradeEntity>

    @PUT
    suspend fun uploadFile(@Url uploadUrl: String, @Body file: RequestBody)
}