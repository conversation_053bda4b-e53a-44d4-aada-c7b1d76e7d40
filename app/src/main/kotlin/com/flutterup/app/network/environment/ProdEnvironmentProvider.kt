package com.flutterup.app.network.environment

import com.flutterup.app.KeyValues
import com.flutterup.app.R
import com.flutterup.base.BaseApplication
import com.flutterup.base.utils.DeviceUtils
import com.flutterup.tracking.TrackingManager

class ProdNoSubAccountEnvironmentProvider : AppEnvironmentProvider {
    override val name: String = "Prod(No sub-account)"

    override val chatKey: String = KeyValues.IM_PROD_APP_KEY

    override val host: String = AppEnvironmentProvider.BASE_URL_PROD

    override val adid: String = System.currentTimeMillis().toString()

    override val deviceId: String = System.currentTimeMillis().toString()
}

class ProdEnvironmentProvider : AppEnvironmentProvider {
    override val name: String = "Prod"

    override val chatKey: String = KeyValues.IM_PROD_APP_KEY

    override val host: String = AppEnvironmentProvider.BASE_URL_PROD

    override val adid: String = TrackingManager.getTrackingId().orEmpty()

    override val deviceId: String = DeviceUtils.getDeviceId()
}