package com.flutterup.app.network
import com.flutterup.app.KeyValues
import okhttp3.Interceptor
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import java.nio.charset.StandardCharsets
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec
import javax.inject.Inject

/**
 * 将请求结果进行AES解密
 */
class DecodeResponseInterceptor @Inject constructor() : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)

        // 如果不需要解密或者响应为空，直接返回
        if (!isEncoded) {
            return response
        }

        // 读取响应内容
        val responseBody = response.body ?: return response
        val contentType = responseBody.contentType()
        val source = responseBody.source()
        source.request(Long.MAX_VALUE) // 缓存整个body
        val buffer = source.buffer.clone()
        val bodyString = buffer.readString(StandardCharsets.UTF_8)

        try {
            // 整个响应体进行解密
            val decryptedBody = decrypt(bodyString, KeyValues.AES_KEY, IV)
            val newBody = decryptedBody.toResponseBody(contentType)
            return response.newBuilder().body(newBody).build()
        } catch (_: Exception) {
            // 解密失败，返回原始响应
        }

        return response
    }

    /**
     * AES解密
     *
     * @param data 待解密数据
     * @param key 密钥
     * @return 解密后的字符串
     */
    private fun decrypt(data: String, key: String, iv: ByteArray): String {
        try {
            // 将密钥处理为16字节
            val keyBytes = getKeyBytes(key)

            // 创建密钥
            val secretKey = SecretKeySpec(keyBytes, "AES")

            // 创建密码器
            val cipher = Cipher.getInstance("AES/CBC/PKCS7Padding")
            val ivSpec = IvParameterSpec(iv)
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec)

            // 解密
            val decodedBytes = android.util.Base64.decode(data, android.util.Base64.DEFAULT)
            val decryptedBytes = cipher.doFinal(decodedBytes)
            return String(decryptedBytes, StandardCharsets.UTF_8)
        } catch (e: Exception) {
            throw RuntimeException(e)
        }
    }

    /**
     * 处理密钥，确保长度为16字节
     */
    private fun getKeyBytes(key: String): ByteArray {
        val keyBytes = key.toByteArray(StandardCharsets.UTF_8)
        // AES要求密钥长度为16、24或32字节，这里取16字节
        val result = ByteArray(16)

        // 如果密钥不足16字节，则填充0；如果超过，则截取前16字节
        System.arraycopy(
            keyBytes, 0,
            result, 0,
            if (keyBytes.size > 16) 16 else keyBytes.size
        )
        return result
    }

    companion object {

        /**
         * 是否请求结果进行加密
         */
        var isEncoded: Boolean = false


        private val IV = "0000000000000000".toByteArray()
    }
}
