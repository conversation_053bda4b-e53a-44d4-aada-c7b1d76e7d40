package com.flutterup.app.di

import com.flutterup.app.network.ApiService
import com.flutterup.app.network.DecodeResponseInterceptor
import com.flutterup.app.network.GlobalApiService
import com.flutterup.app.network.HeaderInterceptor
import com.flutterup.app.network.NavigateResponseInterceptor
import com.flutterup.app.network.environment.AppEnvManager
import com.flutterup.app.network.environment.AppEnvironmentProvider
import com.flutterup.network.AppClient
import com.flutterup.network.impl.NetworkServiceProvider
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.logging.HttpLoggingInterceptor
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object NetworkServiceModule {

    @Singleton
    @Provides
    fun provideAppEnvironment(): AppEnvironmentProvider {
        return AppEnvManager.getEnvironment()
    }

    @Singleton
    @Provides
    fun provideNetworkParams(
        appEnvironmentProvider: AppEnvironmentProvider,
        headerInterceptor: HeaderInterceptor,
        decodeResponseInterceptor: DecodeResponseInterceptor,
        navigateResponseInterceptor: NavigateResponseInterceptor,
    ): NetworkServiceProvider.Params {
        return NetworkServiceProvider.Params(
            host = appEnvironmentProvider.host,
            connectTimeout = CONNECT_TIMEOUT,
            readTimeout = READ_TIMEOUT,
            writeTimeout = WRITE_TIMEOUT,
            retryOnConnectionFailure = false,
            level = HttpLoggingInterceptor.Level.BODY,
            interceptors = listOf(
                headerInterceptor,
                decodeResponseInterceptor,
                navigateResponseInterceptor,
            ),
        )
    }

    @Singleton
    @Provides
    fun provideApiService(@AppClient networkService: NetworkServiceProvider): ApiService {
        return networkService[ApiService::class.java]
    }

    @Singleton
    @Provides
    fun provideGlobalApiService(@AppClient networkService: NetworkServiceProvider): GlobalApiService {
        return networkService[GlobalApiService::class.java]
    }

    private const val CONNECT_TIMEOUT = 30_000L
    private const val READ_TIMEOUT = 60_000L
    private const val WRITE_TIMEOUT = 60_000L
}