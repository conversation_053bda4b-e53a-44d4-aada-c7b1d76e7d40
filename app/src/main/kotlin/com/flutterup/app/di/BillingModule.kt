package com.flutterup.app.di

import com.flutterup.app.billing.AppBillingMonitor
import com.flutterup.app.billing.AppBillingProduct
import com.flutterup.app.billing.AppPurchaseHandlerImpl
import com.flutterup.app.network.ApiService
import com.flutterup.app.utils.UserMonitor
import com.flutterup.billinghelper.BillingHelper
import com.flutterup.billinghelper.handler.PurchaseHandler
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object BillingModule {

    @Provides
    @Singleton
    fun providePurchaseHandler(
        apiService: ApiService
    ): PurchaseHandler {
        return AppPurchaseHandlerImpl(apiService)
    }

    @Provides
    @Singleton
    fun provideAppBillingProduct(
        apiService: ApiService,
        userMonitor: UserMonitor,
        scope: CoroutineScope
    ): AppBillingProduct {
        return AppBillingProduct(apiService, userMonitor, scope)
    }

    @Provides
    @Singleton
    fun provideAppBillingHelper(
        helper: BillingHelper,
        product: AppBillingProduct
    ): AppBillingMonitor {
        return AppBillingMonitor(helper, product)
    }
}