package com.flutterup.app.utils.chat.core.internal

import com.flutterup.app.model.DataSyncMessageEntity
import com.flutterup.app.network.ApiService
import com.flutterup.app.utils.UserOnlineMonitor
import com.flutterup.app.utils.UserMonitor
import com.flutterup.app.utils.chat.core.ChatOnReceivedMessageListener
import com.flutterup.chat.message.content.DataSyncBaseMessageContent
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.imlib.model.ReceivedProfile
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

interface ChatDataSyncMonitor : ChatOnReceivedMessageListener

@Singleton
class ChatDataSyncMonitorImpl @Inject constructor(
    private val userMonitor: UserMonitor,
    private val onlineMonitor: UserOnlineMonitor,

    private val apiService: ApiService,
    private val appScope: CoroutineScope
) : ChatDataSyncMonitor {

    override fun onReceivedMessage(
        message: Message?,
        profile: ReceivedProfile?
    ) {
        if (message == null) return

        val conversationType = message.conversationType
        val content = message.content
        val isOffline = profile?.isOffline == true
        val isSelf = userMonitor.isSelf(message.targetId)

        if (
            conversationType == Conversation.ConversationType.SYSTEM //系统消息
            && content is DataSyncBaseMessageContent //数据同步消息
            && isSelf //是自己
            && !isOffline //不是离线消息
        ) {
            handleSyncMessage(content)
        }
    }


    private fun handleSyncMessage(message: DataSyncBaseMessageContent) {
        val data = message.get(DataSyncMessageEntity::class.java) ?: return

        when(data.event) {
            EVENT_ONLINE -> handleOnlineEvent(data)
            EVENT_USER_RIGHT -> handleUserRightEvent()
        }
    }

    private fun handleOnlineEvent(data: DataSyncMessageEntity) {
        val onlineUserId = data.data?.onlineUserIds ?: return
        onlineMonitor.updateOnlineUserList(onlineUserId)
    }

    private fun handleUserRightEvent() {
        appScope.launch {
            val result = apiService.getMineProfileInfo()
            if (result.isSuccess) {
                userMonitor.updateUserRight(result.data?.right ?: return@launch)
            }
        }
    }

    private companion object {
        const val EVENT_ONLINE = "online"
        const val EVENT_USER_RIGHT = "right"
    }
}