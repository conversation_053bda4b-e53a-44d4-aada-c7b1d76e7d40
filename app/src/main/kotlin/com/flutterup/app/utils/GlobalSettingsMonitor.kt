package com.flutterup.app.utils

import androidx.compose.runtime.State
import com.flutterup.app.model.ConfigSettingsEntity
import com.flutterup.app.model.GreetingItem
import com.flutterup.app.model.ReportItem
import com.flutterup.app.network.GlobalApiService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.retryWhen
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject
import javax.inject.Singleton

interface GlobalSettingsMonitor {

     val settings: Flow<ConfigSettingsEntity>

     val noticeAccount: StateFlow<String?>

     val customerServiceAccount: StateFlow<String?>

     val reportList: StateFlow<List<ReportItem>?>

     val greetingList: StateFlow<List<GreetingItem>?>

     val terms: StateFlow<String?>

     val privacy: StateFlow<String?>

     val childSafe: StateFlow<String?>

     val privateShowMinNum: StateFlow<Int?>

     val privateShowDelayTime: StateFlow<Int?>

     val onlineApiInterval: StateFlow<Long?>
}

@Singleton
class GlobalSettingsMonitorImpl @Inject constructor(
    private val globalApiService: GlobalApiService,
    private val appScope: CoroutineScope,
) : GlobalSettingsMonitor {

    override val settings: Flow<ConfigSettingsEntity> = flow {
        val result = globalApiService.getConfig()
        if (result.isSuccess) {
            result.data?.let { emit(it) }
        }
    }.retryWhen { cause, attempt ->
        delay(1000) //每间隔一秒重试一次, 不限制次数，直到成功
        true
    }.shareIn(
        scope = appScope,
        SharingStarted.Eagerly,
        replay = 1 // 缓存最新的一个值
    )

    override val noticeAccount: StateFlow<String?> = settings.map { it.sysAccount?.noticeAccount }
        .stateIn(appScope, SharingStarted.Eagerly, null)

    override val customerServiceAccount: StateFlow<String?> = settings.map { it.sysAccount?.serviceAccount }
        .stateIn(appScope, SharingStarted.Eagerly, null)

    override val reportList: StateFlow<List<ReportItem>?> = settings.map { it.reportList }
        .stateIn(appScope, SharingStarted.Eagerly, null)

    override val greetingList: StateFlow<List<GreetingItem>?> = settings.map { it.greetingList }
        .stateIn(appScope, SharingStarted.Eagerly, null)

    override val terms: StateFlow<String?> = settings.map { it.html?.terms }
        .stateIn(appScope, SharingStarted.Eagerly, null)

    override val privacy: StateFlow<String?> = settings.map { it.html?.privacy }
        .stateIn(appScope, SharingStarted.Eagerly, null)

    override val childSafe: StateFlow<String?> = settings.map { it.html?.childSafe }
        .stateIn(appScope, SharingStarted.Eagerly, null)

    override val privateShowMinNum: StateFlow<Int?> = settings.map { it.ppvTooltipShowMinNum }
        .stateIn(appScope, SharingStarted.Eagerly, null)

    override val privateShowDelayTime: StateFlow<Int?> = settings.map { it.ppvTooltipShowDelayTime }
        .stateIn(appScope, SharingStarted.Eagerly, null)

    override val onlineApiInterval: StateFlow<Long?> = settings.map { it.onlineApiInterval }
        .stateIn(appScope, SharingStarted.Eagerly, null)
}