package com.flutterup.app.utils

import android.os.Build
import io.rong.imlib.model.Message

object NotificationMessagesUtils {

    private val messagesMap = mutableMapOf<String, MutableList<Message>>()

    fun addMessage(userId: String, message: Message) {
        val list = messagesMap.getOrPut(userId) { mutableListOf() }
        list.add(message)
        // 只保留最近 N 条，避免内存无限增长
        if (list.size > 20) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
                list.removeFirst()
            } else {
                list.removeAt(0)
            }
        }
    }

    fun getMessages(userId: String): List<Message> {
        return messagesMap[userId] ?: emptyList()
    }

    fun clearMessages(userId: String) {
        messagesMap.remove(userId)
    }
}