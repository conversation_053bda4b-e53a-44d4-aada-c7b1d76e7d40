package com.flutterup.app.utils

import android.graphics.Bitmap
import coil3.ImageLoader
import coil3.request.ImageRequest
import coil3.request.SuccessResult
import coil3.request.allowHardware
import coil3.toBitmap
import com.flutterup.base.BaseApplication
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

object CoilUtils {

    suspend fun downloadUrl2Bitmap(url: String): Bitmap? = withContext(Dispatchers.IO) {
        try {
            val loader = ImageLoader(BaseApplication.getApplicationContext())
            val request = ImageRequest.Builder(BaseApplication.getApplicationContext())
                .data(url)
                .allowHardware(false) // 确保能转成 bitmap
                .build()

            val result = loader.execute(request)
            if (result is SuccessResult) {
                val bitmap: Bitmap = result.image.toBitmap()
                bitmap
            } else null
        } catch (_: Exception) {
            null
        }
    }
}