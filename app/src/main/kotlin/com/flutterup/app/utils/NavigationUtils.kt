package com.flutterup.app.utils

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.ViewModel
import androidx.navigation.NavBackStackEntry
import com.flutterup.app.screen.LocalNavController
import kotlin.reflect.KClass

@Composable
inline fun <reified T: ViewModel> NavBackStackEntry.viewModelScopedTo(route: String): T {
    val navController = LocalNavController.current
    val parentEntry = remember(this) { navController.getBackStackEntry(route) }
    return hiltViewModel(parentEntry)
}

@Composable
inline fun <reified T: ViewModel> NavBackStackEntry.viewModelScopedTo(route: KClass<*>): T {
    val navController = LocalNavController.current
    val parentEntry = remember(this) { navController.getBackStackEntry(route) }
    return hiltViewModel(parentEntry)
}

@Composable
inline fun <reified T: ViewModel> NavBackStackEntry.viewModelScopedTo(route: Class<*>): T {
    val navController = LocalNavController.current
    val parentEntry = remember(this) { navController.getBackStackEntry(route) }
    return hiltViewModel(parentEntry)
}