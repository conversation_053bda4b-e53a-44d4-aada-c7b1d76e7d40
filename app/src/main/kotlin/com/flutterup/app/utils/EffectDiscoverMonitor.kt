package com.flutterup.app.utils

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject
import javax.inject.Singleton

data class EffectDiscoverUser(
    val targetId: String,
    val effectType: EffectDiscoverType,
)

enum class EffectDiscoverType {
    Like,

    Pingchat,

    Dislike,

    Report,
}

interface EffectDiscoverMonitor {

    val effect: StateFlow<EffectDiscoverUser?>

    fun postEffect(
        targetId: String,
        effectType: EffectDiscoverType,
    )
}

@Singleton
class EffectDiscoverMonitorImpl @Inject constructor() : EffectDiscoverMonitor {
    private val _effect = MutableStateFlow<EffectDiscoverUser?>(null)

    override val effect: StateFlow<EffectDiscoverUser?> = _effect.asStateFlow()

    override fun postEffect(
        targetId: String,
        effectType: EffectDiscoverType,
    ) {
        val user = EffectDiscoverUser(targetId, effectType)
        _effect.update { user }
    }
}
