package com.flutterup.app.utils

import com.flutterup.app.navigation.GlobalNavCenter
import com.flutterup.base.utils.Timber
import com.flutterup.base.utils.applicationEntryPoint
import com.flutterup.chat.core.ChatException
import com.flutterup.network.ApiException
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineExceptionHandler
import retrofit2.HttpException
import java.io.IOException
import kotlin.coroutines.AbstractCoroutineContextElement
import kotlin.coroutines.CoroutineContext

/**
 * 全局协程异常处理器
 */
object GlobalExceptionHandler
    : AbstractCoroutineContextElement(CoroutineExceptionHandler.Key), CoroutineExceptionHandler {

    private const val TAG = "GlobalExceptionHandler"

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface CoroutineExceptionHandlerEntryPoint {
        fun userRepository(): UserMonitor

        fun navCenter(): GlobalNavCenter
    }

    private val userMonitor: UserMonitor by lazy {
        applicationEntryPoint<CoroutineExceptionHandlerEntryPoint>().userRepository()
    }

    private val navCenter: GlobalNavCenter by lazy {
        applicationEntryPoint<CoroutineExceptionHandlerEntryPoint>().navCenter()
    }

    override fun handleException(context: CoroutineContext, exception: Throwable) {
        when (exception) {
            is ApiException -> {
                // 处理自定义的网络异常
                Timber.e(TAG, "Caught ApiException ", exception)

                if (exception.code == 1002) {
                    Timber.showToast("Authentication failed, please log in again.")
                    userMonitor.logout(isExpired = true)
                } else {
                    if (!exception.message.isNullOrEmpty()) {
                        Timber.showToast(exception.message)
                    }
                }
            }
            is HttpException -> {
                Timber.e(TAG, "Caught HttpException ", exception)
                Timber.showToast("Network Error")
            }
            is IOException -> {
                Timber.e(TAG, "Caught IOException ", exception)
                Timber.showToast("Network Error")
            }
            is ChatException -> {
                Timber.e(TAG, "Caught ChatException ", exception)

                if (exception.message != null) {
                    Timber.showToast(exception.message)
                }
            }
            else -> {
                // 处理其他未捕获的异常
                Timber.e(TAG, "Caught unexpected exception ", exception)
            }
        }
    }
}