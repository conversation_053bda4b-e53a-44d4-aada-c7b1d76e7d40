package com.flutterup.app.utils

import com.flutterup.app.network.ApiService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AppStatusMonitorImpl @Inject constructor(
    private val apiService: ApiService,
    private val scope: CoroutineScope,
) : AppStatusMonitor {

    @Inject
    lateinit var userMonitor: UserMonitor

    override suspend fun onAppStatusChanged(status: AppStatus): Boolean {
        if (!userMonitor.isLogin) return false

        try {
            val reportResult = apiService.reportAppStatus(status.value)
            return reportResult.isSuccess
        } catch (_: Exception) {
            return false
        }
    }

    override fun onAppStatusChangedSync(status: AppStatus, callback: (Boolean) -> Unit) {
        scope.launch {
            val result = onAppStatusChanged(status)
            callback(result)
        }
    }
}


interface AppStatusMonitor {
    suspend fun onAppStatusChanged(status: AppStatus): Boolean

    fun onAppStatusChangedSync(status: AppStatus, callback: (Boolean) -> Unit = {})
}

enum class AppStatus(val value: Int) {
    BACKGROUND(0),

    FOREGROUND(1),

    LOGOUT(4),
}