package com.flutterup.app.utils.chat

import com.flutterup.app.network.ApiService
import com.flutterup.app.utils.UserMonitor
import com.flutterup.app.utils.chat.core.ChatMessageExpansionReceiverMonitor
import com.flutterup.app.utils.chat.core.ChatMessageMonitor
import com.flutterup.app.utils.chat.core.ChatRecallMonitor
import com.flutterup.app.utils.chat.core.ChatUnreadMonitor
import com.flutterup.base.utils.Timber
import io.rong.imlib.RongIMClient
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * IM连接监控器
 *
 * 功能：
 * 1. 自动监听用户登录状态，登录后自动连接IM，退出登录后自动断开IM连接
 * 2. 提供IM连接状态的状态流，供其他组件观察
 * 3. 支持手动连接和断开IM
 *
 * 使用示例：
 * ```kotlin
 * @Inject lateinit var chatMonitor: ChatMonitor
 *
 * // 观察连接状态
 * chatMonitor.connectState.collect { state ->
 *     when (state) {
 *         ChatConnectState.CONNECT_SUCCESS -> {
 *             // IM连接成功，可以开始聊天
 *         }
 *         ChatConnectState.UNCONNECTED -> {
 *             // IM已断开
 *         }
 *         // 其他状态...
 *     }
 * }
 *
 * // 手动操作（一般不需要，会自动管理）
 * chatMonitor.connect()
 * chatMonitor.disconnect()
 * ```
 */
interface ChatMonitor {
    /**
     * IM连接状态流
     */
    val connectState: StateFlow<ChatConnectState>

    /**
     * 手动连接IM
     */
    suspend fun connect()

    /**
     * 手动断开IM连接
     */
    suspend fun disconnect()

    /**
     * 启动自动监听用户状态
     */
    fun startMonitoring()

    /**
     * 停止监听
     */
    fun stopMonitoring()


    val messageMonitor: ChatMessageMonitor

    val unreadMonitor: ChatUnreadMonitor

    val expansionReceiverMonitor: ChatMessageExpansionReceiverMonitor

    val recallMonitor: ChatRecallMonitor
}

@Singleton
class ChatMonitorImpl @Inject constructor(
    private val userMonitor: UserMonitor,
    private val appScope: CoroutineScope,
    private val apiService: ApiService,

    //basic monitor
    override val messageMonitor: ChatMessageMonitor,
    override val unreadMonitor: ChatUnreadMonitor,
    override val expansionReceiverMonitor: ChatMessageExpansionReceiverMonitor,
    override val recallMonitor: ChatRecallMonitor,
) : ChatMonitor {

    companion object {
        private const val TAG = "ChatMonitor"
    }

    private val _connectState = MutableStateFlow(ChatConnectState.UNCONNECTED)
    override val connectState: StateFlow<ChatConnectState> = _connectState.asStateFlow()

    private var monitoringJob: Job? = null
    private var isMonitoring = false

    init {
        // 自动开始监听
        startMonitoring()
    }

    override fun startMonitoring() {
        if (isMonitoring) return

        isMonitoring = true
        monitoringJob = appScope.launch {
            // 监听用户信息状态变化
            userMonitor.userInfoState.collect { userInfo ->
                if (userInfo != null && userMonitor.isLogin && !userMonitor.imToken.isNullOrEmpty()) {
                    // 用户已登录且有IM token，自动连接
                    if (_connectState.value.isAtMost(ChatConnectState.CONNECT_FAILED)) {
                        connect()
                    }
                } else {
                    // 用户未登录或没有IM token，断开连接
                    if (_connectState.value != ChatConnectState.UNCONNECTED) {
                        disconnect()
                    }
                }
            }
        }
    }

    override fun stopMonitoring() {
        isMonitoring = false
        monitoringJob?.cancel()
        monitoringJob = null
    }

    override suspend fun connect() {
        Timber.d(TAG, "connect: currentState=${_connectState.value}")

        if (_connectState.value.isAtLeast(ChatConnectState.CONNECTING)) return

        _connectState.value = ChatConnectState.CONNECTING

        RongIMClient.connect(userMonitor.imToken, object : RongIMClient.ConnectCallback() {
            override fun onSuccess(t: String?) {
                // 连接成功
                _connectState.value = ChatConnectState.CONNECT_SUCCESS
                //开启监听
                messageMonitor.connect()
                unreadMonitor.connect()
                expansionReceiverMonitor.connect()
                recallMonitor.connect()
            }

            override fun onError(e: RongIMClient.ConnectionErrorCode?) {
                _connectState.value = ChatConnectState.CONNECT_FAILED

                if (e == RongIMClient.ConnectionErrorCode.RC_CONN_TOKEN_INCORRECT) {
                    refreshToken()
                }
            }

            override fun onDatabaseOpened(code: RongIMClient.DatabaseOpenStatus?) {
            }
        })
    }

    override suspend fun disconnect() {
        Timber.d(TAG, "disconnect: currentState=${_connectState.value}")

        //断开连接前，先断开监听
        messageMonitor.disconnect()
        unreadMonitor.disconnect()
        expansionReceiverMonitor.disconnect()
        recallMonitor.disconnect()

        //断开连接
        RongIMClient.getInstance().logout()
        _connectState.value = ChatConnectState.UNCONNECTED
    }

    private fun refreshToken() {
        appScope.launch {
            val result = apiService.getMineProfileInfo()
            if (result.isSuccess) {
                userMonitor.updateUserInfo(result.data)
            }
        }
    }
}