package com.flutterup.app.utils.chat.core.internal

import com.flutterup.app.model.UserNotificationEntity
import com.flutterup.app.utils.GlobalSettingsMonitor
import com.flutterup.app.utils.NotificationUtils
import com.flutterup.app.utils.UserMonitor
import com.flutterup.app.utils.chat.core.ChatOnReceivedMessageListener
import com.flutterup.chat.message.content.ConnectBaseMessageContent
import com.flutterup.network.AppDispatchers
import com.flutterup.network.Dispatcher
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.imlib.model.ReceivedProfile
import io.rong.message.RecallCommandMessage
import io.rong.message.RecallNotificationMessage
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton


interface ChatUserMessageMonitor : ChatOnReceivedMessageListener

@Singleton
class ChatUserMessageMonitorImpl @Inject constructor(
    private val settingsMonitor: GlobalSettingsMonitor,
    private val appScope: CoroutineScope,
    private val userMonitor: UserMonitor,
    @Dispatcher(AppDispatchers.Main) private val dispatcher: CoroutineDispatcher,
) : ChatUserMessageMonitor {


    override fun onReceivedMessage(
        message: Message?,
        profile: ReceivedProfile?
    ) {
        if (!userMonitor.isPushConfigEnable(UserNotificationEntity.SETTINGS_NEW_MESSAGE_ALERT)) return //没有开启新消息通知

        if (message == null) return

        if (message.messageDirection == Message.MessageDirection.SEND) return //自己发的消息不显示
        if (message.conversationType != Conversation.ConversationType.PRIVATE) return //不显示非私聊消息
        if (message.content is ConnectBaseMessageContent) return //不显示建联消息
        if (message.content is RecallCommandMessage || message.content is RecallNotificationMessage) return //不显示撤回消息

        appScope.launch(dispatcher) {
            handleMessage(message)
        }
    }

    private suspend fun handleMessage(message: Message) {
        if (message.senderUserId == settingsMonitor.customerServiceAccount.value) return //不显示客服消息
        if (message.senderUserId == settingsMonitor.noticeAccount.value) return //不显示通知消息

        NotificationUtils.showChatNotification(message)
    }
}