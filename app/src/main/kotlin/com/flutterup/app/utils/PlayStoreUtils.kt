package com.flutterup.app.utils

import android.content.ActivityNotFoundException
import android.content.Intent
import android.net.Uri
import androidx.core.net.toUri
import com.flutterup.base.BaseApplication
import com.flutterup.base.utils.Timber

object PlayStoreUtils {


    /**
     * 跳转Google Play更新
     */
    fun gotoPlayStoreWithApp() {
        try {
            val appPackageName = BaseApplication.getApplicationContext().packageName
            val marketIntent = Intent(Intent.ACTION_VIEW, "market://details?id=$appPackageName".toUri())
            marketIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

            // 尝试通过Google Play应用打开
            BaseApplication.getApplicationContext().startActivity(marketIntent)
        } catch (e: Exception) {
            // 如果没有安装Google Play或者出现其他异常，则通过浏览器打开Google Play网页
            try {
                val appPackageName = BaseApplication.getApplicationContext().packageName
                val webIntent = Intent(Intent.ACTION_VIEW, "https://play.google.com/store/apps/details?id=$appPackageName".toUri())
                webIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                BaseApplication.getApplicationContext().startActivity(webIntent)
            } catch (e: Exception) {
                Timber.e("PlayStoreUtils", "open play store failed: ${e.message}")
            }
        }
    }

    fun gotoGooglePlay() {
        val marketUri = "market://store".toUri() // Google Play 首页
        val marketIntent = Intent(Intent.ACTION_VIEW, marketUri).apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }

        try {
            BaseApplication.getApplicationContext().startActivity(marketIntent)
        } catch (e: ActivityNotFoundException) {
            // 如果设备没有 Play 商店，回退到网页版首页
            val webUri = "https://play.google.com/store".toUri()
            val webIntent = Intent(Intent.ACTION_VIEW, webUri).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            BaseApplication.getApplicationContext().startActivity(webIntent)
        }
    }

}