package com.flutterup.app.utils.chat.core

import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Message
import io.rong.message.RecallNotificationMessage
import javax.inject.Inject
import javax.inject.Singleton


fun interface OnRecallMessageListener {
    fun onMessageRecalled(message: Message?, recallNotificationMessage: RecallNotificationMessage?)
}

interface ChatRecallMonitor : ChatBasicMonitor {

    fun addListener(listener: OnRecallMessageListener)

    fun removeListener(listener: OnRecallMessageListener)
}

@Singleton
class ChatRecallMonitorImpl @Inject constructor() : ChatRecallMonitor, RongIMClient.OnRecallMessageListener {
    private val listeners = mutableSetOf<OnRecallMessageListener>()

    override fun addListener(listener: OnRecallMessageListener) {
        if (listeners.contains(listener)) return
        listeners.add(listener)
    }

    override fun removeListener(listener: OnRecallMessageListener) {
        if (!listeners.contains(listener)) return
        listeners.remove(listener)
    }

    override fun connect() {
        RongIMClient.setOnRecallMessageListener(this)
    }

    override fun disconnect() {
        RongIMClient.setOnRecallMessageListener(null)
    }

    override fun onMessageRecalled(
        message: Message?,
        recallNotificationMessage: RecallNotificationMessage?
    ): Boolean {
        listeners.forEach { it.onMessageRecalled(message, recallNotificationMessage) }
        return true
    }
}