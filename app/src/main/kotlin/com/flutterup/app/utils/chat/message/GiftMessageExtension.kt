package com.flutterup.app.utils.chat.message

import io.rong.imlib.model.Message


enum class UnwrapStatus(val value: Int) {
    UNWRAPPED(0), // 未拆开
    WRAPPED(1); // 已拆开

    companion object {
        fun valueOf(value: Int): UnwrapStatus {
            return entries.firstOrNull { it.value == value } ?: UNWRAPPED
        }
    }
}

fun Message.unwrapStatus(): UnwrapStatus {
    val status = expansion?.get(KEY_UNWRAP_STATUS)?.toIntOrNull() ?: return UnwrapStatus.UNWRAPPED

    return UnwrapStatus.valueOf(status)
}

private const val KEY_UNWRAP_STATUS = "status"