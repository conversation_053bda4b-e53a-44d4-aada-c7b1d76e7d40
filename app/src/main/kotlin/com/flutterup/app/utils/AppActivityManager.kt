package com.flutterup.app.utils

import android.app.Activity
import android.app.Application
import android.os.Bundle
import java.lang.ref.WeakReference

object AppActivityManager : Application.ActivityLifecycleCallbacks {

    // 使用 WeakReference 避免内存泄漏
    private var topActivityRef: WeakReference<Activity>? = null

    val topActivity: Activity? get() = topActivityRef?.get()


    override fun onActivityCreated(activity: Activity, bundle: Bundle?) {
    }

    override fun onActivityStarted(activity: Activity) {
    }

    override fun onActivityResumed(activity: Activity) {
        // 更新栈顶 Activity 引用
        topActivityRef = WeakReference(activity)
    }

    override fun onActivityPaused(activity: Activity) {
    }

    override fun onActivityStopped(activity: Activity) {
    }

    override fun onActivitySaveInstanceState(activity: Activity, bundle: Bundle) {
    }

    override fun onActivityDestroyed(activity: Activity) {
        // 如果被销毁的是栈顶 Activity，清除引用
        if (topActivityRef?.get() == activity) {
            topActivityRef = null
        }
    }

}