package com.flutterup.app.utils.chat.message

import com.flutterup.app.R
import com.flutterup.base.BaseApplication
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.model.Message


const val CHAT_SEND_ERROR_KEY = "error"
const val CHAT_SEND_BLOCK_BY_SERVER = "data"

fun Message.isSendError(): <PERSON><PERSON><PERSON> {
    return sentStatus == Message.SentStatus.FAILED
}

/**
 * @see [io.rong.imlib.IRongCoreEnum.CoreErrorCode]
 */
fun Message.sendErrorReason(): String? {
    val code: Int = expansion?.get(CHAT_SEND_ERROR_KEY)?.toIntOrNull() ?: return null

    return when(code) {

        IRongCoreEnum.CoreErrorCode.REJECTED_BY_BLACKLIST.code -> {
            BaseApplication.getApplicationContext().getString(R.string.message_being_blocked)
        }

        else -> null
    }
}

/**
 * 是否是被服务器拒绝发送, 只要不为空，就必定是被拦截了
 */
fun Message.isChatWithoutLimit(): Boolean {
    val data = expansion?.get(CHAT_SEND_BLOCK_BY_SERVER)
    return data != null && data.isNotEmpty()
}