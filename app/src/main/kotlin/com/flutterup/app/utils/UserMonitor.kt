package com.flutterup.app.utils

import com.flutterup.app.model.Gender
import com.flutterup.app.model.IUserInfo
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.model.UserInfo
import com.flutterup.app.model.UserRightsEntity
import com.flutterup.app.navigation.GlobalNavCenter
import com.flutterup.base.store.MMKVStoreImpl
import com.flutterup.base.store.jsonValue
import com.flutterup.base.store.nullableStringValue
import com.flutterup.base.utils.JsonUtils
import com.flutterup.base.utils.DateUtils
import com.flutterup.base.utils.applicationEntryPoint
import com.tencent.mmkv.MMKV
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserMonitor @Inject constructor(
    private val jsonUtils: JsonUtils,
    private val appScope: CoroutineScope
) : IUserInfo {


    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface UserRepositoryEntryPoint {
        fun navCenter(): GlobalNavCenter

        fun appStatusMonitor(): AppStatusMonitor
    }

    private val navCenter: GlobalNavCenter by lazy {
        applicationEntryPoint<UserRepositoryEntryPoint>().navCenter()
    }

    private val appStatusMonitor: AppStatusMonitor by lazy {
        applicationEntryPoint<UserRepositoryEntryPoint>().appStatusMonitor()
    }

    private val mmkvStore = MMKVStoreImpl(mmkv = MMKV.mmkvWithID("user"))

    private var _userInfo: UserInfo? by mmkvStore.jsonValue<UserInfo>("userInfo", jsonUtils = jsonUtils)

    private var _token: String? by mmkvStore.nullableStringValue("token")

    private var _imToken: String? by mmkvStore.nullableStringValue("imToken")

    private val _userInfoState = MutableStateFlow(_userInfo)

    /** 用户信息状态流 */
    val userInfoState: StateFlow<UserInfo?> = _userInfoState.asStateFlow()

    /** 当前用户信息 */
    val userInfo: UserInfo? get() = _userInfo

    /** 是否登录 */
    val isLogin: Boolean get() = userId != null && token != null

    /**
     * 是否是无效用户, 是否需要重新引导用户走一遍信息设置
     * 1. 登陆了
     * 2. 没有昵称
     */
    val isInvalid: Boolean get() = isLogin && _userInfo?.isInvalid == true

    /** 是否是VIP */
    val isVip: Boolean get() = right?.vip == 1

    /** 性别 */
    val gender: Gender get() = Gender.fromValue(sex)

    /** 性取向 */
    val orientationGender: Gender get() = Gender.fromValue(sexuality)

    /** 生日字符串 */
    val birthdayFormatted: String get() = birthday?.let { DateUtils.formatBirthday(it) } ?: ""

    // 委托给userInfo的属性
    override val userId: String? get() = _userInfo?.userId
    override val token: String? get() = _token
    override val imToken: String? get() = _imToken
    override val nickname: String? get() = _userInfo?.nickname
    override val headImage: String? get() = _userInfo?.headImage
    override val isNewUser: Int? get() = _userInfo?.isNewUser
    override val isModel: Boolean? get() = _userInfo?.isModel
    override val sign: String? get() = _userInfo?.sign
    override val sex: Int? get() = _userInfo?.sex
    override val sexuality: Int? get() = _userInfo?.sexuality
    override val age: Int? get() = _userInfo?.age
    override val isHide: Int? get() = _userInfo?.isHide
    override val birthday: Long? get() = _userInfo?.birthday
    override val mediaList: List<MediaItemEntity>? get() = _userInfo?.mediaList
    override val tags: List<String>? get() = _userInfo?.tags
    override val online: Int? get() = _userInfo?.online
    override val right: IUserInfo.Right? get() = _userInfo?.right
    override val scene: Int? get() = _userInfo?.scene
    override val userFlag: Int? get() = _userInfo?.userFlag
    override val pushConfig: Int? get() = _userInfo?.pushConfig
    override val location: Int? get() = _userInfo?.location
    override val refer: Int? get() = _userInfo?.refer

    /**
     * 更新用户信息
     */
    fun updateUserInfo(userInfo: UserInfo?) {
        if (_token.isNullOrEmpty() || userInfo?.token.isValidToken()) {
            _token = userInfo?.token
        }

        if (_imToken.isNullOrEmpty() || userInfo?.imToken.isValidToken()) {
            _imToken = userInfo?.imToken
        }

        _userInfo = userInfo
        _userInfoState.update { userInfo }
    }

    fun updateUserRight(right: UserRightsEntity) {
        _userInfo = _userInfo?.copy(right = right)
        _userInfoState.update { _userInfo }
    }

    fun updatePushConfig(pushConfig: Int) {
        _userInfo = _userInfo?.copy(pushConfig = pushConfig)
        _userInfoState.update { _userInfo }
    }

    /**
     * 登出
     */
    fun logout(isExpired: Boolean = false) {
        appScope.launch {
            if (isExpired) {
                logoutLogic()
            } else {
                val reportResult = appStatusMonitor.onAppStatusChanged(AppStatus.LOGOUT)
                if (reportResult) {
                    logoutLogic()
                }
            }
        }
    }

    fun isSelf(userId: String?): Boolean {
        return this.userId == userId
    }

    fun isPushConfigEnable(key: Int): Boolean {
        return pushConfig?.and(key) != 0
    }

    private suspend fun logoutLogic() {
        withContext(Dispatchers.IO) {
            mmkvStore.clear()
        }

        withContext(Dispatchers.Main) {
            navCenter.navigateLogin()
        }
    }

    private fun String?.isValidToken(): Boolean {
        return !this.isNullOrEmpty()
    }
}