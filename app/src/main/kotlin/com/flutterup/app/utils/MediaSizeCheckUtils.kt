package com.flutterup.app.utils

import android.content.Context
import android.media.MediaMetadataRetriever
import android.net.Uri
import com.flutterup.base.utils.Timber

object MediaSizeCheckUtils {
    //图片默认最大大小
    const val PICTURE_MAX_SIZE = 3 * 1024 * 1024L
    //视频默认最大大小
    const val VIDEO_MAX_SIZE = 50 * 1024 * 1024L
    //视频默认最短时长, 单位秒
    const val VIDEO_MIN_DURATION = 1
    //视频默认最长时长, 单位秒
    const val VIDEO_MAX_DURATION = 60

    fun sizeCheck(context: Context, uri: Uri?): Boolean {
        if (uri == null) return false

        try {
            val mimeType = context.contentResolver.getType(uri) ?: return false
            val isImage = mimeType.startsWith("image/")
            val isVideo = mimeType.startsWith("video/")

            val fileSize = getFileSize(context, uri)

            if (isImage) {
                if (fileSize > PICTURE_MAX_SIZE) {
                    Timber.showToast("Maximum photo size: 3MB.")
                    return false
                }
            }

            if (isVideo) {
                if (fileSize > VIDEO_MAX_SIZE) {
                    Timber.showToast("Maximum video size: 50MB.")
                    return false
                }

                val retriever = MediaMetadataRetriever()
                retriever.setDataSource(context, uri)
                val durationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
                val duration = durationStr?.toLongOrNull() ?: 0L
                val durationInSeconds = duration / 1000
                retriever.release()

                if (durationInSeconds < VIDEO_MIN_DURATION || durationInSeconds > VIDEO_MAX_DURATION) {
                    Timber.showToast("Video length limit: 1-60 seconds.")
                    return false
                }
            }

            return true
        } catch (e: Exception) {
            Timber.d("Media size check error: ${e.message}")
            return false
        }
    }

    private fun getFileSize(context: Context, uri: Uri): Long {
        val fileDescriptor = context.contentResolver.openFileDescriptor(uri, "r")
        val fileSize = fileDescriptor?.statSize ?: 0L
        fileDescriptor?.close()
        return fileSize
    }
}