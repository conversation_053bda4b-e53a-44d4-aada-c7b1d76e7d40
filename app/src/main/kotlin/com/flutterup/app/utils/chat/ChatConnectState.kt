package com.flutterup.app.utils.chat

enum class ChatConnectState {
    /**
     * 未连接
     */
    UNCONNECTED,

    /**
     * 连接失败
     */
    CONNECT_FAILED,

    /**
     * 连接中
     */
    CONNECTING,

    /**
     * 连接成功
     */
    CONNECT_SUCCESS;

    fun isAtLeast(state: ChatConnectState): Boolean {
        return compareTo(state) >= 0
    }

    fun isAtMost(state: ChatConnectState): <PERSON><PERSON><PERSON> {
        return compareTo(state) <= 0
    }
}