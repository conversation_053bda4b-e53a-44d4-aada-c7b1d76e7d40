package com.flutterup.app.utils.chat.message

import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.model.MediaMessageEntity
import com.flutterup.app.model.MultiMediaMessageEntity
import com.flutterup.chat.message.content.BaseMessageType
import com.flutterup.chat.message.content.MultiPrivateMessageContent
import com.flutterup.chat.message.content.PrivateMessageContent
import com.flutterup.chat.message.content.PublicMessageContent
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent
import io.rong.message.ImageMessage
import io.rong.message.SightMessage
import java.lang.Exception


enum class PrivateMessageLockStatus(val value: Int) {
    LOCKED(0), // 未解锁
    OPENED(1), // 已解锁
    EXPIRED(2); // 已过期

    companion object {

        fun valueOf(value: Int): PrivateMessageLockStatus {
            return entries.firstOrNull { it.value == value } ?: LOCKED
        }
    }
}

/**
 * 解锁状态
 */
fun Message.lockStatus(): PrivateMessageLockStatus {
    val content = content ?: return PrivateMessageLockStatus.LOCKED

    if (content !is BaseMessageType.Private) {
        return PrivateMessageLockStatus.LOCKED
    }

    val status = expansion?.get(KEY_LOCK_STATUS)?.toIntOrNull() ?: return PrivateMessageLockStatus.LOCKED

    return PrivateMessageLockStatus.valueOf(status)
}

fun Message.deleteTimeMillis(): Long {
    val content = content ?: return 0L

    if (content !is BaseMessageType.Private) {
        return 0L
    }

    val deleteTime = expansion?.get(KEY_DELETE_TIME)?.toLongOrNull() ?: return 0L

    return deleteTime * 1000 //默认的时间是秒制
}

fun Message.getMediaList(): List<MediaItemEntity>? {
    val content = content ?: return null

    if (content !is ImageMessage
        && content !is SightMessage
        && content !is PublicMessageContent
        && content !is BaseMessageType.Private
    ) {
        return null
    }

    return try {
        MediaGlobalCache.getOrPut(messageId) { realMediaList(content) }
    } catch (_: Exception) {
        null
    }
}



private const val KEY_LOCK_STATUS = "status"
private const val KEY_DELETE_TIME = "del_time"

private val MediaGlobalCache = mutableMapOf<Int, List<MediaItemEntity>>()

private fun realMediaList(content: MessageContent): List<MediaItemEntity> {
    return when(content) {
        is ImageMessage -> listOf(MediaItemEntity(MediaItemEntity.TYPE_IMAGE, content.mediaUrl.toString()))
        is SightMessage -> listOf(MediaItemEntity(MediaItemEntity.TYPE_VIDEO, content.mediaUrl.toString(), thumbUrl = content.thumbUri.toString()))

        is PublicMessageContent -> {
            val entity = content.get(MediaMessageEntity::class.java) ?: return emptyList()
            listOf(MediaItemEntity(entity.type, entity.url, thumbUrl = entity.thumbUrl))
        }
        is PrivateMessageContent -> {
            val entity = content.get(MediaMessageEntity::class.java) ?: return emptyList()
            listOf(MediaItemEntity(entity.type, entity.url, thumbUrl = entity.thumbUrl))
        }
        is MultiPrivateMessageContent -> {
            val entity = content.get(MultiMediaMessageEntity::class.java) ?: return emptyList()
            entity.entities.map { MediaItemEntity(it.type, it.url, thumbUrl = it.thumbUrl) }
        }

        else -> emptyList()
    }
}
