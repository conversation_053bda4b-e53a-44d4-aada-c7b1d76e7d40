package com.flutterup.app.utils.chat

import com.flutterup.app.model.UserInfo
import com.flutterup.app.network.ApiService
import com.flutterup.app.utils.GlobalExceptionHandler
import com.flutterup.base.utils.applicationEntryPoint
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

object ChatUserCache {

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface ChatUserCacheEntryPoint {
        fun apiService(): ApiService
    }

    private val userMap = mutableMapOf<String, UserInfo>()

    private val apiService: ApiService by lazy {
        applicationEntryPoint<ChatUserCacheEntryPoint>().apiService()
    }

    fun getUserInfo(userId: String): UserInfo? {
        return userMap[userId]
    }

    fun putUserInfo(userId: String, userInfo: UserInfo?) {
        if (userInfo == null) {
            userMap.remove(userId)
            return
        }
        userMap[userId] = userInfo
    }

    suspend fun getUserInfoOrFetch(userId: String): UserInfo? {
        return getUserInfo(userId) ?: fetchUserInfo(userId)
    }

    suspend fun fetchUserInfo(userId: String): UserInfo? = withContext(Dispatchers.IO) {
        try {
            val result = apiService.getAnotherProfileInfoList(userId)
            if (result.isSuccess) {
                val userinfo = result.data?.firstOrNull()
                putUserInfo(userId, userinfo)
                return@withContext userinfo
            }
        } catch (e: Exception) {
            GlobalExceptionHandler.handleException(coroutineContext, e)
        }
        return@withContext null
    }
}