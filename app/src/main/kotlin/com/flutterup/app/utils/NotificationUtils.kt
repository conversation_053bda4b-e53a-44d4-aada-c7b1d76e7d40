package com.flutterup.app.utils

import android.Manifest
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.content.Context.NOTIFICATION_SERVICE
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.app.Person
import androidx.core.graphics.drawable.IconCompat
import com.flutterup.app.R
import com.flutterup.app.model.SystemMessageEntity
import com.flutterup.app.navigation.GlobalNavCenter
import com.flutterup.app.utils.chat.ChatMessageUtils
import com.flutterup.app.utils.chat.ChatUserCache
import com.flutterup.base.BaseApplication
import com.flutterup.base.utils.applicationEntryPoint
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.rong.imlib.model.Message
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object NotificationUtils {

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface NotificationUtilsEntryPoint {
        fun globalNavCenter(): GlobalNavCenter
    }

    private val navCenter: GlobalNavCenter by lazy {
        applicationEntryPoint<NotificationUtilsEntryPoint>().globalNavCenter()
    }


    fun openNotificationSettings(context: Context) {
        try {
            // Android 8.0 及以上
            val intent = Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS).apply {
                putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
            }
            context.startActivity(intent)
        } catch (_: Exception) {
            // 如果异常，兜底还是跳到应用详情页
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", context.packageName, null)
            }
            context.startActivity(intent)
        }
    }

    /**
     * 展示系统通知
     */
    fun showNotification(systemMessageEntity: SystemMessageEntity) {
        BaseApplication.getApplicationScope().launch {
            val icon = CoilUtils.downloadUrl2Bitmap(systemMessageEntity.headimg.orEmpty()) ?: return@launch

            withContext(Dispatchers.Main) {
                val id = System.currentTimeMillis().hashCode()
                val intent = navCenter.createPendingIntent(id, systemMessageEntity)
                val notification = NotificationCompat.Builder(BaseApplication.getApplicationContext(), CHANNEL_ID)
                    .setContentTitle(systemMessageEntity.nickname)
                    .setContentText(systemMessageEntity.msg)
                    .setSmallIcon(R.mipmap.ic_app_logo)
                    .setLargeIcon(icon)
                    .setPriority(NotificationCompat.PRIORITY_HIGH)
                    .setContentIntent(intent)
                    .setAutoCancel(true)
                    .build()

                with(NotificationManagerCompat.from(BaseApplication.getApplicationContext())) {

                    if (ActivityCompat.checkSelfPermission(
                            BaseApplication.getApplicationContext(),
                            Manifest.permission.POST_NOTIFICATIONS
                        ) != PackageManager.PERMISSION_GRANTED
                    ) {
                        //没有通知权限
                        return@with
                    }
                    notify(id, notification)
                }
            }
        }
    }

    /**
     * 展示聊天通知
     */
    fun showChatNotification(message: Message) {
        BaseApplication.getApplicationScope().launch {
            val userinfo = ChatUserCache.getUserInfoOrFetch(message.targetId) ?: return@launch
            val icon = CoilUtils.downloadUrl2Bitmap(userinfo.headImage.orEmpty()) ?: return@launch
            val iconCompat = IconCompat.createWithBitmap(icon)

            val person = Person.Builder()
                .setName(userinfo.nickname)
                .setIcon(iconCompat)
                .setKey(userinfo.userId)
                .build()
            //添加消息到缓存
            NotificationMessagesUtils.addMessage(message.targetId, message)
            val messages = NotificationMessagesUtils.getMessages(message.targetId)

            withContext(Dispatchers.Main) {
                val id = message.senderUserId.hashCode()

                val messagingStyle = NotificationCompat.MessagingStyle(person)
                messages.forEach {
                    messagingStyle.addMessage(ChatMessageUtils.convert(it), it.sentTime, person)
                }
                val intent = navCenter.createMessagePendingIntent(id, message)

                val notification = NotificationCompat.Builder(BaseApplication.getApplicationContext(), CHAT_CHANNEL_ID)
                    .setSmallIcon(R.mipmap.ic_app_logo)
                    .setLargeIcon(icon)
                    .setStyle(messagingStyle)
                    .setPriority(NotificationCompat.PRIORITY_HIGH)
                    .setContentIntent(intent)
                    .setAutoCancel(true)
                    .build()

                with(NotificationManagerCompat.from(BaseApplication.getApplicationContext())) {
                    if (ActivityCompat.checkSelfPermission(
                            BaseApplication.getApplicationContext(),
                            Manifest.permission.POST_NOTIFICATIONS
                        ) != PackageManager.PERMISSION_GRANTED
                    ) {
                        //没有通知权限
                        return@with
                    }
                    notify(id, notification)
                }
            }
        }
    }


    /**
     * 紧急
     * ：发出提示音，并以浮动通知的形式显示。	IMPORTANCE_HIGH
     * 高
     * ：发出提示音。	IMPORTANCE_DEFAULT
     * 中
     * ：不发出提示音。	IMPORTANCE_LOW
     * 低
     * ：不发出提示音，且不会在状态栏中显示。	IMPORTANCE_MIN
     * 无
     * ：不发出提示音，也不会在状态栏或通知栏中显示。	IMPORTANCE_NONE
     */
    fun createNotificationChannel() {
        val notificationManager = BaseApplication.getApplicationContext().getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.createSystemNotification()
        notificationManager.createChatNotification()
    }


    private fun NotificationManager.createSystemNotification() {
        val importance = NotificationManager.IMPORTANCE_HIGH
        val mChannel = NotificationChannel(CHANNEL_ID, CHANNEL_NAME, importance)
        mChannel.description = CHANNEL_DESCRIPTION

        this.createNotificationChannel(mChannel)
    }

    private fun NotificationManager.createChatNotification() {
        val importance = NotificationManager.IMPORTANCE_HIGH
        val mChannel = NotificationChannel(CHAT_CHANNEL_ID, CHAT_CHANNEL_NAME, importance)
        mChannel.description = CHAT_CHANNEL_DESCRIPTION

        this.createNotificationChannel(mChannel)
    }
}

private const val CHANNEL_NAME = "Notification"
private const val CHANNEL_DESCRIPTION = "FlutterUp Notification Channel"
private const val CHANNEL_ID = "flutterup_channel"

private const val CHAT_CHANNEL_NAME = "Chat Notification"
private const val CHAT_CHANNEL_DESCRIPTION = "FlutterUp Chat Channel"
private const val CHAT_CHANNEL_ID = "flutterup_chat_channel"
