package com.flutterup.app.utils

import android.app.Activity
import com.google.android.play.core.review.ReviewManagerFactory

object ReviewUtils {

    fun startReview(activity: Activity) {
        val manager = ReviewManagerFactory.create(activity.applicationContext)
        val request = manager.requestReviewFlow()
        request.addOnCompleteListener { task ->
            if (task.isSuccessful) {
                val reviewInfo = task.result
                val flow = manager.launchReviewFlow(activity, reviewInfo)
                flow.addOnCompleteListener {
                }
            }
        }
    }
}