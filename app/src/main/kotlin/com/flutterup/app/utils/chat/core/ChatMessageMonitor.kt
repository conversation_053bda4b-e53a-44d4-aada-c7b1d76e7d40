package com.flutterup.app.utils.chat.core

import com.flutterup.app.utils.chat.core.internal.ChatDataSyncMonitor
import com.flutterup.app.utils.chat.core.internal.ChatSystemMessageMonitor
import com.flutterup.app.utils.chat.core.internal.ChatUserMessageMonitor
import io.rong.imlib.RongCoreClient
import io.rong.imlib.listener.OnReceiveMessageWrapperListener
import io.rong.imlib.model.Message
import io.rong.imlib.model.ReceivedProfile
import javax.inject.Inject
import javax.inject.Singleton

fun interface ChatOnReceivedMessageListener {
    fun onReceivedMessage(message: Message?, profile: ReceivedProfile?)
}

interface ChatMessageMonitor : ChatBasicMonitor {

    fun addMessageListener(listener: ChatOnReceivedMessageListener)

    fun removeMessageListener(listener: ChatOnReceivedMessageListener)
}

@Singleton
class ChatMessageMonitorImpl @Inject constructor(
    private val dataSyncMonitor: ChatDataSyncMonitor,
    private val systemMessageMonitor: ChatSystemMessageMonitor,
    private val userMessageMonitor: ChatUserMessageMonitor,
) : ChatMessageMonitor, OnReceiveMessageWrapperListener() {
    private val listeners = mutableSetOf<ChatOnReceivedMessageListener>()

    override fun addMessageListener(listener: ChatOnReceivedMessageListener) {
        if (listeners.contains(listener)) return
        listeners.add(listener)
    }

    override fun removeMessageListener(listener: ChatOnReceivedMessageListener) {
        if (!listeners.contains(listener)) return
        listeners.remove(listener)
    }

    override fun connect() {
        RongCoreClient.addOnReceiveMessageListener(this)
    }

    override fun disconnect() {
        RongCoreClient.removeOnReceiveMessageListener(this)
    }

    override fun onReceivedMessage(
        message: Message?,
        profile: ReceivedProfile?
    ) {
        listeners.forEach { it.onReceivedMessage(message, profile) }

        dataSyncMonitor.onReceivedMessage(message, profile)
        systemMessageMonitor.onReceivedMessage(message, profile)
        userMessageMonitor.onReceivedMessage(message, profile)
    }
}
