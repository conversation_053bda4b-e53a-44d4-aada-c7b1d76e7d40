package com.flutterup.app.utils

import com.flutterup.app.model.UserCountEntity
import com.flutterup.app.network.ApiService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.conflate
import kotlinx.coroutines.flow.flowOn
import javax.inject.Inject
import javax.inject.Singleton

interface UserUnreadMonitor {

    val unreadCount: Flow<UserCountEntity>
}


@Singleton
class UserUnreadMonitorImpl @Inject constructor(
    private val apiService: ApiService,
    private val userMonitor: UserMonitor,
) : UserUnreadMonitor {
    override val unreadCount: Flow<UserCountEntity> = callbackFlow {
        if (!userMonitor.isLogin) {
            channel.trySend(UserCountEntity.EMPTY)
            channel.close()
            return@callbackFlow
        }

        try {
            val result = apiService.getUserListNum()
            channel.trySend(result.data ?: UserCountEntity.EMPTY)
        } catch (_: Exception) {
            channel.trySend(UserCountEntity.EMPTY)
        }

        awaitClose {
        }
    }.flowOn(Dispatchers.IO).conflate()
}