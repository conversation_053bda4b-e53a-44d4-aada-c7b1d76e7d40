package com.flutterup.app.utils.chat

import com.flutterup.app.model.UserInfo
import com.flutterup.app.network.ApiService
import com.flutterup.app.utils.GlobalSettingsMonitor
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseApplication
import com.flutterup.base.utils.applicationEntryPoint
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

object ChatUserInfoCache {

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface ChatUserInfoCacheEntryPoint {
        fun apiService(): ApiService

        fun settingsMonitor(): GlobalSettingsMonitor

        fun userMonitor(): UserMonitor
    }


    private val userInfoMap = ConcurrentHashMap<String, UserInfo>()

    private val apiService: ApiService by lazy {
        applicationEntryPoint<ChatUserInfoCacheEntryPoint>().apiService()
    }

    private val settingsMonitor: GlobalSettingsMonitor by lazy {
        applicationEntryPoint<ChatUserInfoCacheEntryPoint>().settingsMonitor()
    }

    private val userMonitor: UserMonitor by lazy {
        applicationEntryPoint<ChatUserInfoCacheEntryPoint>().userMonitor()
    }

    //初始化的时候会拉取客服和推送的账号信息
    init {
        BaseApplication.getApplicationScope().launch {
            val customerServiceAccount = settingsMonitor.customerServiceAccount.value
            val noticeAccount = settingsMonitor.noticeAccount.value
            val list = listOfNotNull(customerServiceAccount, noticeAccount)
            fetchUserinfoList(list)
        }
    }

    suspend fun getOrFetchUserInfo(userId: String): UserInfo? {
        if (!userMonitor.isLogin) return null

        if (userInfoMap.contains(userId)) {
            return userInfoMap[userId]
        }

        val userinfoResult = apiService.getAnotherProfileInfo(userId)
        val data = userinfoResult.data
        if (userinfoResult.isSuccess && data != null) {
            userInfoMap[userId] = data
            return data
        }

        return data
    }


    fun getUserInfo(userId: String): UserInfo? {
        return userInfoMap[userId]
    }

    fun fetchUserinfoList(idList: List<String>?) {
        if (!userMonitor.isLogin) return
        if (idList.isNullOrEmpty()) return

        val nonExistList = idList.filterNot { userInfoMap.contains(it) }
        if (nonExistList.isEmpty()) return

        BaseApplication.getApplicationScope().launch {
            val result = apiService.getAnotherProfileInfoList(nonExistList.joinToString(","))
            val data = result.data
        }
    }
}