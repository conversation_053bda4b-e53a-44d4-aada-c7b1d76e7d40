package com.flutterup.app.utils

object SharedElementUtils {
    fun generatorProfileIconKey(userId: String?): SharedElementKey {
        return ProfileIconSharedElementKey(userId.orEmpty())
    }

    fun generatorMediaUrlKey(url: String): SharedElement<PERSON>ey {
        return MediaUrlSharedElementKey(url)
    }
}

sealed interface SharedElementKey

sealed class ProfileSharedElementKey(
    open val userId: String,
    val type: ProfileSharedElementType
) : SharedElementKey

enum class ProfileSharedElementType {
    Media,

    Icon
}

data class MediaUrlSharedElementKey(
    val url: String
) : SharedElement<PERSON>ey

data class ProfileMediaSharedElementKey(
    override val userId: String,
    val mediaIndex: Int
) : ProfileSharedElementKey(userId, ProfileSharedElementType.Media)

data class ProfileIconSharedElementKey(
    override val userId: String
) : ProfileSharedElementKey(userId, ProfileSharedElementType.Icon)
