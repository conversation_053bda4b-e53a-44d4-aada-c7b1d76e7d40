package com.flutterup.app.utils.chat.core

import io.rong.imlib.RongIMClient
import io.rong.imlib.RongIMClient.MessageExpansionListener
import io.rong.imlib.model.Message
import javax.inject.Inject
import javax.inject.Singleton


fun interface OnMessageExpansionChangedListener : MessageExpansionListener {

    override fun onMessageExpansionRemove(keyArray: List<String?>?, message: Message?) = Unit
}


interface ChatMessageExpansionReceiverMonitor : ChatBasicMonitor {

    fun addListener(listener: OnMessageExpansionChangedListener)

    fun removeListener(listener: OnMessageExpansionChangedListener)
}

@Singleton
class ChatMessageExpansionReceiverMonitorImpl @Inject constructor() : ChatMessageExpansionReceiverMonitor, MessageExpansionListener {
    private val listeners = mutableSetOf<OnMessageExpansionChangedListener>()

    override fun addListener(listener: OnMessageExpansionChangedListener) {
        if (listeners.contains(listener)) return
        listeners.add(listener)
    }

    override fun removeListener(listener: OnMessageExpansionChangedListener) {
        if (!listeners.contains(listener)) return
        listeners.remove(listener)
    }

    override fun connect() {
        RongIMClient.getInstance().setMessageExpansionListener(this)
    }

    override fun disconnect() {
        RongIMClient.getInstance().setMessageExpansionListener(null)
    }

    override fun onMessageExpansionUpdate(
        expansion: Map<String?, String?>?,
        message: Message?
    ) {
        listeners.forEach { it.onMessageExpansionUpdate(expansion, message) }
    }

    override fun onMessageExpansionRemove(
        keyArray: List<String?>?,
        message: Message?
    ) {
        listeners.forEach { it.onMessageExpansionRemove(keyArray, message) }
    }
}