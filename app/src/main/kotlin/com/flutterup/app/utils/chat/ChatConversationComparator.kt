package com.flutterup.app.utils.chat

import com.flutterup.app.screen.chat.state.ChatConversation
import io.rong.imlib.model.Conversation

object ConversationComparator : Comparator<Conversation> {
    override fun compare(
        a: Conversation,
        b: Conversation
    ): Int = runCatching {
        // ALL filter: 按照isTop排序
        if (a.isTop != b.isTop) {
            return@runCatching if (a.isTop) -1 else 1
        }

        // 置顶状态相同，按照receivedTime降序排序（最新消息在前）
        return@runCatching b.receivedTime.compareTo(a.receivedTime)
    }.getOrElse {
        // 发生异常时，默认按operationTime降序排序
        b.receivedTime.compareTo(a.receivedTime)
    }
}

object ChatConversationComparator : Comparator<ChatConversation> {
    override fun compare(a: ChatConversation, b: ChatConversation): Int = ConversationComparator.compare(a.conversation, b.conversation)
}