package com.flutterup.app.utils.chat

import com.flutterup.base.utils.resumeIfActive
import com.flutterup.base.utils.resumeWithExceptionIfActive
import com.flutterup.chat.core.ChatException
import com.flutterup.chat.message.content.GiftMessageContent
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.RongCommonDefine
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Conversation.ConversationType
import io.rong.imlib.model.Message
import io.rong.imlib.model.Message.MessageDirection
import kotlinx.coroutines.suspendCancellableCoroutine

object ChatConversationUtils {

    suspend fun isExistUnreadReceivedGift(conversation: Conversation): Boolean {
        val objectNames = listOf(GiftMessageContent.VALUE)

        return suspendCancellableCoroutine { cont ->
            RongCoreClient.getInstance().getHistoryMessages(
                conversation.conversationType,
                conversation.targetId,
                objectNames,
                System.currentTimeMillis(),
                100, //最多只加载100条
                RongCommonDefine.GetMessageDirection.FRONT,
                object : IRongCoreCallback.ResultCallback<List<Message>>() {
                    override fun onSuccess(messages: List<Message>?) {

                        val existUnreadReceivedGift = messages?.any {
                            val isReceive = it.messageDirection == MessageDirection.RECEIVE //接收方
                            val isUnread = !it.receivedStatus.isRead //未读
                            val isGift = it.content is GiftMessageContent //礼物消息

                            /**
                             * 满足以下条件
                             * 1. 接收方
                             * 2. 未读
                             * 3. 礼物消息
                             */
                            isReceive && isUnread && isGift
                        } ?: false

                        cont.resumeIfActive(existUnreadReceivedGift)
                    }

                    override fun onError(e: IRongCoreEnum.CoreErrorCode?) {
                        cont.resumeIfActive(false)
                    }
                }
            )
        }
    }


    suspend fun topOrUntopConversation(conversation: Conversation, isTop: Boolean): Boolean =
        suspendCancellableCoroutine { cont ->
            RongCoreClient.getInstance().setConversationToTop(
                conversation.conversationType,
                conversation.targetId,
                isTop,
                object : IRongCoreCallback.ResultCallback<Boolean>() {
                    override fun onSuccess(t: Boolean?) {
                        cont.resumeIfActive(t == true)
                    }

                    override fun onError(e: IRongCoreEnum.CoreErrorCode) {
                        cont.resumeWithExceptionIfActive(ChatException(e))
                    }
                })
        }

    suspend fun getConversation(conversation: Conversation) = getConversation(conversation.conversationType, conversation.targetId)

    suspend fun getConversation(type: ConversationType, targetId: String): Conversation? =
        suspendCancellableCoroutine { cont ->
            RongCoreClient.getInstance().getConversation(
                type,
                targetId,
                object : IRongCoreCallback.ResultCallback<Conversation>() {
                    override fun onSuccess(t: Conversation?) {
                        cont.resumeIfActive(t)
                    }

                    override fun onError(e: IRongCoreEnum.CoreErrorCode?) {
                        cont.resumeWithExceptionIfActive(ChatException(e))
                    }
                }
            )
        }

    suspend fun deleteConversation(conversation: Conversation, isClearHistory: Boolean = true): Boolean {
        if (isClearHistory) {
            clearConversationHistory(conversation)
        }
        return deleteConversation(conversation)
    }

    suspend fun clearConversationHistory(conversation: Conversation): Boolean =
        suspendCancellableCoroutine { cont ->
            RongCoreClient.getInstance().deleteMessages(
                conversation.conversationType,
                conversation.targetId,
                object : IRongCoreCallback.ResultCallback<Boolean>() {
                    override fun onSuccess(t: Boolean?) {
                        //删除会话内所有消息是否成功
                        cont.resumeIfActive(t == true)
                    }

                    override fun onError(e: IRongCoreEnum.CoreErrorCode?) {
                        cont.resumeWithExceptionIfActive(ChatException(e))
                    }
                })
        }

    private suspend fun deleteConversation(conversation: Conversation): Boolean =
        suspendCancellableCoroutine { cont ->
            RongIMClient.getInstance().removeConversation(
                conversation.conversationType,
                conversation.targetId,
                object : RongIMClient.ResultCallback<Boolean>() {
                    override fun onSuccess(t: Boolean?) {
                        //删除会话是否成功
                        cont.resumeIfActive(t == true)
                    }

                    override fun onError(e: RongIMClient.ErrorCode?) {
                        cont.resumeWithExceptionIfActive(ChatException(e))
                    }
                })
        }
}