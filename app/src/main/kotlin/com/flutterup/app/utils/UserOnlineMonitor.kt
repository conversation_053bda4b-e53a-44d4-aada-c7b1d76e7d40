package com.flutterup.app.utils

import com.flutterup.app.network.ApiService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

interface UserOnlineMonitor {

    val onlineUserList: StateFlow<List<String>>

    val previousOnlineUserList: StateFlow<List<String>>

    fun start()

    fun cancel()

    fun updateOnlineUserList(onlineUserList: List<String>)

    fun isOnline(userId: String): Boolean
}

@Singleton
class UserOnlineMonitorImpl @Inject constructor(
    private val userMonitor: UserMonitor,
    private val appScope: CoroutineScope,
    private val globalSettingsMonitor: GlobalSettingsMonitor,
    private val apiService: ApiService,
) : UserOnlineMonitor {
    private val _onlineUserList = MutableStateFlow(emptyList<String>())
    private val _previousOnlineUserList = MutableStateFlow(emptyList<String>())

    override val onlineUserList: StateFlow<List<String>> = _onlineUserList.asStateFlow()

    override val previousOnlineUserList: StateFlow<List<String>> = _previousOnlineUserList.asStateFlow()

    private var pollingJob: Job? = null


    override fun start() {
        cancel()

        pollingJob = appScope.launch {
            val interval = globalSettingsMonitor.onlineApiInterval.value ?: 60L

            while (isActive) {
                fetchOnlineUserList()
                delay(interval * 1000L)
            }
        }
    }

    override fun cancel() {
        pollingJob?.cancel()
        pollingJob = null
    }

    override fun updateOnlineUserList(onlineUserList: List<String>) {
        _previousOnlineUserList.value = _onlineUserList.value
        _onlineUserList.value = onlineUserList
    }

    override fun isOnline(userId: String): Boolean {
        return _onlineUserList.value.contains(userId)
    }

    private suspend fun fetchOnlineUserList() {
        _previousOnlineUserList.value = _onlineUserList.value
        _onlineUserList.value = emptyList()

        if (!userMonitor.isLogin) return

        val result = apiService.getOnlineUserList()
        if (result.isSuccess) {
            _onlineUserList.value = result.data?.map { it.userId } ?: emptyList()
        }
    }
}