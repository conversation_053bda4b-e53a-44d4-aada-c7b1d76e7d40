<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">


    <uses-permission android:name="android.permission.INTERNET"/>

    <!--    定位权限    -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>

    <!--    推送权限    -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_app_logo"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_app_logo"
        android:supportsRtl="true"
        android:theme="@style/FlutterUp"
        android:name=".FlutterUpApplication">

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">

            <meta-data android:name="com.flutterup.app.startup.TrackingInitializer" android:value="androidx.startup" />
            <meta-data android:name="com.flutterup.app.startup.ChatInitializer" android:value="androidx.startup" />
            <meta-data android:name="com.flutterup.app.startup.NotificationChannelInitializer" android:value="androidx.startup" />
        </provider>

        <activity
            android:name=".screen.MainActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            android:theme="@style/FlutterUp.Splash"
            android:exported="true"
            android:windowSoftInputMode="adjustResize">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity android:name=".screen.ImageCropperActivity"
            android:screenOrientation="portrait"/>
    </application>
</manifest>