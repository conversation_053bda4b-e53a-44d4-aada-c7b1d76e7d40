package com.flutterup.app.design.text

import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import org.junit.Test
import org.junit.Assert.*

/**
 * AppText 组件单元测试
 * 测试新增的 bold 和 fontType 参数功能
 */
class AppTextTest {

    @Test
    fun `test AppFontFamily enum values`() {
        // 测试所有字体类型枚举值
        assertEquals(null, AppFontFamily.Default.fontFamily)
        assertEquals(FontFamily.SansSerif, AppFontFamily.SansSerif.fontFamily)
        assertEquals(FontFamily.Serif, AppFontFamily.Serif.fontFamily)
        assertEquals(FontFamily.Monospace, AppFontFamily.Monospace.fontFamily)
        assertEquals(FontFamily.Cursive, AppFontFamily.Cursive.fontFamily)
    }

    @Test
    fun `test AppFontFamily enum count`() {
        // 确保我们有正确数量的字体类型
        val values = AppFontFamily.values()
        assertEquals(5, values.size)
        
        // 验证所有预期的值都存在
        assertTrue(values.contains(AppFontFamily.Default))
        assertTrue(values.contains(AppFontFamily.SansSerif))
        assertTrue(values.contains(AppFontFamily.Serif))
        assertTrue(values.contains(AppFontFamily.Monospace))
        assertTrue(values.contains(AppFontFamily.Cursive))
    }

    @Test
    fun `test font family mapping`() {
        // 测试字体类型映射是否正确
        val mappings = mapOf(
            AppFontFamily.Default to null,
            AppFontFamily.SansSerif to FontFamily.SansSerif,
            AppFontFamily.Serif to FontFamily.Serif,
            AppFontFamily.Monospace to FontFamily.Monospace,
            AppFontFamily.Cursive to FontFamily.Cursive
        )
        
        mappings.forEach { (appFont, expectedFont) ->
            assertEquals(
                "Font mapping for $appFont should be $expectedFont",
                expectedFont,
                appFont.fontFamily
            )
        }
    }
}
