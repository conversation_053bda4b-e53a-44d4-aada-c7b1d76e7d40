static def getBuildVersionTime(versionName, versionCode) {
    return "$versionName($versionCode)_" + new Date().format("MMddhhmm")
}

def ALIAS = "flutterup"

android {
    buildTypes {
        debug {
            buildConfigField("String", "ALIAS", "\"$ALIAS\"")
            manifestPlaceholders["firebaseCrashlyticsCollectionEnabled"] = true
        }

        release {
            buildConfigField("String", "ALIAS", "\"$ALIAS\"")
            manifestPlaceholders["firebaseCrashlyticsCollectionEnabled"] = true
        }
    }

    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            def buildType = variant.buildType.name

            def fileName = "$ALIAS"
            if (buildType) {
                fileName += "_${buildType}"
            }
            fileName += "_v${getBuildVersionTime(android.defaultConfig.versionName, android.defaultConfig.versionCode)}"

            // 检查输出文件类型，支持APK和AAB
            def fileExtension = output.outputFile.name.endsWith('.aab') ? '.aab' : '.apk'
            fileName += fileExtension

            outputFileName = fileName
        }
    }

    signingConfigs {
        debug {
            storeFile file("../flutterup.jks")
            storePassword 'flutterup'
            keyAlias = 'flutterup'
            keyPassword 'flutterup'
            v1SigningEnabled true
            v2SigningEnabled true
        }

        release {
            storeFile file("../flutterup.jks")
            storePassword 'flutterup'
            keyAlias = 'flutterup'
            keyPassword 'flutterup'
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
            signingConfig signingConfigs.debug
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        release {
            debuggable true
            zipAlignEnabled true
            shrinkResources true
            minifyEnabled true
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

project.afterEvaluate {
    android.buildTypes.all { buildType ->
        buildType.buildConfigField "String", "BUILD_VERSION_TIME",
                "\"${getBuildVersionTime(android.defaultConfig.versionName, android.defaultConfig.versionCode)}\""
    }
}