plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.ksp)
}

apply(from = "${rootDir}/gradles/buildBase.gradle")
apply(from = "${rootDir}/gradles/buildCompose.gradle")
apply(from = "${rootDir}/gradles/buildHilt.gradle")

android {
    defaultConfig {
        proguardFiles()
    }
    namespace = "com.flutterup.base"
}

dependencies {
    // Coroutines
    implementation(libs.coroutines.core)
    implementation(libs.coroutines.android)

    // Moshi - JSON serialization
    api(libs.moshi)
    api(libs.moshi.kotlin)
    ksp(libs.moshi.codegen)

    // Image Loading
    api(libs.coil.compose)
    api(libs.coil.network)
    api(libs.coil.gif)
    api(libs.coil.video)

    // Architecture Components
    api(libs.lifecycle.runtime.ktx)
    api(libs.lifecycle.viewmodel.ktx)
    api(libs.lifecycle.livedata.ktx)

    // mmkv
    api(libs.mmkv)

    //startup
    api(libs.androidx.startup)

    //location
    api(libs.google.services.location)
}