package com.flutterup.base.utils

import android.content.res.Resources
import java.util.Locale

object LocaleUtils {
    val currentLocale: Locale get() = Resources.getSystem().configuration.locales.get(0)

    val currentLanguage: String get() = Resources.getSystem().configuration.locales.get(0).language

    val currentCountry: String get() = Resources.getSystem().configuration.locales.get(0).country
}