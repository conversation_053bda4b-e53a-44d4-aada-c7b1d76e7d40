package com.flutterup.base.utils

import android.content.Context
import android.graphics.BitmapFactory
import android.media.MediaMetadataRetriever
import android.net.Uri
import java.io.File

object UriUtils {

    fun getMimeType(context: Context, uri: Uri): MimeType {
        val contentResolver = context.contentResolver
        val mimeType = contentResolver.getType(uri)
        return when {
            mimeType?.startsWith("image/") == true -> MimeType.IMAGE
            mimeType?.startsWith("video/") == true -> MimeType.VIDEO
            mimeType?.startsWith("audio/") == true -> MimeType.AUDIO
            else -> MimeType.OTHER
        }
    }

    fun getFileSize(context: Context, uri: Uri): Long {
        try {
            val fileDescriptor = context.contentResolver.openFileDescriptor(uri, "r")
            val fileSize = fileDescriptor?.statSize ?: 0L
            fileDescriptor?.close()
            return fileSize
        } catch (_: Exception) {
            return 0L
        }
    }

    fun getUriMediaInfo(context: Context, mimeType: MimeType, uri: Uri): Triple<Long, Long, Long> {
        var width = 0L
        var height = 0L
        var duration = 0L

        when (mimeType) {
            MimeType.IMAGE -> {
                try {
                    val options = BitmapFactory.Options().apply {
                        inJustDecodeBounds = true
                    }
                    context.contentResolver.openInputStream(uri)?.use { inputStream ->
                        BitmapFactory.decodeStream(inputStream, null, options)
                        width = options.outWidth.toLong()
                        height = options.outHeight.toLong()
                    }
                } catch (_: Exception) {
                }
            }

            MimeType.VIDEO -> {
                try {
                    val retriever = MediaMetadataRetriever()
                    retriever.setDataSource(context, uri)
                    width =
                        retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)
                            ?.toLongOrNull() ?: 0L
                    height =
                        retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)
                            ?.toLongOrNull() ?: 0L
                    duration =
                        retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
                            ?.toLongOrNull() ?: 0L
                    retriever.release()
                } catch (_: Exception) {
                }
            }

            else -> {
            }
        }

        return Triple(width, height, duration)
    }

    fun uriToFile(context: Context, mimeType: MimeType, uri: Uri): File? {
        val suffix = when (mimeType) {
            MimeType.IMAGE -> FileUtils.IMAGE_SUFFIX
            MimeType.VIDEO -> FileUtils.VIDEO_SUFFIX
            else -> return null
        }
        return FileUtils.uriToCacheFile(context, uri, suffix)
    }
}

enum class MimeType {
    IMAGE,
    VIDEO,
    AUDIO,
    OTHER
}