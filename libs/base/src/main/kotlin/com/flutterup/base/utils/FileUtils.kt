package com.flutterup.base.utils

import android.content.Context
import android.net.Uri
import java.io.File

object FileUtils {
    const val IMAGE_SUFFIX = ".png"
    const val VIDEO_SUFFIX = ".mp4"

    const val SIZE_B = 1L
    const val SIZE_KB = 1024L
    const val SIZE_MB = 1024L * 1024L
    const val SIZE_GB = 1024L * 1024L * 1024L

    const val UNIT_B = "B"
    const val UNIT_KB = "KB"
    const val UNIT_MB = "MB"
    const val UNIT_GB = "GB"


    fun uriToCacheFile(
        context: Context,
        uri: Uri,
        suffix: String,
        fileName: String = "temp_${System.currentTimeMillis()}",
    ): File {
        val inputStream = context.contentResolver.openInputStream(uri)
        val cacheFile = generateFile(context, suffix, fileName)
        cacheFile.outputStream().use { outputStream ->
            inputStream?.copyTo(outputStream)
        }
        return cacheFile
    }

    fun generateFile(
        context: Context,
        suffix: String,
        fileName: String = "temp_${System.currentTimeMillis()}",
    ): File {
        return File(context.cacheDir, "$fileName$suffix")
    }

    fun generateFileName(suffix: String): String {
        return "temp_${System.currentTimeMillis()}$suffix"
    }

    /**
     * 获取文件夹大小
     */
    fun getFolderSize(folder: File): Long {
        return folder.walk().filter { it.isFile }.sumOf { it.length() }
    }

    /**
     * 格式化文件大小
     */
    fun formatFileSize(cacheSize: Long): String {
        return if (cacheSize < 1024) {
            "${cacheSize}B"
        } else if (cacheSize < 1024 * 1024) {
            "${cacheSize / 1024}KB"
        } else if (cacheSize < 1024 * 1024 * 1024) {
            "${cacheSize / 1024 / 1024}MB"
        } else {
            "${cacheSize / 1024 / 1024 / 1024}GB"
        }
    }
}