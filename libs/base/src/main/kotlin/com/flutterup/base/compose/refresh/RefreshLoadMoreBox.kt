package com.flutterup.base.compose.refresh

import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.PullToRefreshDefaults.Indicator
import androidx.compose.material3.pulltorefresh.PullToRefreshState
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * 支持下拉刷新和上拉加载更多的组合组件
 * 所有状态由外部管理，组件专注于UI展示和交互逻辑
 *
 * @param isRefreshing 是否正在刷新
 * @param isLoadingMore 是否正在加载更多
 * @param hasNoMoreData 是否没有更多数据
 * @param onRefresh 刷新回调
 * @param onLoadMore 加载更多回调
 * @param modifier 修饰符
 * @param lazyListState LazyColumn的状态
 * @param isLoadMoreEnabled 是否启用加载更多功能
 * @param thresholdDp 触发加载更多的阈值
 * @param contentAlignment 内容对齐方式
 * @param refreshIndicator 自定义刷新指示器
 * @param loadMoreIndicator 自定义加载更多指示器
 * @param content LazyColumn的内容
 */
@ExperimentalMaterial3Api
@Composable
fun RefreshLoadMoreBox(
    isRefreshing: Boolean,
    isLoadingMore: Boolean,
    hasNoMoreData: Boolean,
    onRefresh: () -> Unit,
    onLoadMore: () -> Unit,
    modifier: Modifier = Modifier,
    pullRefreshState: PullToRefreshState = rememberPullToRefreshState(),
    pullLoadMoreState: PullToLoadMoreState = rememberPullToLoadMoreState(),
    lazyListState: LazyListState = rememberLazyListState(),
    isLoadMoreEnabled: Boolean = true,
    thresholdDp: Dp = 60.dp,
    contentAlignment: Alignment = Alignment.TopStart,
    refreshIndicator: @Composable BoxScope.() -> Unit = {
        Indicator(
            modifier = Modifier.align(Alignment.TopCenter),
            isRefreshing = isRefreshing,
            state = pullRefreshState
        )
    },
    loadMoreIndicator: @Composable () -> Unit = {
        AnimatedLoadMoreIndicator(isLoadingMore, hasNoMoreData, pullLoadMoreState)
    },
    content: @Composable BoxScope.() -> Unit
) {
    // 管理加载更多动画状态
    LaunchedEffect(isLoadingMore) {
        if (isLoadingMore) {
            pullLoadMoreState.animateToThreshold()
        } else {
            pullLoadMoreState.animateToHidden()
        }
    }

    // 管理 noMoreData 状态的自动收起
    LaunchedEffect(hasNoMoreData) {
        if (hasNoMoreData) {
            // 显示 noMoreData UI
            pullLoadMoreState.animateToThreshold()
            // 延迟 2 秒后自动收起
            kotlinx.coroutines.delay(2000)
            pullLoadMoreState.animateToHidden()
        }
    }

    // 刷新时重置加载更多动画状态
    LaunchedEffect(isRefreshing) {
        if (isRefreshing) {
            pullLoadMoreState.animateToHidden()
        }
    }

    PullToRefreshBox(
        isRefreshing = isRefreshing,
        onRefresh = onRefresh,
        state = pullRefreshState,
        contentAlignment = contentAlignment,
        indicator = refreshIndicator,
        modifier = modifier.pullToLoadMoreGesture(
            state = pullLoadMoreState,
            lazyListState = lazyListState,
            enabled = isLoadMoreEnabled && !isLoadingMore && !hasNoMoreData,
            thresholdDp = 80f,
            onLoadMore = onLoadMore
        ),
    ) {
        // 用户提供的内容
        content()

        // 加载更多指示器
        if (isLoadMoreEnabled &&
            (isLoadingMore || hasNoMoreData)) {
            loadMoreIndicator()
        }
    }
}