package com.flutterup.base.di

import android.content.Context
import androidx.startup.Initializer
import com.flutterup.base.store.MMKVStore
import com.flutterup.base.store.MMKVStoreImpl
import com.tencent.mmkv.MMKV
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object StoreModule {

    @Provides
    @Singleton
    fun provideMMKV(): MMKV {
        return MMKV.defaultMMKV()
    }

    @Provides
    @Singleton
    fun provideMMKVStore(mmkv: MMKV): MMKVStore {
        return MMKVStoreImpl(mmkv)
    }
}

class MMKVInitializer : Initializer<Unit> {
    override fun create(context: Context) {
        MMKV.initialize(context, context.cacheDir.absolutePath + "/mmkv")
    }

    override fun dependencies(): List<Class<out Initializer<*>?>?> = emptyList()
}