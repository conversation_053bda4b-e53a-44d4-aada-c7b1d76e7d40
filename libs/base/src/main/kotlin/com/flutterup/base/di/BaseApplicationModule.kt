package com.flutterup.base.di

import com.flutterup.base.BaseApplication
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope


@Module
@InstallIn(SingletonComponent::class)
object BaseApplicationModule {

    @Provides
    fun provideApplicationScope(): CoroutineScope {
        return BaseApplication.getApplicationScope()
    }
}