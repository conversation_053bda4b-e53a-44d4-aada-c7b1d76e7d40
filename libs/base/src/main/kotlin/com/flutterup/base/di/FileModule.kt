package com.flutterup.base.di

import android.content.Context
import com.flutterup.base.AppDirs
import com.flutterup.base.Dirs
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import java.io.File


@Module
@InstallIn(SingletonComponent::class)
object FileModule {

    @Provides
    @Dirs(AppDirs.CACHE)
    fun provideCacheDir(@ApplicationContext context: Context): File {
        return context.cacheDir
    }

    @Provides
    @Dirs(AppDirs.DATA)
    fun provideDataDir(@ApplicationContext context: Context): File {
        return context.dataDir
    }

    @Provides
    @Dirs(AppDirs.FILES)
    fun provideFilesDir(@ApplicationContext context: Context): File {
        return context.filesDir
    }

    @Provides
    @Dirs(AppDirs.APP_CACHE)
    fun provideAppCacheDir(@ApplicationContext context: Context): File {
        return File(context.cacheDir, "app_cache")
    }
}