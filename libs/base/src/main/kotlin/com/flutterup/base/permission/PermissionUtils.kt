package com.flutterup.base.permission

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * 权限工具类
 */
object PermissionUtils {
    
    /**
     * 检查单个权限是否已授权
     */
    fun isPermissionGranted(context: Context, permission: String): Boolean {
        return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查多个权限是否都已授权
     */
    fun arePermissionsGranted(context: Context, permissions: List<String>): Bo<PERSON>an {
        return permissions.all { isPermissionGranted(context, it) }
    }
    
    /**
     * 获取未授权的权限列表
     */
    fun getDeniedPermissions(context: Context, permissions: List<String>): List<String> {
        return permissions.filter { !isPermissionGranted(context, it) }
    }
    
    /**
     * 获取已授权的权限列表
     */
    fun getGrantedPermissions(context: Context, permissions: List<String>): List<String> {
        return permissions.filter { isPermissionGranted(context, it) }
    }
    
    /**
     * 检查权限是否应该显示说明（用户之前拒绝过）
     */
    fun shouldShowRequestPermissionRationale(activity: Activity, permission: String): Boolean {
        return ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
    }
    
    /**
     * 检查权限是否被永久拒绝
     * 权限被拒绝且不再显示说明对话框时，认为是永久拒绝
     */
    fun isPermissionPermanentlyDenied(activity: Activity, permission: String): Boolean {
        return !isPermissionGranted(activity, permission) && 
               !shouldShowRequestPermissionRationale(activity, permission)
    }
    
    /**
     * 获取权限状态
     */
    fun getPermissionStatus(activity: Activity, permission: String): PermissionStatus {
        return when {
            isPermissionGranted(activity, permission) -> PermissionStatus.GRANTED
            shouldShowRequestPermissionRationale(activity, permission) -> PermissionStatus.DENIED
            else -> PermissionStatus.PERMANENTLY_DENIED
        }
    }
    
    /**
     * 获取多个权限的状态
     */
    fun getPermissionsStatus(activity: Activity, permissions: List<String>): Map<String, PermissionStatus> {
        return permissions.associateWith { getPermissionStatus(activity, it) }
    }
    
    /**
     * 打开应用设置页面
     */
    fun openAppSettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", context.packageName, null)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 如果无法打开应用设置，尝试打开系统设置
            try {
                val intent = Intent(Settings.ACTION_SETTINGS).apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
            } catch (e2: Exception) {
                // 忽略异常
            }
        }
    }
    
    /**
     * 检查是否是危险权限
     */
    fun isDangerousPermission(permission: String): Boolean {
        // Android 6.0+ 的危险权限列表
        val dangerousPermissions = setOf(
            android.Manifest.permission.READ_CALENDAR,
            android.Manifest.permission.WRITE_CALENDAR,
            android.Manifest.permission.CAMERA,
            android.Manifest.permission.READ_CONTACTS,
            android.Manifest.permission.WRITE_CONTACTS,
            android.Manifest.permission.GET_ACCOUNTS,
            android.Manifest.permission.ACCESS_FINE_LOCATION,
            android.Manifest.permission.ACCESS_COARSE_LOCATION,
            android.Manifest.permission.RECORD_AUDIO,
            android.Manifest.permission.READ_PHONE_STATE,
            android.Manifest.permission.CALL_PHONE,
            android.Manifest.permission.READ_CALL_LOG,
            android.Manifest.permission.WRITE_CALL_LOG,
            android.Manifest.permission.ADD_VOICEMAIL,
            android.Manifest.permission.USE_SIP,
            android.Manifest.permission.PROCESS_OUTGOING_CALLS,
            android.Manifest.permission.BODY_SENSORS,
            android.Manifest.permission.SEND_SMS,
            android.Manifest.permission.RECEIVE_SMS,
            android.Manifest.permission.READ_SMS,
            android.Manifest.permission.RECEIVE_WAP_PUSH,
            android.Manifest.permission.RECEIVE_MMS,
            android.Manifest.permission.READ_EXTERNAL_STORAGE,
            android.Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
        
        // Android 13+ 新增的权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val android13Permissions = setOf(
                android.Manifest.permission.POST_NOTIFICATIONS,
                android.Manifest.permission.READ_MEDIA_IMAGES,
                android.Manifest.permission.READ_MEDIA_VIDEO,
                android.Manifest.permission.READ_MEDIA_AUDIO
            )
            return dangerousPermissions.contains(permission) || android13Permissions.contains(permission)
        }
        
        return dangerousPermissions.contains(permission)
    }
}
