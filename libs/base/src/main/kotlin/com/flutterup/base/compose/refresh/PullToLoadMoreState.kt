package com.flutterup.base.compose.refresh

import androidx.annotation.FloatRange
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationVector1D
import androidx.compose.animation.core.VectorConverter
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.rememberSaveable

/**
 * 记住上拉加载更多状态
 */
@Composable
fun rememberPullToLoadMoreState(): PullToLoadMoreState {
    return rememberSaveable(saver = PullToLoadMoreStateImpl.Saver) { PullToLoadMoreStateImpl() }
}

@Stable
interface PullToLoadMoreState {

    /**
     * Distance percentage towards the refresh threshold. 0.0 indicates no distance, 1.0 indicates
     * being at the threshold offset, > 1.0 indicates overshoot beyond the provided threshold.
     */
    @get:FloatRange(from = 0.0) val distanceFraction: Float

    /** Whether the state is currently animating */
    val isAnimating: Boolean
        get() = false

    /**
     * Animate the distance towards the anchor or threshold position, where the indicator will be
     * shown when refreshing.
     */
    suspend fun animateToThreshold()

    /** Animate the distance towards the position where the indicator will be hidden when idle */
    suspend fun animateToHidden()

    /** Snap the indicator to the desired threshold fraction */
    suspend fun snapTo(@FloatRange(from = 0.0) targetValue: Float)
}

/**
 * 上拉加载更多状态管理类
 * 参考 PullToRefreshState 的设计模式
 */
@Stable
private class PullToLoadMoreStateImpl(
    private val anim: Animatable<Float, AnimationVector1D>
) : PullToLoadMoreState {
    constructor() : this(Animatable(0f, Float.VectorConverter))

    override val distanceFraction
        get() = anim.value

    /** Whether the state is currently animating */
    override val isAnimating: Boolean
        get() = anim.isRunning

    override suspend fun animateToThreshold() {
        anim.animateTo(1f)
    }

    override suspend fun animateToHidden() {
        anim.animateTo(0f)
    }

    override suspend fun snapTo(targetValue: Float) {
        anim.snapTo(targetValue)
    }

    companion object {
        val Saver =
            Saver<PullToLoadMoreStateImpl, Float>(
                save = { it.anim.value },
                restore = { PullToLoadMoreStateImpl(Animatable(it, Float.VectorConverter)) }
            )
    }
}