package com.flutterup.base.compose.refresh

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp

/**
 * 默认的加载更多指示器
 */
@Composable
fun DefaultLoadMoreIndicator(
    isLoadingMore: Boolean,
    hasNoMoreData: Boolean,
    modifier: Modifier = Modifier,
    loadingText: String = "Loading...",
    noMoreDataText: String = "No more data.",
    loadingTextColor: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    noMoreDataTextColor: Color = MaterialTheme.colorScheme.onSurfaceVariant
) {
    AnimatedVisibility(
        visible = isLoadingMore || hasNoMoreData,
        enter = fadeIn(),
        exit = fadeOut(),
        modifier = modifier
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            when {
                isLoadingMore -> {
                    Row(
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = loadingText,
                            style = MaterialTheme.typography.bodyMedium,
                            color = loadingTextColor
                        )
                    }
                }
                hasNoMoreData -> {
                    Text(
                        text = noMoreDataText,
                        style = MaterialTheme.typography.bodyMedium,
                        color = noMoreDataTextColor,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

/**
 * 带动画的加载更多指示器
 * 支持 PullToLoadMoreState 的动画状态
 */
@Composable
fun AnimatedLoadMoreIndicator(
    isLoadingMore: Boolean,
    hasNoMoreData: Boolean,
    state: PullToLoadMoreState,
    modifier: Modifier = Modifier,
    loadingText: String = "Loading...",
    noMoreDataText: String = "No more data",
    showProgress: Boolean = true
) {
    // 控制整体可见性的动画，考虑 state.distanceFraction
    val alpha by animateFloatAsState(
        targetValue = if (isLoadingMore || (hasNoMoreData && state.distanceFraction > 0f)) 1f else 0f,
        label = "load_more_alpha"
    )

    // 控制 noMoreData 文本的缩放动画
    val noMoreDataScale by animateFloatAsState(
        targetValue = if (hasNoMoreData && state.distanceFraction > 0f) 1f else 0.8f,
        label = "no_more_data_scale"
    )

    Box(
        modifier = modifier
            .fillMaxWidth()
            .alpha(alpha)
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            when {
                isLoadingMore -> {
                    if (showProgress) {
                        LinearProgressIndicator(
                            progress = { state.distanceFraction },
                            modifier = Modifier
                                .fillMaxWidth(0.3f)
                                .height(2.dp),
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                    
                    Row(
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = loadingText,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                hasNoMoreData -> {
                    Text(
                        text = noMoreDataText,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.scale(noMoreDataScale)
                    )
                }
            }
        }
    }
}

/**
 * 简约风格的加载更多指示器
 */
@Composable
fun MinimalLoadMoreIndicator(
    isLoadingMore: Boolean,
    hasNoMoreData: Boolean,
    modifier: Modifier = Modifier
) {
    if (!isLoadingMore && !hasNoMoreData) return

    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 12.dp),
        contentAlignment = Alignment.Center
    ) {
        when {
            isLoadingMore -> {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    strokeWidth = 2.dp
                )
            }
            hasNoMoreData -> {
                Text(
                    text = "· · ·",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

/**
 * 自定义样式的加载更多指示器
 */
@Composable
fun CustomLoadMoreIndicator(
    isLoadingMore: Boolean,
    hasNoMoreData: Boolean,
    modifier: Modifier = Modifier,
    loadingContent: @Composable () -> Unit = {
        CircularProgressIndicator(
            modifier = Modifier.size(20.dp),
            strokeWidth = 2.dp
        )
    },
    noMoreDataContent: @Composable () -> Unit = {
        Text(
            text = "No more data.",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
    }
) {
    AnimatedVisibility(
        visible = isLoadingMore || hasNoMoreData,
        enter = fadeIn(),
        exit = fadeOut(),
        modifier = modifier
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            when {
                isLoadingMore -> loadingContent()
                hasNoMoreData -> noMoreDataContent()
            }
        }
    }
}
