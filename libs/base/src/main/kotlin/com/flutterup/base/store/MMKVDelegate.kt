package com.flutterup.base.store

import android.os.Parcelable
import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty

class MMKVDelegate<T>(
    private val store: MMKVStore,
    private val key: String? = null,
    private val defaultValue: T
) : ReadWriteProperty<Any?, T> {

    override fun getValue(thisRef: Any?, property: KProperty<*>): T {
        val actualKey = key ?: property.name
        return store.get(actualKey, defaultValue)
    }

    override fun setValue(thisRef: Any?, property: KProperty<*>, value: T) {
        val actualKey = key ?: property.name
        store.put(actualKey, value)
    }
}

// 扩展函数，简化委托的创建
fun MMKVStore.stringValue(key: String? = null, defaultValue: String = "") =
    MMKVDelegate(this, key, defaultValue)

fun MMKVStore.intValue(key: String? = null, defaultValue: Int = 0) =
    MMKVDelegate(this, key, defaultValue)

fun MMKVStore.longValue(key: String? = null, defaultValue: Long = 0L) =
    MMKVDelegate(this, key, defaultValue)

fun MMKVStore.floatValue(key: String? = null, defaultValue: Float = 0f) =
    MMKVDelegate(this, key, defaultValue)

fun MMKVStore.doubleValue(key: String? = null, defaultValue: Double = 0.0) =
    MMKVDelegate(this, key, defaultValue)

fun MMKVStore.booleanValue(key: String? = null, defaultValue: Boolean = false) =
    MMKVDelegate(this, key, defaultValue)

fun MMKVStore.byteArrayValue(key: String? = null, defaultValue: ByteArray = ByteArray(0)) =
    MMKVDelegate(this, key, defaultValue)

fun <T : Parcelable> MMKVStore.parcelableValue(key: String? = null, defaultValue: T) =
    MMKVDelegate(this, key, defaultValue)

// 可空值的委托
class NullableMMKVDelegate<T>(
    private val store: MMKVStore,
    private val key: String? = null,
    private val defaultValue: T
) : ReadWriteProperty<Any?, T?> {

    override fun getValue(thisRef: Any?, property: KProperty<*>): T? {
        val actualKey = key ?: property.name
        return if (store.contains(actualKey)) {
            store.get(actualKey, defaultValue)
        } else {
            null
        }
    }

    override fun setValue(thisRef: Any?, property: KProperty<*>, value: T?) {
        val actualKey = key ?: property.name
        if (value == null) {
            store.remove(actualKey)
        } else {
            store.put(actualKey, value)
        }
    }
}

fun MMKVStore.nullableStringValue(key: String? = null) =
    NullableMMKVDelegate(this, key, "")

fun MMKVStore.nullableIntValue(key: String? = null) =
    NullableMMKVDelegate(this, key, 0)

fun MMKVStore.nullableLongValue(key: String? = null) =
    NullableMMKVDelegate(this, key, 0L)

fun MMKVStore.nullableFloatValue(key: String? = null) =
    NullableMMKVDelegate(this, key, 0f)

fun MMKVStore.nullableDoubleValue(key: String? = null) =
    NullableMMKVDelegate(this, key, 0.0)

fun MMKVStore.nullableBooleanValue(key: String? = null) =
    NullableMMKVDelegate(this, key, false)

fun MMKVStore.nullableByteArrayValue(key: String? = null) =
    NullableMMKVDelegate(this, key, ByteArray(0))

fun <T : Parcelable> MMKVStore.nullableParcelableValue(key: String? = null, defaultValue: T) =
    NullableMMKVDelegate(this, key, defaultValue)
