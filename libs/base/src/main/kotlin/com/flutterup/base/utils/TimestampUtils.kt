package com.flutterup.base.utils

import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId

private const val TAG = "TimestampUtils"

fun Long.toLocalDate(): LocalDate? {
    return try {
        Instant.ofEpochMilli(this)
            .atZone(ZoneId.systemDefault())
            .toLocalDate()
    } catch (e: Exception) {
        Timber.e(TAG, "Failed to convert timestamp to LocalDate", e)
        null
    }
}