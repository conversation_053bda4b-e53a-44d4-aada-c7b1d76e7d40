package com.flutterup.base.utils

import kotlinx.coroutines.CancellableContinuation
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

fun <T> CancellableContinuation<T>.resumeIfActive(value: T) {
    if (isActive) {
        resume(value)
    }
}

fun <T> CancellableContinuation<T>.resumeWithExceptionIfActive(cause: Throwable? = null) {
    if (isActive && cause != null) {
        resumeWithException(cause)
    }
}
