package com.flutterup.base.compose.extension

import android.content.ClipData
import androidx.compose.ui.platform.ClipEntry
import androidx.compose.ui.platform.Clipboard


/**
 * @receiver Clipboard
 *
 * ``` kotlin
 * @Composable
 * fun Screen() {
 *  val clipboard = LocalClipboard.current
 *  val scope = rememberCoroutineScope()
 *  Button(onClick = {
 *      scope.launch {
 *          clipboard.copyToClipboard("Hello World")
 *      }
 *  }) {
 *      Text("Copy")
 *  }
 * }
 * ```
 *
 * @param text String
 * @param label String
 */
suspend fun Clipboard.copyToClipboard(text: String, label: String = "Copy") {
    val clipData = ClipData.newPlainText(label, text)
    val clipEntry = ClipEntry(clipData)
    setClipEntry(clipEntry)
}