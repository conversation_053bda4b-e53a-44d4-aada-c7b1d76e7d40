package com.flutterup.base.compose.refresh

import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.verticalDrag
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.pointer.positionChange
import kotlinx.coroutines.launch
import kotlin.math.abs

/**
 * 扩展函数，为 Modifier 添加上拉加载更多功能
 * 参考 PullToRefreshBox 的实现方式
 */
@Composable
fun Modifier.pullToLoadMoreGesture(
    state: PullToLoadMoreState,
    lazyListState: LazyListState,
    enabled: Boolean = true,
    thresholdDp: Float = 80f,
    onLoadMore: () -> Unit
): Modifier {
    val coroutineScope = rememberCoroutineScope()

    return this.pointerInput(state, enabled, lazyListState) {
        if (!enabled) return@pointerInput

        val thresholdPx = thresholdDp * density
        var hasTriggered = false

        awaitEachGesture {
            val down = awaitFirstDown()

            // 检查是否在底部
            val layoutInfo = lazyListState.layoutInfo
            val totalItemsCount = layoutInfo.totalItemsCount
            val lastVisibleItemIndex = layoutInfo.visibleItemsInfo.lastOrNull()?.index ?: -1
            val isAtBottom = totalItemsCount > 0 && lastVisibleItemIndex == totalItemsCount - 1

            if (!isAtBottom) return@awaitEachGesture

            var totalDelta = 0f

            // 开始垂直拖拽
            verticalDrag(down.id) { change ->
                val delta = change.positionChange().y

                // 只处理向上拖拽（负值）
                if (delta < 0) {
                    totalDelta += abs(delta)

                    // 更新状态的距离比例
                    val distanceFraction = (totalDelta / thresholdPx).coerceAtMost(1.5f)
                    coroutineScope.launch {
                        state.snapTo(distanceFraction)
                    }

                    // 检查是否达到触发阈值
                    if (totalDelta >= thresholdPx && !hasTriggered) {
                        hasTriggered = true
                        onLoadMore()
                    }

                    // 消费事件，防止列表滚动
                    change.consume()
                } else if (delta > 0) {
                    // 向下拖拽时重置
                    totalDelta = 0f
                    hasTriggered = false
                    coroutineScope.launch {
                        state.snapTo(0f)
                    }
                }
            }

            // 拖拽结束，重置状态
            hasTriggered = false
            coroutineScope.launch {
                state.animateToHidden()
            }
        }
    }
}
