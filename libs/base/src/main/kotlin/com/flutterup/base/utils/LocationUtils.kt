package com.flutterup.base.utils

import android.Manifest
import android.content.Context
import android.location.Geocoder
import android.location.Location
import android.location.LocationManager
import androidx.annotation.RequiresPermission
import com.flutterup.base.BaseApplication
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.Priority
import com.google.android.gms.tasks.CancellationTokenSource
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import java.util.Locale
import kotlin.coroutines.resume

/**
 * 位置信息数据类
 */
data class LocationData(
    val lat: Double,
    val lng: Double,
    val country: String = "",
    val state: String = "",
    val city: String = ""
) {
    companion object {
        val EMPTY = LocationData(0.0, 0.0, "", "", "")
    }
}

/**
 * 位置工具类
 * 支持Google Play Services Location和原生定位
 * 内存缓存最后一次获取的位置信息
 */

object LocationUtils {

    private const val TAG = "LocationUtils"
    private const val LOCATION_TIMEOUT_MS = 10000L // 10秒超时
    private const val MIN_ACCURACY_METERS = 100f // 最小精度要求

    // 内存缓存
    private var cachedLocationData: LocationData? = null

    // Google Play Services Location客户端
    private val fusedLocationClient: FusedLocationProviderClient by lazy {
        LocationServices.getFusedLocationProviderClient(BaseApplication.getApplicationContext())
    }

    // 原生LocationManager
    private val locationManager: LocationManager by lazy {
        BaseApplication.getApplicationContext().getSystemService(Context.LOCATION_SERVICE) as LocationManager
    }

    // Geocoder用于地址解析
    private val geocoder: Geocoder by lazy {
        Geocoder(BaseApplication.getApplicationContext(), Locale.getDefault())
    }

    /**
     * 获取当前位置信息
     * 如果已有缓存则直接返回，否则优先使用Google Play Services，失败时使用原生定位
     */
    @RequiresPermission(anyOf = [Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION])
    suspend fun getCurrentLocation(): LocationData? {
        // 如果已有缓存，直接返回
        cachedLocationData?.let { cached ->
            Timber.d(TAG, "Returning cached location: ${cached.lat}, ${cached.lng}")
            return cached
        }

        return try {
            // 首先尝试Google Play Services Location
            getLocationFromGoogleServices() ?: run {
                Timber.w(TAG, "Google Services location failed, trying native location")
                // 失败时使用原生定位
                getLocationFromNative()
            }
        } catch (e: Exception) {
            Timber.e(TAG, "Failed to get current location", e)
            null
        }
    }

    /**
     * 获取缓存的位置信息
     */
    fun getCachedLocation(): LocationData? {
        return cachedLocationData
    }

    /**
     * 清除位置缓存
     */
    fun clearLocationCache() {
        cachedLocationData = null
        Timber.d(TAG, "Location cache cleared")
    }

    /**
     * 使用Google Play Services获取位置
     */
    @RequiresPermission(anyOf = [Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION])
    private suspend fun getLocationFromGoogleServices(): LocationData? {
        return try {
            withTimeoutOrNull(LOCATION_TIMEOUT_MS) {
                suspendCancellableCoroutine { continuation ->
                    val cancellationTokenSource = CancellationTokenSource()

                    continuation.invokeOnCancellation {
                        cancellationTokenSource.cancel()
                    }

                    fusedLocationClient.getCurrentLocation(
                        Priority.PRIORITY_HIGH_ACCURACY,
                        cancellationTokenSource.token
                    ).addOnSuccessListener { location ->
                        if (location == null) {
                            Timber.w(TAG, "Google Services location is null")
                            continuation.resume(null)
                            return@addOnSuccessListener
                        }

                        Timber.d(TAG, "Got location from Google Services: ${location.latitude}, ${location.longitude}")
                        // 异步进行地址解析
                        resolveLocationToAddress(location) { locationData ->
                            if (locationData != null) {
                                cacheLocation(locationData)
                                continuation.resume(locationData)
                            } else {
                                continuation.resume(null)
                            }
                        }
                    }.addOnFailureListener { exception ->
                        Timber.e(TAG, "Google Services location failed", exception)
                        continuation.resume(null)
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(TAG, "Error getting location from Google Services", e)
            null
        }
    }

    /**
     * 使用原生LocationManager获取位置
     */
    @RequiresPermission(anyOf = [Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION])
    private suspend fun getLocationFromNative(): LocationData? {
        return try {
            withTimeoutOrNull(LOCATION_TIMEOUT_MS) {
                suspendCancellableCoroutine { continuation ->
                    val providers = locationManager.getProviders(true)
                    if (providers.isEmpty()) {
                        Timber.w(TAG, "No location providers available")
                        continuation.resume(null)
                        return@suspendCancellableCoroutine
                    }

                    // 优先使用GPS，然后是网络定位
                    val provider = when {
                        providers.contains(LocationManager.GPS_PROVIDER) -> LocationManager.GPS_PROVIDER
                        providers.contains(LocationManager.NETWORK_PROVIDER) -> LocationManager.NETWORK_PROVIDER
                        else -> providers.first()
                    }

                    try {
                        val lastKnownLocation = locationManager.getLastKnownLocation(provider)
                        if (lastKnownLocation != null &&
                            System.currentTimeMillis() - lastKnownLocation.time < 5 * 60 * 1000) { // 5分钟内的位置

                            Timber.d(TAG, "Got location from native provider $provider: ${lastKnownLocation.latitude}, ${lastKnownLocation.longitude}")
                            // 异步进行地址解析
                            resolveLocationToAddress(lastKnownLocation) { locationData ->
                                if (locationData != null) {
                                    cacheLocation(locationData)
                                    continuation.resume(locationData)
                                } else {
                                    continuation.resume(null)
                                }
                            }
                        } else {
                            Timber.w(TAG, "Native location is null or too old")
                            continuation.resume(null)
                        }
                    } catch (e: SecurityException) {
                        Timber.e(TAG, "Security exception getting native location", e)
                        continuation.resume(null)
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(TAG, "Error getting location from native provider", e)
            null
        }
    }

    /**
     * 将Location对象解析为包含地址信息的LocationData
     */
    private fun resolveLocationToAddress(location: Location, callback: (LocationData?) -> Unit) {
        try {
            if (!Geocoder.isPresent()) {
                Timber.w(TAG, "Geocoder not available")
                // 如果Geocoder不可用，返回只有坐标的LocationData
                val locationData = LocationData(
                    lat = location.latitude,
                    lng = location.longitude
                )
                callback(locationData)
                return
            }

            // 使用Geocoder进行反向地理编码
            val addresses = geocoder.getFromLocation(location.latitude, location.longitude, 1)
            if (addresses?.isNotEmpty() == true) {
                val address = addresses[0]
                val locationData = LocationData(
                    lat = location.latitude,
                    lng = location.longitude,
                    country = address.countryName ?: "",
                    state = address.adminArea ?: "",
                    city = address.locality ?: address.subAdminArea ?: ""
                )
                Timber.d(TAG, "Resolved address: ${locationData.country}, ${locationData.state}, ${locationData.city}")
                callback(locationData)
            } else {
                Timber.w(TAG, "No address found for location")
                // 如果没有找到地址，返回只有坐标的LocationData
                val locationData = LocationData(
                    lat = location.latitude,
                    lng = location.longitude
                )
                callback(locationData)
            }
        } catch (e: Exception) {
            Timber.e(TAG, "Error resolving location to address", e)
            // 出错时返回只有坐标的LocationData
            val locationData = LocationData(
                lat = location.latitude,
                lng = location.longitude
            )
            callback(locationData)
        }
    }

    /**
     * 缓存位置信息到内存
     */
    private fun cacheLocation(locationData: LocationData) {
        cachedLocationData = locationData
        Timber.d(TAG, "Location cached: ${locationData.lat}, ${locationData.lng}")
    }
}