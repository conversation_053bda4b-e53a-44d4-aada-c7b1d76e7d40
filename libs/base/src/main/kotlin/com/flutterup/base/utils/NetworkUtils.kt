package com.flutterup.base.utils

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkInfo
import androidx.annotation.RequiresPermission
import com.flutterup.base.BaseApplication

object NetworkUtils {
    /**
     * 是否开了代理或者vpn
     * @return 1:开了代理或者vpn 0:都未开启
     */
    @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
    fun isVpnOrProxyActive(): Int {
        return if (isWifiProxy() || isVpnActive(BaseApplication.getApplicationContext())) 1 else 0
    }

    fun isWifiProxy(): <PERSON>olean {
        try {
            val proxyPort: Int
            val proxyAddress = System.getProperty("http.proxyHost")
            val portStr = System.getProperty("http.proxyPort")
            proxyPort = (portStr ?: "-1").toInt()
            return !proxyAddress.isNullOrEmpty() && proxyPort != -1
        } catch (_: Exception) {
        }
        return false
    }

    @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
    @SuppressLint("WrongConstant")
    fun isVpnActive(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkInfo: NetworkInfo? = connectivityManager.getNetworkInfo(ConnectivityManager.TYPE_VPN)
        return networkInfo?.isConnected ?: false
    }
}