package com.flutterup.base.compose

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import com.flutterup.base.BaseFragment

abstract class BaseComposeFragment : BaseFragment() {

    override fun getContentView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return setContentWithView { Screen() }
    }

    @Composable
    abstract fun Screen()
}
