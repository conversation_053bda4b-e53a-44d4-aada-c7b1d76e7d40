package com.flutterup.tracking

import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustEvent
import com.flutterup.base.utils.Timber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Adjust埋点服务实现
 */
@Singleton
class AdjustTrackingService @Inject constructor() : TrackingService {
    
    private val tag = "AdjustTracking"
    private var presetProperties: MutableMap<String, Any> = mutableMapOf()
    
    override suspend fun track(event: TrackingEvent) {
        withContext(Dispatchers.IO) {
            try {
                val eventName = event.getFinalEventName()
                val adjustEvent = AdjustEvent(getAdjustToken(eventName))
                
                // 合并预置属性和事件属性
                val finalProperties = mutableMapOf<String, Any>()
                finalProperties.putAll(presetProperties)
                finalProperties.putAll(event.properties)
                
                // 添加属性到Adjust事件
                finalProperties.forEach { (key, value) ->
                    when (value) {
                        is String -> adjustEvent.addCallbackParameter(key, value)
                        is Number -> adjustEvent.addCallbackParameter(key, value.toString())
                        is Boolean -> adjustEvent.addCallbackParameter(key, value.toString())
                        else -> adjustEvent.addCallbackParameter(key, value.toString())
                    }
                }
                
                // 发送事件
                Adjust.trackEvent(adjustEvent)
                
                // 记录日志
                val config = event.config
                if (config?.enableLogging != false) {
                    Timber.i(tag, "Adjust event tracked: $eventName, properties: $finalProperties")
                }
                
            } catch (e: Exception) {
                Timber.e(tag, "Failed to track Adjust event: ${event.getFinalEventName()}", e)
            }
        }
    }
    
    override fun setPresetProperties(properties: Map<String, Any>) {
        presetProperties.clear()
        presetProperties.putAll(properties)
        Timber.d(tag, "Preset properties updated: $properties")
    }
    
    override fun getPresetProperties(): Map<String, Any> {
        return presetProperties.toMap()
    }

    override fun getTrackingId(callback: (String) -> Unit) {
        Adjust.getAdid { adid ->
            callback(adid ?: "")
            Timber.i(tag, "Adjust id: $adid")
        }
    }
    
    /**
     * 根据事件名称获取Adjust Token
     * 这里需要根据实际的Adjust配置来映射
     */
    private fun getAdjustToken(eventName: String): String {
        //adjust 事件使用默认名字
        return eventName
    }
}
