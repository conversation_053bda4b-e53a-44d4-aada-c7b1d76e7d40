package com.flutterup.tracking.di

import com.flutterup.network.AppClient
import com.flutterup.network.AppClients
import com.flutterup.network.impl.NetworkServiceProvider
import com.flutterup.tracking.AdjustTrackingService
import com.flutterup.tracking.InternalTrackingService
import com.flutterup.tracking.TrackingApiService
import com.flutterup.tracking.TrackingServiceFactory
import com.squareup.moshi.Moshi
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object TrackModule {

    @Singleton
    @Provides
    fun provideTrackingApiService(@AppClient(AppClients.NORMAL) networkService: NetworkServiceProvider): TrackingApiService {
        return networkService[TrackingApiService::class.java]
    }

    @Singleton
    @Provides
    fun provideAdjustTrackingService(): AdjustTrackingService {
        return AdjustTrackingService()
    }

    @<PERSON>ton
    @Provides
    fun provideInternalTrackingService(
        trackingApiService: TrackingApiService,
        moshi: Moshi
    ): InternalTrackingService {
        return InternalTrackingService(trackingApiService, moshi)
    }

    @Singleton
    @Provides
    fun provideTrackingServiceFactory(
        adjustTrackingService: AdjustTrackingService,
        internalTrackingService: InternalTrackingService
    ): TrackingServiceFactory {
        return TrackingServiceFactory(adjustTrackingService, internalTrackingService)
    }
}