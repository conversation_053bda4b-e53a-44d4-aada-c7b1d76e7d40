package com.flutterup.tracking

import com.flutterup.base.utils.Timber
import com.squareup.moshi.Moshi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 内部API埋点服务实现
 */
@Singleton
class InternalTrackingService @Inject constructor(
    private val trackingApiService: TrackingApiService,
    private val moshi: Moshi
) : TrackingService {
    
    private val tag = "InternalTracking"
    private var presetProperties: MutableMap<String, Any> = mutableMapOf()
    
    override suspend fun track(event: TrackingEvent) {
        withContext(Dispatchers.IO) {
            try {
                val eventName = event.getFinalEventName()
                
                // 合并预置属性和事件属性
                val finalProperties = mutableMapOf<String, Any>()
                finalProperties.putAll(presetProperties)
                finalProperties.putAll(event.properties)
                
                // 将属性转换为JSON字符串
                val propertiesJson = moshi.adapter(Map::class.java).toJson(finalProperties)
                
                // 调用API
                val response = trackingApiService.tracking(
                    key = eventName,
                    value = propertiesJson
                )
                
                // 记录日志
                val config = event.config
                if (config?.enableLogging != false) {
                    if (response.isSuccess) {
                        Timber.i(tag, "Internal API event tracked: $eventName, properties: $finalProperties")
                    } else {
                        Timber.w(tag, "Internal API event tracking failed: $eventName, error: ${response.message}")
                    }
                }
                
            } catch (e: Exception) {
                Timber.e(tag, "Failed to track internal API event: ${event.getFinalEventName()}", e)
            }
        }
    }
    
    override fun setPresetProperties(properties: Map<String, Any>) {
        presetProperties.clear()
        presetProperties.putAll(properties)
        Timber.d(tag, "Preset properties updated: $properties")
    }
    
    override fun getPresetProperties(): Map<String, Any> {
        return presetProperties.toMap()
    }

    override fun getTrackingId(callback: (String) -> Unit) {
        //ignore this
    }
}
