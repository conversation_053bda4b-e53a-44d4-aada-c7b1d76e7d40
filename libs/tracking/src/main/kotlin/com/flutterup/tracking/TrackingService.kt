package com.flutterup.tracking

/**
 * 埋点服务接口
 */
interface TrackingService {
    
    /**
     * 发送埋点事件
     * @param event 埋点事件
     */
    suspend fun track(event: TrackingEvent)
    
    /**
     * 设置预置属性
     * @param properties 预置属性
     */
    fun setPresetProperties(properties: Map<String, Any>)
    
    /**
     * 获取预置属性
     */
    fun getPresetProperties(): Map<String, Any>


    fun getTrackingId(callback: (String) -> Unit)
}
