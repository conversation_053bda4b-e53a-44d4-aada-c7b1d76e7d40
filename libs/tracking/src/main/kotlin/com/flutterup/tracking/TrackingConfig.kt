package com.flutterup.tracking

/**
 * 埋点上报方式
 */
enum class TrackingReportType {
    /**
     * 使用Adjust SDK上报
     */
    ADJUST,

    /**
     * 使用内部API上报
     */
    INTERNAL
}

/**
 * 埋点配置
 */
data class TrackingConfig(
    /**
     * 上报方式
     */
    val reportType: TrackingReportType = TrackingReportType.INTERNAL,

    /**
     * 是否启用日志
     */
    val enableLogging: Boolean = true,

    /**
     * 预置属性
     */
    val presetProperties: Map<String, Any> = emptyMap(),

    /**
     * 动态预置属性提供者
     * 在发送埋点时会调用此函数获取最新的预置属性
     */
    val dynamicPresetPropertiesProvider: (() -> Map<String, Any>)? = null
)
