plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
}

apply(from = "${rootDir}/gradles/buildBase.gradle")
apply(from = "${rootDir}/gradles/buildHilt.gradle")

android {
    namespace = "com.flutterup.tracking"
}

dependencies {
    implementation(project(":libs:base"))
    implementation(project(":libs:network"))

    implementation(libs.adjust)
    implementation(libs.adjust.installreferrer)
    implementation(libs.google.services.ads.identifier)
}