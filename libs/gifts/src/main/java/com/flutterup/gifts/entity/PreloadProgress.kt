package com.flutterup.gifts.entity

/**
 * 预加载进度状态
 */
sealed class PreloadProgress {
    /**
     * 空闲状态
     */
    object Idle : PreloadProgress()

    /**
     * 进行中状态
     * @param completed 已完成数量
     * @param total 总数量
     * @param successfulGifts 成功的礼物ID列表
     * @param failedGifts 失败的礼物ID列表
     * @param cachedGifts 已缓存的礼物数据（实时更新）
     */
    data class InProgress(
        val completed: Int,
        val total: Int,
        val successfulGifts: List<String>,
        val failedGifts: List<String>,
        val cachedGifts: List<GiftEntity> = emptyList()
    ) : PreloadProgress() {

        /**
         * 获取进度百分比 (0-100)
         */
        val progressPercentage: Int
            get() = if (total > 0) (completed * 100 / total) else 0

        /**
         * 获取成功率百分比 (0-100)
         */
        val successRate: Int
            get() = if (completed > 0) (successfulGifts.size * 100 / completed) else 100
    }

    /**
     * 完成状态
     * @param result 预加载结果（包含完整的数据库数据）
     */
    data class Completed(val result: PreloadResult) : PreloadProgress()
}
