package com.flutterup.gifts.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey


@Entity(tableName = "gift_cache")
data class GiftEntity(
    @PrimaryKey val giftId: String,

    @ColumnInfo(name = "name")
    val name: String?,

    @ColumnInfo(name = "price")
    val price: Int?,

    @ColumnInfo(name = "image_url")
    val imageUrl: String?,

    @ColumnInfo(name = "gif_url")
    val gifUrl: String?,

    @ColumnInfo(name = "video_url")
    val videoUrl: String?,

    @ColumnInfo(name = "image_path")
    val imagePath: String?,

    @ColumnInfo(name = "gif_path")
    val gifPath: String?,

    @ColumnInfo(name = "video_path")
    val videoPath: String?,

    @ColumnInfo(name = "version")
    val version: Long,

    @ColumnInfo(name = "desc")
    val desc: String?,

    @ColumnInfo(name = "last_updated")
    val lastUpdated: Long = System.currentTimeMillis() // 最后一次更新时间
)

/**
 * 将 GiftResourceInfo 和 GiftCacheEntity 合并为 GiftEntity
 */
fun GiftResourceInfo.toGiftEntity(entity: GiftEntity): GiftEntity {
    return GiftEntity(
        giftId = this.giftId,
        name = this.name,
        price = this.price,
        imageUrl = this.image,
        gifUrl = this.gif,
        videoUrl = this.video,
        imagePath = entity.imagePath,
        gifPath = entity.gifPath,
        videoPath = entity.videoPath,
        desc = this.desc,
        version = this.version
    )
}

fun GiftEntity.toGiftResourceInfo(): GiftResourceInfo {
    return GiftResourceInfo(
        giftId = this.giftId,
        name = this.name,
        price = this.price,
        image = this.imageUrl,
        gif = this.gifUrl,
        video = this.videoUrl,
        desc = this.desc,
        version = this.version
    )
}