package com.flutterup.gifts.di

import android.content.Context
import com.flutterup.gifts.database.GiftDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Singleton
    @Provides
    fun provideGiftDatabase(@ApplicationContext context: Context): GiftDatabase {
        return GiftDatabase.getInstance(context)
    }
}