package com.flutterup.gifts.entity

/**
 * 预加载结果
 */
sealed class PreloadResult {
    abstract val successfulGifts: List<String>
    abstract val failedGifts: List<String>
    abstract val cachedGiftsData: List<GiftEntity>

    /**
     * 完全成功
     * @param successfulGifts 成功预加载的礼物ID列表
     * @param failedGifts 失败的礼物ID列表（应该为空）
     * @param cachedGiftsData 成功缓存的礼物数据
     */
    data class Success(
        override val successfulGifts: List<String>,
        override val failedGifts: List<String>,
        override val cachedGiftsData: List<GiftEntity>
    ) : PreloadResult()

    /**
     * 部分成功
     * @param successfulGifts 成功预加载的礼物ID列表
     * @param failedGifts 失败的礼物ID列表
     * @param cachedGiftsData 成功缓存的礼物数据
     */
    data class PartialSuccess(
        override val successfulGifts: List<String>,
        override val failedGifts: List<String>,
        override val cachedGiftsData: List<GiftEntity>
    ) : PreloadResult()

    /**
     * 完全失败
     * @param error 错误信息
     * @param successfulGifts 成功预加载的礼物ID列表（应该为空）
     * @param failedGifts 失败的礼物ID列表
     * @param cachedGiftsData 成功缓存的礼物数据（可能为空）
     */
    data class Error(
        val error: Throwable,
        override val successfulGifts: List<String>,
        override val failedGifts: List<String>,
        override val cachedGiftsData: List<GiftEntity>
    ) : PreloadResult()
    
    /**
     * 获取总数量
     */
    val totalCount: Int
        get() = successfulGifts.size + failedGifts.size
    
    /**
     * 获取成功率 (0.0 - 1.0)
     */
    val successRate: Float
        get() = if (totalCount > 0) successfulGifts.size.toFloat() / totalCount else 1.0f
    
    /**
     * 是否完全成功
     */
    val isCompleteSuccess: Boolean
        get() = this is Success && failedGifts.isEmpty()
    
    /**
     * 是否有失败
     */
    val hasFailures: Boolean
        get() = failedGifts.isNotEmpty()

    /**
     * 根据礼物ID获取缓存数据
     */
    fun getCachedGiftById(giftId: String): GiftEntity? {
        return cachedGiftsData.find { it.giftId == giftId }
    }

    /**
     * 获取所有成功缓存的礼物数据
     */
    fun getSuccessfulCachedGifts(): List<GiftEntity> {
        return cachedGiftsData.filter { it.giftId in successfulGifts }
    }

    /**
     * 获取失败的礼物ID和对应的缓存数据（如果有的话）
     */
    fun getFailedGiftsWithCachedData(): Map<String, GiftEntity?> {
        return failedGifts.associateWith { giftId ->
            cachedGiftsData.find { it.giftId == giftId }
        }
    }
}
