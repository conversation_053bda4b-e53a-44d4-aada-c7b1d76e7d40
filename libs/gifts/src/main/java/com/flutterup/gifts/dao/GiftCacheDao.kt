package com.flutterup.gifts.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.flutterup.gifts.entity.GiftEntity

@Dao
interface GiftCacheDao {

    @Query("SELECT * FROM gift_cache WHERE giftId = :giftId")
    suspend fun getGift(giftId: String): GiftEntity?

    @Query("SELECT * FROM gift_cache WHERE giftId IN (:giftIds)")
    suspend fun getGifts(giftIds: List<String>): List<GiftEntity>

    @Query("SELECT * FROM gift_cache")
    suspend fun getAllGifts(): List<GiftEntity>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGift(gift: GiftEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGifts(gifts: List<GiftEntity>)

    @Query("DELETE FROM gift_cache WHERE giftId = :giftId")
    suspend fun deleteGift(giftId: String)

    @Query("DELETE FROM gift_cache WHERE giftId IN (:giftIds)")
    suspend fun deleteGifts(giftIds: List<String>)
}