# Gift Preload Manager

Gift预加载管理器，支持批量预加载礼物资源，提供进度跟踪和并发控制，**最新版本使用 GiftEntity 数据结构**。

## 🚀 最新功能亮点

- ✅ **使用 GiftEntity 数据结构** - 预加载结果直接返回完整的 GiftEntity 对象
- ✅ **实时 GiftEntity 结果返回** - 预加载过程中实时获取已缓存的完整礼物数据
- ✅ **完整礼物信息** - 包含价格、URL、本地路径等完整信息
- ✅ **进度跟踪包含完整数据** - 可以在预加载过程中提前显示完整的礼物信息
- ✅ **🆕 自动清理过时缓存** - 自动删除不在当前礼物列表中的过时缓存数据和文件

## 功能特性

- ✅ 支持批量预加载礼物列表
- ✅ 并发下载控制，可配置并发数
- ✅ 实时进度跟踪
- ✅ **实时返回 GiftEntity 结果**
- ✅ **预加载完成后直接获取完整 GiftEntity 数据**
- ✅ **🆕 智能缓存清理** - 自动清理过时的缓存数据和文件
- ✅ 多种预加载策略
- ✅ 错误处理和重试机制
- ✅ 取消预加载支持
- ✅ 协程支持（同步和异步API）
- ✅ Hilt依赖注入

## 🆕 自动清理功能

### 智能缓存管理
预加载管理器现在支持自动清理过时的缓存数据：

```kotlin
giftPreloadManager.preloadGifts(
    gifts = currentGiftList,
    cleanupObsolete = true, // 🆕 自动清理过时缓存
    onProgress = { progress ->
        // 预加载过程中会自动清理不在 currentGiftList 中的缓存
    }
)
```

### 清理逻辑
1. **数据库清理** - 删除不在当前礼物列表中的缓存记录
2. **文件清理** - 删除对应的图片、GIF、视频文件
3. **日志记录** - 详细记录清理过程和结果

### 使用场景
- **应用启动** - 清理过时的礼物缓存 (`cleanupObsolete = true`)
- **增量更新** - 只添加新礼物，保留现有缓存 (`cleanupObsolete = false`)
- **特定场景** - 临时预加载，不影响其他缓存 (`cleanupObsolete = false`)

## 核心优化

### 1. 实时 GiftEntity 结果
```kotlin
giftPreloadManager.preloadGifts(
    gifts = giftList,
    onProgress = { progress ->
        when (progress) {
            is PreloadProgress.InProgress -> {
                // 🆕 实时获取已缓存的完整 GiftEntity 数据
                progress.cachedGifts.forEach { gift ->
                    // 可以立即使用完整的礼物信息
                    println("Gift ${gift.giftId} (${gift.name}) - Price: ${gift.price}")
                    println("  Image: ${gift.image} -> ${gift.imagePath}")
                    println("  GIF: ${gift.gif} -> ${gift.gifPath}")
                    println("  Video: ${gift.video} -> ${gift.videoPath}")

                    // 可以直接在UI中显示礼物
                    displayGiftInShop(gift)
                }
            }
        }
    }
)
```

### 2. 完成后直接获取 GiftEntity 数据
```kotlin
suspend fun preloadAndUse() {
    val result = giftPreloadManager.preloadGiftsAsync(giftList)

    when (result) {
        is PreloadResult.Success -> {
            // 🆕 直接使用 GiftEntity 数据，包含完整信息
            result.cachedGiftsData.forEach { gift ->
                displayGiftInShop(
                    id = gift.giftId,
                    name = gift.name,
                    price = gift.price,
                    imageUrl = gift.image,
                    imagePath = gift.imagePath,
                    gifUrl = gift.gif,
                    gifPath = gift.gifPath,
                    videoUrl = gift.video,
                    videoPath = gift.videoPath,
                    version = gift.version
                )
            }
        }
        is PreloadResult.PartialSuccess -> {
            // 获取成功缓存的 GiftEntity
            val successfulGifts = result.getSuccessfulCachedGifts()
            successfulGifts.forEach { gift ->
                displayGiftInShop(gift)
            }
        }
    }
}
```

### 3. 便利方法 (返回 GiftEntity)
```kotlin
// 🆕 根据ID查找特定 GiftEntity
val specificGift: GiftEntity? = result.getCachedGiftById("gift_123")
if (specificGift != null) {
    println("Found: ${specificGift.name} - ${specificGift.price} coins")
    displayGiftInShop(specificGift)
}

// 🆕 获取所有成功缓存的 GiftEntity
val successfulGifts: List<GiftEntity> = result.getSuccessfulCachedGifts()
successfulGifts.forEach { gift ->
    displayGiftInShop(gift)
}

// 🆕 获取失败礼物的 GiftEntity 缓存状态
val failedWithCache: Map<String, GiftEntity?> = result.getFailedGiftsWithCachedData()
failedWithCache.forEach { (giftId, giftEntity) ->
    if (giftEntity != null) {
        // 使用旧版本的缓存数据
        displayGiftInShop(giftEntity, isOldVersion = true)
    }
}
```

## 数据结构更新

### PreloadProgress (优化版)
```kotlin
data class InProgress(
    val completed: Int,
    val total: Int,
    val successfulGifts: List<String>,
    val failedGifts: List<String>,
    val cachedGifts: List<GiftCacheEntity> = emptyList() // 🆕 实时缓存数据
) : PreloadProgress()
```

### PreloadResult (优化版)
```kotlin
sealed class PreloadResult {
    abstract val cachedGiftsData: List<GiftCacheEntity> // 🆕 数据库数据
    
    data class Success(
        override val successfulGifts: List<String>,
        override val failedGifts: List<String>,
        override val cachedGiftsData: List<GiftCacheEntity> // 🆕 完整数据库数据
    ) : PreloadResult()
    
    // 便利方法
    fun getCachedGiftById(giftId: String): GiftCacheEntity?
    fun getSuccessfulCachedGifts(): List<GiftCacheEntity>
    fun getFailedGiftsWithCachedData(): Map<String, GiftCacheEntity?>
}
```

## 使用示例

### 基本使用 - 实时数据 + 自动清理
```kotlin
@Inject
lateinit var giftPreloadManager: GiftPreloadManager

fun preloadWithRealTimeData() {
    giftPreloadManager.preloadGifts(
        gifts = giftList,
        cleanupObsolete = true, // 🆕 自动清理过时缓存
        onProgress = { progress ->
            when (progress) {
                is PreloadProgress.InProgress -> {
                    // 实时显示已缓存的礼物
                    updateUI("${progress.progressPercentage}% completed")
                    
                    // 立即使用已缓存的数据
                    progress.cachedGifts.forEach { gift ->
                        showGiftInUI(gift) // 可以立即显示
                    }
                }
                is PreloadProgress.Completed -> {
                    // 获取完整的数据库数据
                    val allCachedGifts = progress.result.cachedGiftsData
                    displayAllGifts(allCachedGifts)
                }
            }
        }
    )
}
```

### 异步使用 - 直接获取结果
```kotlin
suspend fun preloadAndGetData(): List<GiftCacheEntity> {
    val result = giftPreloadManager.preloadGiftsAsync(
        gifts = giftList,
        onProgress = { progress ->
            if (progress is PreloadProgress.InProgress) {
                // 可以提前使用部分数据
                usePartialData(progress.cachedGifts)
            }
        }
    )
    
    return when (result) {
        is PreloadResult.Success -> result.cachedGiftsData
        is PreloadResult.PartialSuccess -> result.getSuccessfulCachedGifts()
        is PreloadResult.Error -> {
            // 即使失败也可能有部分数据可用
            result.cachedGiftsData
        }
    }
}
```

### 状态监听 - 实时更新UI
```kotlin
lifecycleScope.launch {
    giftPreloadManager.preloadProgress.collect { progress ->
        when (progress) {
            is PreloadProgress.InProgress -> {
                // 实时更新进度条
                progressBar.progress = progress.progressPercentage
                
                // 实时显示已缓存的礼物
                giftAdapter.updateCachedGifts(progress.cachedGifts)
                
                // 可以提前启用相关功能
                if (progress.cachedGifts.isNotEmpty()) {
                    enableGiftFeatures()
                }
            }
            is PreloadProgress.Completed -> {
                // 显示最终结果
                displayFinalResults(progress.result.cachedGiftsData)
            }
        }
    }
}
```

### 🆕 清理功能使用示例
```kotlin
// 场景1：应用启动时的完整预加载（推荐清理）
fun startupPreload() {
    val allCurrentGifts = getCurrentGiftList()
    giftPreloadManager.preloadGifts(
        gifts = allCurrentGifts,
        cleanupObsolete = true, // 清理不再使用的礼物
        onComplete = { result ->
            println("Startup preload completed, obsolete cache cleaned")
        }
    )
}

// 场景2：增量更新（不清理，保留现有缓存）
fun incrementalUpdate() {
    val newGifts = getNewGifts()
    giftPreloadManager.preloadGifts(
        gifts = newGifts,
        cleanupObsolete = false, // 不清理，只添加新的
        onComplete = { result ->
            println("New gifts added, existing cache preserved")
        }
    )
}

// 场景3：异步预加载with清理控制
suspend fun controlledAsyncPreload(shouldCleanup: Boolean) {
    val result = giftPreloadManager.preloadGiftsAsync(
        gifts = giftList,
        cleanupObsolete = shouldCleanup,
        onProgress = { progress ->
            if (shouldCleanup) {
                println("Cleaning up obsolete gifts...")
            }
        }
    )
}
```

## 性能优势

1. **减少数据库查询** - 预加载完成后直接返回数据库数据，无需额外查询
2. **实时用户体验** - 可以在预加载过程中提前显示已完成的礼物
3. **更好的错误处理** - 即使部分失败，也能获取成功的数据
4. **内存效率** - 只在需要时查询数据库，避免重复查询
5. **🆕 存储优化** - 自动清理过时缓存，节省存储空间

## 最佳实践

1. **实时显示** - 利用进度回调中的缓存数据提前显示内容
2. **渐进式加载** - 随着预加载进度逐步显示更多礼物
3. **错误恢复** - 使用部分成功的数据提供降级体验
4. **缓存验证** - 检查返回的缓存数据的完整性和有效性
5. **🆕 智能清理** - 在应用启动时启用清理，增量更新时禁用清理
6. **🆕 存储管理** - 定期使用清理功能来管理存储空间

## 注意事项

- 进度回调中的数据库查询是异步的，可能有轻微延迟
- 建议在WiFi环境下进行大量预加载
- 缓存数据包含完整的文件路径，可以直接用于UI显示
- 预加载失败不会影响正常的礼物显示功能
- **🆕 清理功能注意事项**：
  - 清理操作是不可逆的，请谨慎使用
  - 清理失败不会影响预加载流程
  - 建议在应用启动时进行清理，避免在用户活跃时执行
  - 增量更新时建议禁用清理以保留现有缓存
