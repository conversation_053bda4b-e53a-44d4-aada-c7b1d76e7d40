package com.flutterup.players.compose

import android.annotation.SuppressLint
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.media3.ui.PlayerView

@SuppressLint("UnsafeOptInUsageError")
@Composable
fun VideoPlayer(
    exoPlayer: ExoPlayer,
    videoPath: String,
    isPlaying: Boolean
) {
    val context = LocalContext.current
    val playerView = remember {
        exoPlayer.apply {
            setMediaItem(MediaItem.fromUri(videoPath), true)
            prepare()
        }
        PlayerView(context).apply {
            resizeMode = AspectRatioFrameLayout.RESIZE_MODE_FIXED_WIDTH
            useController = true
            controllerAutoShow = true
            controllerHideOnTouch = true
            controllerShowTimeoutMs = 1000
            player = exoPlayer
        }
    }
    AndroidView(factory = { playerView })

    exoPlayer.playWhenReady = isPlaying
}