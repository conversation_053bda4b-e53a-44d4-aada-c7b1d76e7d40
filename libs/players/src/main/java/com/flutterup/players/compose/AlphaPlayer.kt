package com.flutterup.players.compose

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.flutterup.players.view.AlphaExoPlayerView
import com.ss.ugc.android.alpha_player.IMonitor
import com.ss.ugc.android.alpha_player.IPlayerAction
import com.ss.ugc.android.alpha_player.player.AbsPlayer

/**
 * AlphaPlayer Compose 组件
 *
 * @param playerState 播放器状态管理
 * @param modifier 修饰符
 * @param config 播放器配置
 * @param player 自定义播放器实现
 * @param playerAction 播放器动作监听
 * @param monitor 播放器监控
 */
@Composable
fun AlphaPlayer(
    playerState: AlphaPlayerState,
    modifier: Modifier = Modifier,
    config: PlayerConfig = PlayerConfig(),
    player: AbsPlayer? = null,
    playerAction: IPlayerAction? = null,
    monitor: IMonitor? = null
) {
    val lifecycleOwner = LocalLifecycleOwner.current

    Box(modifier = modifier) {
        AndroidView(
            factory = { ctx ->
                AlphaExoPlayerView(ctx).apply {
                    // 初始化播放器控制器
                    initPlayerController(
                        context = ctx,
                        owner = lifecycleOwner,
                        player = player ?: playerState.createPlayer(ctx),
                        alphaVideoViewType = config.videoViewType
                    )

                    // 设置监听器
                    playerAction?.let { setPlayerAction(it) }
                    monitor?.let { setMonitor(it) }

                    // 绑定到状态管理
                    playerState.bindToView(this)

                    // 应用配置
                    playerState.setLooping(config.looping)
                    playerState.setScaleType(config.scaleType)
                }
            },
            modifier = modifier.fillMaxSize(),
            update = { view ->
                // 当状态变化时更新视图
                playerState.updateView(view)
            }
        )
    }

    // 生命周期管理
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_START -> {
                    playerState.onStart()
                }
                Lifecycle.Event.ON_STOP -> {
                    playerState.onStop()
                }
                Lifecycle.Event.ON_DESTROY -> {
                    playerState.onDestroy()
                }
                else -> {}
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
            playerState.onDestroy()
        }
    }
}

/**
 * 简化的 AlphaPlayer 组件
 *
 * @param videoPath 视频路径（URL 或本地文件路径）
 * @param modifier 修饰符
 * @param config 播放器配置
 * @param onPlayerReady 播放器准备完成回调
 * @param onPlaybackComplete 播放完成回调
 * @param onError 错误回调
 */
@Composable
fun SimpleAlphaPlayer(
    videoPath: String?,
    modifier: Modifier = Modifier,
    playerState: AlphaPlayerState = rememberAlphaPlayerState(),
    config: PlayerConfig = PlayerConfig(),
    onPlayerReady: (() -> Unit)? = null,
    onPlaybackComplete: (() -> Unit)? = null,
    onError: ((String) -> Unit)? = null
) {
    // 设置视频源
    LaunchedEffect(videoPath) {
        if (!videoPath.isNullOrEmpty()) {
            playerState.setVideoSource(videoPath)
        }
    }

    // 设置配置
    LaunchedEffect(config) {
        playerState.setLooping(config.looping)
        playerState.setScaleType(config.scaleType)
    }

    // 监听状态变化
    LaunchedEffect(playerState.isReadyCompose) {
        if (playerState.isReadyCompose) {
            onPlayerReady?.invoke()
        }
    }

    LaunchedEffect(playerState.isCompletedCompose) {
        if (playerState.isCompletedCompose) {
            onPlaybackComplete?.invoke()
        }
    }

    LaunchedEffect(playerState.errorMessageCompose) {
        playerState.errorMessageCompose?.let { error ->
            onError?.invoke(error)
        }
    }

    AlphaPlayer(
        playerState = playerState,
        modifier = modifier,
        config = config
    )
}
