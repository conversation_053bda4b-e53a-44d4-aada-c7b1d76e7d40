package com.flutterup.players.compose

import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Assert.*
import com.ss.ugc.android.alpha_player.model.ScaleType

/**
 * AlphaPlayerState 单元测试
 */
class AlphaPlayerStateTest {

    @Test
    fun `test initial state`() {
        val playerState = AlphaPlayerState()
        
        // 验证初始状态
        assertFalse(playerState.isPlayingCompose)
        assertFalse(playerState.isReadyCompose)
        assertFalse(playerState.isCompletedCompose)
        assertNull(playerState.errorMessageCompose)
    }

    @Test
    fun `test setVideoSource updates state`() = runTest {
        val playerState = AlphaPlayerState()
        val testPath = "https://example.com/test.mp4"
        
        playerState.setVideoSource(testPath)
        
        // 验证视频源设置
        assertEquals(testPath, playerState.videoSource.first())
        assertFalse(playerState.isCompletedCompose)
        assertNull(playerState.errorMessageCompose)
    }

    @Test
    fun `test configuration setters`() = runTest {
        val playerState = AlphaPlayerState()
        
        // 测试自动播放设置
        playerState.setAutoPlay(false)
        assertFalse(playerState.autoPlay.first())
        
        // 测试循环播放设置
        playerState.setLooping(true)
        assertTrue(playerState.looping.first())
        
        // 测试缩放类型设置
        playerState.setScaleType(ScaleType.ScaleAspectFill)
        assertEquals(ScaleType.ScaleAspectFill, playerState.scaleType.first())
    }

    @Test
    fun `test stop sets completed state`() {
        val playerState = AlphaPlayerState()
        
        playerState.stop()
        
        assertFalse(playerState.isPlayingCompose)
        assertTrue(playerState.isCompletedCompose)
    }

    @Test
    fun `test replay resets completed state`() {
        val playerState = AlphaPlayerState()
        
        // 先设置为完成状态
        playerState.stop()
        assertTrue(playerState.isCompletedCompose)
        
        // 重播应该重置完成状态
        playerState.replay()
        assertFalse(playerState.isCompletedCompose)
    }

    @Test
    fun `test setError updates error state`() = runTest {
        val playerState = AlphaPlayerState()
        val errorMessage = "Test error"
        
        playerState.setError(errorMessage)
        
        assertEquals(errorMessage, playerState.errorMessage.first())
        assertEquals(errorMessage, playerState.errorMessageCompose)
        assertFalse(playerState.isPlayingCompose)
    }

    @Test
    fun `test setReady updates ready state`() = runTest {
        val playerState = AlphaPlayerState()
        
        playerState.setReady(true)
        
        assertTrue(playerState.isReady.first())
        assertTrue(playerState.isReadyCompose)
    }

    @Test
    fun `test setCompleted updates completed state`() = runTest {
        val playerState = AlphaPlayerState()
        
        playerState.setCompleted(true)
        
        assertTrue(playerState.isCompleted.first())
        assertTrue(playerState.isCompletedCompose)
        assertFalse(playerState.isPlayingCompose)
    }

    @Test
    fun `test onDestroy resets all states`() = runTest {
        val playerState = AlphaPlayerState()
        
        // 设置一些状态
        playerState.setVideoSource("test.mp4")
        playerState.setReady(true)
        playerState.setError("test error")
        
        // 销毁
        playerState.onDestroy()
        
        // 验证状态重置
        assertFalse(playerState.isPlaying.first())
        assertFalse(playerState.isReady.first())
        assertFalse(playerState.isCompleted.first())
        assertNull(playerState.errorMessage.first())
        
        assertFalse(playerState.isPlayingCompose)
        assertFalse(playerState.isReadyCompose)
        assertFalse(playerState.isCompletedCompose)
        assertNull(playerState.errorMessageCompose)
    }
}
