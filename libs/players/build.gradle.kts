plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
}

apply(from = "${rootDir}/gradles/buildBase.gradle")
apply(from = "${rootDir}/gradles/buildHilt.gradle")
apply(from = "${rootDir}/gradles/buildCompose.gradle")


android {
    namespace = "com.flutterup.players"

    buildFeatures {
        viewBinding = true
    }
}

dependencies {
    implementation(project(":libs:base"))
    implementation(project(":libs:network"))

    //alpha_player
    api(project(":libs:alpha_player"))

    api(libs.media3.exoplayer)
    api(libs.media3.exoplayer.dash)
    api(libs.media3.extractor)
    api(libs.media3.ui)
    api(libs.media3.ui.compose)
}