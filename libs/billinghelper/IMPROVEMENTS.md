# BillingHelper 改进说明

## 🚀 最新改进

### 1. 产品类型判断交给外部处理

**改进前**：
- `determineProductType` 方法在 `BillingHelper` 内部实现
- 使用简单的字符串匹配规则
- 外部无法自定义产品类型判断逻辑

**改进后**：
- 将 `determineProductType` 方法移到 `PurchaseHandler` 接口中
- 外部模块可以根据自己的业务逻辑实现产品类型判断
- 更加灵活和可扩展

```kotlin
interface PurchaseHandler {
    /**
     * 确定产品类型
     * @param purchase Google Play 购买对象
     * @return 产品类型：BillingClient.ProductType.INAPP 或 BillingClient.ProductType.SUBS
     */
    fun determineProductType(purchase: Purchase): String
    
    // ... 其他方法
}
```

**实现示例**：
```kotlin
override fun determineProductType(purchase: Purchase): String {
    val productId = purchase.products.firstOrNull() ?: ""
    return when {
        // 订阅类型
        productId.contains("sub") || productId.contains("subscription") -> BillingUtils.ProductType.SUBS
        productId.contains("monthly") || productId.contains("yearly") -> BillingUtils.ProductType.SUBS
        productId.contains("premium") || productId.contains("vip") -> BillingUtils.ProductType.SUBS
        
        // 内购类型
        productId.contains("diamond") || productId.contains("coin") -> BillingUtils.ProductType.INAPP
        productId.contains("pack") || productId.contains("bundle") -> BillingUtils.ProductType.INAPP
        
        // 默认为内购
        else -> BillingUtils.ProductType.INAPP
    }
}
```

### 2. 订阅和内购设置为确认和消耗后再调用验证

**改进前**：
- 购买成功后直接保存到数据库并验证
- 没有区分订阅和内购的处理方式
- 可能导致 Google Play 的购买状态不一致

**改进后**：
- **订阅产品**：先确认（acknowledge），确认成功后再验证
- **内购产品**：先消耗（consume），消耗成功后再验证
- 确保 Google Play 的购买状态正确处理

```kotlin
// 根据产品类型进行不同处理
when (productType) {
    BillingUtils.ProductType.SUBS -> {
        // 订阅：确认后验证
        if (!purchase.isAcknowledged) {
            acknowledgePurchase(purchase.purchaseToken) {
                // 确认成功后验证
                scope.launch {
                    verifyPurchaseWithHandler(purchaseEntity)
                }
            }
        } else {
            // 已确认，直接验证
            verifyPurchaseWithHandler(purchaseEntity)
        }
    }
    BillingUtils.ProductType.INAPP -> {
        // 内购：消耗后验证
        consumePurchase(purchase.purchaseToken) {
            // 消耗成功后验证
            scope.launch {
                verifyPurchaseWithHandler(purchaseEntity)
            }
        }
    }
}
```

## 🔧 新增方法

### 1. consumePurchase 方法

```kotlin
/**
 * 消耗购买
 */
private fun consumePurchase(purchaseToken: String, onSuccess: (() -> Unit)? = null) {
    try {
        val consumeParams = ConsumeParams.newBuilder()
            .setPurchaseToken(purchaseToken)
            .build()

        billingClient?.consumeAsync(consumeParams) { billingResult, _ ->
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                log("Purchase consumed: $purchaseToken")
                onSuccess?.invoke()
            } else {
                log("Failed to consume purchase: ${billingResult.debugMessage}")
            }
        }
    } catch (e: Exception) {
        log("Exception consuming purchase: ${e.message}")
    }
}
```

### 2. 改进的 acknowledgePurchase 方法

```kotlin
/**
 * 确认购买
 */
private fun acknowledgePurchase(purchaseToken: String, onSuccess: (() -> Unit)? = null) {
    // ... 添加了成功回调参数
}
```

## 📋 处理流程

### 新的购买处理流程

```
购买成功 → 保存到数据库 → 判断产品类型 → 确认/消耗 → 验证购买 → 删除本地记录
```

**详细步骤**：

1. **购买成功**：Google Play 返回购买成功
2. **保存到数据库**：立即保存购买记录到本地数据库
3. **判断产品类型**：调用 `PurchaseHandler.determineProductType()`
4. **确认或消耗**：
   - 订阅：调用 `acknowledgePurchase()`
   - 内购：调用 `consumePurchase()`
5. **验证购买**：确认/消耗成功后调用 `verifyPurchase()`
6. **删除记录**：验证成功后从本地数据库删除记录

## ✅ 优势

### 1. 更好的扩展性
- 外部可以自定义产品类型判断逻辑
- 支持复杂的业务规则

### 2. 更规范的 Google Play 集成
- 正确处理订阅的确认流程
- 正确处理内购的消耗流程
- 避免 Google Play 的警告和问题

### 3. 更可靠的验证时机
- 确保 Google Play 状态正确后再验证
- 减少验证失败的可能性

## 🔄 迁移指南

如果你已经在使用旧版本的 BillingHelper，需要：

1. **更新 PurchaseHandler 实现**：
   ```kotlin
   // 添加新方法
   override fun determineProductType(purchase: Purchase): String {
       // 实现你的产品类型判断逻辑
   }
   ```

2. **无需修改其他代码**：
   - BillingHelper 的使用方式保持不变
   - 购买流程的调用方式保持不变

## 🎯 最佳实践

1. **产品类型判断**：
   - 使用清晰的产品ID命名规则
   - 在 `determineProductType` 中添加详细的日志
   - 考虑使用配置文件或远程配置

2. **错误处理**：
   - 在确认/消耗失败时添加重试逻辑
   - 记录详细的错误日志用于调试

3. **测试**：
   - 分别测试订阅和内购的完整流程
   - 测试网络异常情况下的处理

这些改进使 BillingHelper 更加健壮和符合 Google Play 的最佳实践！
