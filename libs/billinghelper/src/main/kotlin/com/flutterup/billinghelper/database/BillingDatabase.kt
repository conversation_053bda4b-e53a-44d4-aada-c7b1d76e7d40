package com.flutterup.billinghelper.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.flutterup.billinghelper.dao.PurchaseDao
import com.flutterup.billinghelper.model.PurchaseEntity

/**
 * Billing 数据库
 */
@Database(
    entities = [PurchaseEntity::class],
    version = 1,
    exportSchema = false
)
abstract class BillingDatabase : RoomDatabase() {
    
    abstract fun purchaseDao(): PurchaseDao
    
    companion object {
        @Volatile
        private var INSTANCE: BillingDatabase? = null
        
        fun getInstance(context: Context): BillingDatabase {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: Room.databaseBuilder(
                    context.applicationContext,
                    BillingDatabase::class.java,
                    "billing_database.db"
                ).build().also { INSTANCE = it }
            }
        }
    }
}
