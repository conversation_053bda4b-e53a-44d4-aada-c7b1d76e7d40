package com.flutterup.billinghelper.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 购买记录实体类
 * 用于存储未验证的购买记录
 */
@Entity(tableName = "purchase_records")
data class PurchaseEntity(
    @PrimaryKey
    val purchaseToken: String,
    
    // 产品ID
    val productId: String,
    
    // 产品类型：INAPP（内购）或 SUBS（订阅）
    val productType: String,
    
    // 订单ID
    val orderId: String?,
    
    // 购买时间戳
    val purchaseTime: Long,
    
    // 购买状态：1-已购买，2-待处理
    val purchaseState: Int,
    
    // 用户ID（obfuscatedAccountId）
    val userId: String,
    
    // 后端订单号（obfuscatedProfileId）
    val backendOrderId: String,
    
    // 原始购买数据（JSON格式）
    val originalJson: String,
    
    // 签名
    val signature: String,
    
    // 重试次数
    val retryCount: Int = 0,
    
    // 创建时间
    val createdAt: Long = System.currentTimeMillis(),
    
    // 最后更新时间
    val updatedAt: Long = System.currentTimeMillis()
)
