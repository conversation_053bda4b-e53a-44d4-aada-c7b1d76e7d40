package com.flutterup.billinghelper.model

import com.android.billingclient.api.Purchase

/**
 * Billing 操作结果
 */
sealed class BillingResult<out T> {
    /**
     * 成功
     */
    data class Success<T>(val data: T) : BillingResult<T>()
    
    /**
     * 失败
     */
    data class Error(
        val responseCode: Int,
        val debugMessage: String? = null,
        val exception: Throwable? = null
    ) : BillingResult<Nothing>()
    
    /**
     * 用户取消
     */
    object UserCanceled : BillingResult<Nothing>()
    
    /**
     * 服务不可用
     */
    object ServiceUnavailable : BillingResult<Nothing>()
    
    /**
     * 网络错误
     */
    object NetworkError : BillingResult<Nothing>()
}

/**
 * 购买结果
 */
data class PurchaseResult(
    val purchases: List<Purchase>,
    val responseCode: Int,
    val debugMessage: String? = null
)

/**
 * 产品详情结果
 */
data class ProductDetailsResult(
    val productDetails: List<com.android.billingclient.api.ProductDetails>,
    val responseCode: Int,
    val debugMessage: String? = null
)

/**
 * 连接状态
 */
enum class BillingConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    CLOSED
}

/**
 * 重试配置
 */
data class RetryConfig(
    val maxRetryCount: Int = 3,
    val retryDelayMs: Long = 1000L,
    val backoffMultiplier: Float = 2.0f
)

/**
 * Billing 配置
 */
data class BillingConfig(
    val enableLogging: Boolean = true,
    val retryConfig: RetryConfig = RetryConfig(),
    val autoReconnect: Boolean = true,
    val connectionTimeoutMs: Long = 30000L
)
