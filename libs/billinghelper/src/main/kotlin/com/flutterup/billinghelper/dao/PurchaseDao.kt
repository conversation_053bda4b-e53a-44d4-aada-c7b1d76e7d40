package com.flutterup.billinghelper.dao

import androidx.room.*
import com.flutterup.billinghelper.model.PurchaseEntity
import kotlinx.coroutines.flow.Flow

/**
 * 购买记录数据访问对象
 */
@Dao
interface PurchaseDao {
    
    /**
     * 插入购买记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPurchase(purchase: PurchaseEntity)
    
    /**
     * 批量插入购买记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPurchases(purchases: List<PurchaseEntity>)
    
    /**
     * 删除购买记录
     */
    @Delete
    suspend fun deletePurchase(purchase: PurchaseEntity)
    
    /**
     * 根据购买令牌删除记录
     */
    @Query("DELETE FROM purchase_records WHERE purchaseToken = :purchaseToken")
    suspend fun deletePurchaseByToken(purchaseToken: String)
    
    /**
     * 根据购买令牌查询记录
     */
    @Query("SELECT * FROM purchase_records WHERE purchaseToken = :purchaseToken")
    suspend fun getPurchaseByToken(purchaseToken: String): PurchaseEntity?
    
    /**
     * 获取所有未验证的购买记录
     */
    @Query("SELECT * FROM purchase_records ORDER BY createdAt ASC")
    suspend fun getAllPurchases(): List<PurchaseEntity>
    
    /**
     * 获取所有未验证的购买记录（Flow）
     */
    @Query("SELECT * FROM purchase_records ORDER BY createdAt ASC")
    fun getAllPurchasesFlow(): Flow<List<PurchaseEntity>>
    
    /**
     * 根据用户ID获取购买记录
     */
    @Query("SELECT * FROM purchase_records WHERE userId = :userId ORDER BY createdAt ASC")
    suspend fun getPurchasesByUserId(userId: String): List<PurchaseEntity>
    
    /**
     * 根据产品类型获取购买记录
     */
    @Query("SELECT * FROM purchase_records WHERE productType = :productType ORDER BY createdAt ASC")
    suspend fun getPurchasesByType(productType: String): List<PurchaseEntity>
    
    /**
     * 更新重试次数
     */
    @Query("UPDATE purchase_records SET retryCount = :retryCount, updatedAt = :updatedAt WHERE purchaseToken = :purchaseToken")
    suspend fun updateRetryCount(purchaseToken: String, retryCount: Int, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * 获取重试次数小于指定值的记录
     */
    @Query("SELECT * FROM purchase_records WHERE retryCount < :maxRetryCount ORDER BY createdAt ASC")
    suspend fun getPurchasesForRetry(maxRetryCount: Int): List<PurchaseEntity>
    
    /**
     * 清空所有记录
     */
    @Query("DELETE FROM purchase_records")
    suspend fun clearAll()
    
    /**
     * 获取记录总数
     */
    @Query("SELECT COUNT(*) FROM purchase_records")
    suspend fun getCount(): Int
}
