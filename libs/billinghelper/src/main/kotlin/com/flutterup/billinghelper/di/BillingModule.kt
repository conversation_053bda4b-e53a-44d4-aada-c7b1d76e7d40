package com.flutterup.billinghelper.di

import android.content.Context
import com.flutterup.billinghelper.BillingHelper
import com.flutterup.billinghelper.dao.PurchaseDao
import com.flutterup.billinghelper.database.BillingDatabase
import com.flutterup.billinghelper.handler.PurchaseHandler
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Billing 依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object BillingModule {
    
    /**
     * 提供 BillingDatabase
     */
    @Provides
    @Singleton
    fun provideBillingDatabase(@ApplicationContext context: Context): BillingDatabase {
        return BillingDatabase.getInstance(context)
    }
    
    /**
     * 提供 PurchaseDao
     */
    @Provides
    fun providePurchaseDao(database: BillingDatabase): PurchaseDao {
        return database.purchaseDao()
    }
    
    /**
     * 提供 BillingHelper
     * 注意：PurchaseHandler 是可选的，由外部模块提供
     */
    @Provides
    @Singleton
    fun provideBillingHelper(
        @ApplicationContext context: Context,
        purchaseDao: PurchaseDao,
        purchaseHandler: PurchaseHandler?
    ): BillingHelper {
        return BillingHelper(context, purchaseDao, purchaseHandler)
    }
}
