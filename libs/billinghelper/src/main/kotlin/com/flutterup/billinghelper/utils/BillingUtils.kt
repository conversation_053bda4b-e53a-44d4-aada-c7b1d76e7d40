package com.flutterup.billinghelper.utils

import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchaseHistoryRecord
import com.flutterup.billinghelper.entity.PurchaseEntity

/**
 * Billing 工具类
 */
object BillingUtils {
    
    /**
     * 产品类型常量
     */
    object ProductType {
        const val INAPP = BillingClient.ProductType.INAPP
        const val SUBS = BillingClient.ProductType.SUBS
    }
    
    /**
     * 购买状态常量
     */
    object PurchaseState {
        const val UNSPECIFIED_STATE = Purchase.PurchaseState.UNSPECIFIED_STATE
        const val PURCHASED = Purchase.PurchaseState.PURCHASED
        const val PENDING = Purchase.PurchaseState.PENDING
    }
    
    /**
     * 响应码常量
     */
    object ResponseCode {
        const val OK = BillingClient.BillingResponseCode.OK
        const val USER_CANCELED = BillingClient.BillingResponseCode.USER_CANCELED
        const val SERVICE_UNAVAILABLE = BillingClient.BillingResponseCode.SERVICE_UNAVAILABLE
        const val BILLING_UNAVAILABLE = BillingClient.BillingResponseCode.BILLING_UNAVAILABLE
        const val ITEM_UNAVAILABLE = BillingClient.BillingResponseCode.ITEM_UNAVAILABLE
        const val DEVELOPER_ERROR = BillingClient.BillingResponseCode.DEVELOPER_ERROR
        const val ERROR = BillingClient.BillingResponseCode.ERROR
        const val ITEM_ALREADY_OWNED = BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED
        const val ITEM_NOT_OWNED = BillingClient.BillingResponseCode.ITEM_NOT_OWNED
        const val SERVICE_DISCONNECTED = BillingClient.BillingResponseCode.SERVICE_DISCONNECTED
        const val FEATURE_NOT_SUPPORTED = BillingClient.BillingResponseCode.FEATURE_NOT_SUPPORTED
        const val SERVICE_TIMEOUT = BillingClient.BillingResponseCode.SERVICE_TIMEOUT
        const val NETWORK_ERROR = BillingClient.BillingResponseCode.NETWORK_ERROR
    }
    
    /**
     * 检查响应码是否成功
     */
    fun isResponseCodeSuccess(responseCode: Int): Boolean {
        return responseCode == ResponseCode.OK
    }
    
    /**
     * 检查购买状态是否为已购买
     */
    fun isPurchased(purchaseState: Int): Boolean {
        return purchaseState == PurchaseState.PURCHASED
    }
    
    /**
     * 检查购买状态是否为待处理
     */
    fun isPending(purchaseState: Int): Boolean {
        return purchaseState == PurchaseState.PENDING
    }
    
    /**
     * 获取响应码描述
     */
    fun getResponseCodeDescription(responseCode: Int): String {
        return when (responseCode) {
            ResponseCode.OK -> "Success"
            ResponseCode.USER_CANCELED -> "User canceled"
            ResponseCode.SERVICE_UNAVAILABLE -> "Service unavailable"
            ResponseCode.BILLING_UNAVAILABLE -> "Billing unavailable"
            ResponseCode.ITEM_UNAVAILABLE -> "Item unavailable"
            ResponseCode.DEVELOPER_ERROR -> "Developer error"
            ResponseCode.ERROR -> "Error"
            ResponseCode.ITEM_ALREADY_OWNED -> "Item already owned"
            ResponseCode.ITEM_NOT_OWNED -> "Item not owned"
            ResponseCode.SERVICE_DISCONNECTED -> "Service disconnected"
            ResponseCode.FEATURE_NOT_SUPPORTED -> "Feature not supported"
            ResponseCode.SERVICE_TIMEOUT -> "Service timeout"
            ResponseCode.NETWORK_ERROR -> "Network error"
            else -> "Unknown error code: $responseCode"
        }
    }
}

/**
 * Purchase 扩展函数
 */
fun Purchase.toPurchaseEntity(
    userId: String,
    backendOrderId: String,
    productType: String
): PurchaseEntity {
    return PurchaseEntity(
        purchaseToken = purchaseToken,
        productId = products.firstOrNull() ?: "",
        productType = productType,
        orderId = orderId,
        purchaseTime = purchaseTime,
        purchaseState = purchaseState,
        userId = userId,
        backendOrderId = backendOrderId,
        originalJson = originalJson,
        signature = signature,
        retryCount = 0,
        createdAt = System.currentTimeMillis(),
        updatedAt = System.currentTimeMillis()
    )
}

/**
 * PurchaseHistoryRecord 扩展函数
 */
fun PurchaseHistoryRecord.toPurchaseEntity(
    userId: String,
    backendOrderId: String,
    productType: String
): PurchaseEntity {
    return PurchaseEntity(
        purchaseToken = purchaseToken,
        productId = products.firstOrNull() ?: "",
        productType = productType,
        orderId = null, // PurchaseHistoryRecord 没有 orderId
        purchaseTime = purchaseTime,
        purchaseState = BillingUtils.PurchaseState.PURCHASED, // 历史记录默认为已购买
        userId = userId,
        backendOrderId = backendOrderId,
        originalJson = originalJson,
        signature = signature,
        retryCount = 0,
        createdAt = System.currentTimeMillis(),
        updatedAt = System.currentTimeMillis()
    )
}
