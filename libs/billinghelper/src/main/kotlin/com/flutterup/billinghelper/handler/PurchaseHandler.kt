package com.flutterup.billinghelper.handler

import com.flutterup.billinghelper.model.PurchaseEntity

/**
 * 购买处理器接口
 * 由外部模块实现，用于处理后端验证逻辑
 */
interface PurchaseHandler {
    
    /**
     * 验证购买
     * @param purchase 购买记录
     * @return 验证结果，true表示验证成功，false表示验证失败
     */
    suspend fun verifyPurchase(purchase: PurchaseEntity): Boolean
    
    /**
     * 处理验证失败的情况
     * @param purchase 购买记录
     * @param error 错误信息
     */
    suspend fun onVerificationFailed(purchase: PurchaseEntity, error: Throwable?)
    
    /**
     * 处理验证成功的情况
     * @param purchase 购买记录
     */
    suspend fun onVerificationSuccess(purchase: PurchaseEntity)
}

/**
 * 购买验证结果
 */
sealed class PurchaseVerificationResult {
    /**
     * 验证成功
     */
    object Success : PurchaseVerificationResult()
    
    /**
     * 验证失败，可以重试
     */
    data class RetryableFailure(val error: Throwable) : PurchaseVerificationResult()
    
    /**
     * 验证失败，不可重试
     */
    data class NonRetryableFailure(val error: Throwable) : PurchaseVerificationResult()
}

/**
 * 增强版购买处理器接口
 * 提供更详细的验证结果
 */
interface EnhancedPurchaseHandler {
    
    /**
     * 验证购买（增强版）
     * @param purchase 购买记录
     * @return 详细的验证结果
     */
    suspend fun verifyPurchaseEnhanced(purchase: PurchaseEntity): PurchaseVerificationResult
}
