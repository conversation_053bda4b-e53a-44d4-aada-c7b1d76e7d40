package com.flutterup.billinghelper

import android.app.Activity
import android.content.Context
import com.android.billingclient.api.AcknowledgePurchaseParams
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.PendingPurchasesParams
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.QueryPurchasesParams
import com.flutterup.base.utils.Timber
import com.flutterup.billinghelper.dao.PurchaseDao
import com.flutterup.billinghelper.model.PurchaseEntity
import com.flutterup.billinghelper.handler.PurchaseHandler
import com.flutterup.billinghelper.model.BillingConfig
import com.flutterup.billinghelper.model.BillingConnectionState
import com.flutterup.billinghelper.model.BillingResult
import com.flutterup.billinghelper.model.ProductDetailsResult
import com.flutterup.billinghelper.model.PurchaseResult
import com.flutterup.billinghelper.utils.BillingUtils
import com.flutterup.billinghelper.utils.toPurchaseEntity
import com.android.billingclient.api.BillingResult as GoogleBillingResult
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import kotlin.coroutines.resume
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Billing Helper 核心类
 * 支持订阅和内购，包含购买流程、本地存储、重试机制
 */
@Singleton
class BillingHelper @Inject constructor(
    private val context: Context,
    private val purchaseDao: PurchaseDao,
    private val purchaseHandler: PurchaseHandler?
) : PurchasesUpdatedListener, BillingClientStateListener {

    private var billingClient: BillingClient? = null
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private val _connectionState = MutableStateFlow(BillingConnectionState.DISCONNECTED)
    val connectionState: StateFlow<BillingConnectionState> = _connectionState.asStateFlow()
    
    private val _purchaseFlow = MutableSharedFlow<BillingResult<PurchaseResult>>()
    val purchaseFlow: SharedFlow<BillingResult<PurchaseResult>> = _purchaseFlow.asSharedFlow()
    
    private var config = BillingConfig()
    
    init {
        initializeBillingClient()
    }
    
    /**
     * 初始化 Billing Client
     */
    private fun initializeBillingClient() {
        billingClient = BillingClient.newBuilder(context)
            .setListener(this)
            .enablePendingPurchases(
                PendingPurchasesParams.newBuilder()
                    .enableOneTimeProducts()
                    .build()
            )
            .build()
    }
    
    /**
     * 连接到 Google Play Billing 服务
     */
    fun connect() {
        if (_connectionState.value == BillingConnectionState.CONNECTED) {
            return
        }
        
        _connectionState.value = BillingConnectionState.CONNECTING
        billingClient?.startConnection(this)
    }
    
    /**
     * 断开连接
     */
    fun disconnect() {
        billingClient?.endConnection()
        _connectionState.value = BillingConnectionState.DISCONNECTED
    }
    
    /**
     * 设置配置
     */
    fun setConfig(config: BillingConfig) {
        this.config = config
    }
    
    override fun onBillingSetupFinished(billingResult: GoogleBillingResult) {
        if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
            _connectionState.value = BillingConnectionState.CONNECTED
            log("Billing client connected successfully")
            
            // 连接成功后，查询现有购买记录
            scope.launch {
                queryExistingPurchases()
            }
        } else {
            _connectionState.value = BillingConnectionState.DISCONNECTED
            log("Billing client connection failed: ${billingResult.debugMessage}")
            
            // 如果启用自动重连，则尝试重连
            if (config.autoReconnect) {
                scope.launch {
                    delay(config.retryConfig.retryDelayMs)
                    connect()
                }
            }
        }
    }
    
    override fun onBillingServiceDisconnected() {
        _connectionState.value = BillingConnectionState.DISCONNECTED
        log("Billing service disconnected")
        
        if (config.autoReconnect) {
            scope.launch {
                delay(config.retryConfig.retryDelayMs)
                connect()
            }
        }
    }
    
    override fun onPurchasesUpdated(billingResult: GoogleBillingResult, purchases: MutableList<Purchase>?) {
        when (billingResult.responseCode) {
            BillingClient.BillingResponseCode.OK -> {
                purchases?.let { purchaseList ->
                    scope.launch {
                        handlePurchases(purchaseList)
                    }
                    _purchaseFlow.tryEmit(BillingResult.Success(PurchaseResult(purchaseList, billingResult.responseCode, billingResult.debugMessage)))
                }
            }
            BillingClient.BillingResponseCode.USER_CANCELED -> {
                _purchaseFlow.tryEmit(BillingResult.UserCanceled)
            }
            BillingClient.BillingResponseCode.SERVICE_UNAVAILABLE -> {
                _purchaseFlow.tryEmit(BillingResult.ServiceUnavailable)
            }
            BillingClient.BillingResponseCode.NETWORK_ERROR -> {
                _purchaseFlow.tryEmit(BillingResult.NetworkError)
            }
            else -> {
                _purchaseFlow.tryEmit(BillingResult.Error(billingResult.responseCode, billingResult.debugMessage))
            }
        }
    }
    
    /**
     * 启动购买流程
     */
    suspend fun launchBillingFlow(
        activity: Activity,
        productDetails: ProductDetails,
        userId: String,
        backendOrderId: String
    ): BillingResult<Unit> {
        return withContext(Dispatchers.Main) {
            try {
                if (_connectionState.value != BillingConnectionState.CONNECTED) {
                    return@withContext BillingResult.Error(
                        BillingClient.BillingResponseCode.SERVICE_DISCONNECTED,
                        "Billing client not connected"
                    )
                }
                
                val productDetailsParamsList = listOf(
                    BillingFlowParams.ProductDetailsParams.newBuilder()
                        .setProductDetails(productDetails)
                        .build()
                )
                
                val billingFlowParams = BillingFlowParams.newBuilder()
                    .setProductDetailsParamsList(productDetailsParamsList)
                    .setObfuscatedAccountId(userId)
                    .setObfuscatedProfileId(backendOrderId)
                    .build()
                
                val billingResult = billingClient?.launchBillingFlow(activity, billingFlowParams)
                
                if (billingResult?.responseCode == BillingClient.BillingResponseCode.OK) {
                    BillingResult.Success(Unit)
                } else {
                    BillingResult.Error(
                        billingResult?.responseCode ?: BillingClient.BillingResponseCode.ERROR,
                        billingResult?.debugMessage
                    )
                }
            } catch (e: Exception) {
                BillingResult.Error(BillingClient.BillingResponseCode.ERROR, e.message, e)
            }
        }
    }
    
    /**
     * 查询产品详情
     */
    suspend fun queryProductDetails(
        productIds: List<String>,
        productType: String
    ): BillingResult<ProductDetailsResult> {
        return withContext(Dispatchers.IO) {
            try {
                if (_connectionState.value != BillingConnectionState.CONNECTED) {
                    return@withContext BillingResult.Error(
                        BillingClient.BillingResponseCode.SERVICE_DISCONNECTED,
                        "Billing client not connected"
                    )
                }
                
                val productList = productIds.map { productId ->
                    QueryProductDetailsParams.Product.newBuilder()
                        .setProductId(productId)
                        .setProductType(productType)
                        .build()
                }
                
                val params = QueryProductDetailsParams.newBuilder()
                    .setProductList(productList)
                    .build()
                
                return@withContext suspendCancellableCoroutine<BillingResult<ProductDetailsResult>> { continuation ->
                    billingClient?.queryProductDetailsAsync(params) { billingResult, productDetailsList ->
                        if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                            continuation.resume(
                                BillingResult.Success(
                                    ProductDetailsResult(
                                        productDetailsList ?: emptyList(),
                                        billingResult.responseCode,
                                        billingResult.debugMessage
                                    )
                                )
                            )
                        } else {
                            continuation.resume(
                                BillingResult.Error(
                                    billingResult.responseCode,
                                    billingResult.debugMessage
                                )
                            )
                        }
                    } ?: continuation.resume(
                        BillingResult.Error(
                            BillingClient.BillingResponseCode.SERVICE_DISCONNECTED,
                            "Billing client is null"
                        )
                    )
                }
            } catch (e: Exception) {
                BillingResult.Error(BillingClient.BillingResponseCode.ERROR, e.message, e)
            }
        }
    }
    
    /**
     * 处理购买记录
     */
    private suspend fun handlePurchases(purchases: List<Purchase>) {
        purchases.forEach { purchase ->
            if (BillingUtils.isPurchased(purchase.purchaseState)) {
                // 确认购买（如果需要）
                if (!purchase.isAcknowledged) {
                    acknowledgePurchase(purchase.purchaseToken)
                }

                // 保存到本地数据库
                savePurchaseToDatabase(purchase)
            }
        }
    }

    /**
     * 保存购买记录到数据库
     */
    private suspend fun savePurchaseToDatabase(purchase: Purchase) {
        try {
            // 从 obfuscatedAccountId 和 obfuscatedProfileId 获取用户信息
            val userId = purchase.accountIdentifiers?.obfuscatedAccountId ?: ""
            val backendOrderId = purchase.accountIdentifiers?.obfuscatedProfileId ?: ""

            // 确定产品类型
            val productType = determineProductType(purchase.products.firstOrNull() ?: "")

            val purchaseEntity = purchase.toPurchaseEntity(userId, backendOrderId, productType)
            purchaseDao.insertPurchase(purchaseEntity)

            log("Purchase saved to database: ${purchase.purchaseToken}")

            // 尝试验证购买
            verifyPurchaseWithHandler(purchaseEntity)

        } catch (e: Exception) {
            log("Failed to save purchase to database: ${e.message}")
        }
    }

    /**
     * 确认购买
     */
    private suspend fun acknowledgePurchase(purchaseToken: String) {
        try {
            val acknowledgeParams = AcknowledgePurchaseParams.newBuilder()
                .setPurchaseToken(purchaseToken)
                .build()

            billingClient?.acknowledgePurchase(acknowledgeParams) { billingResult ->
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    log("Purchase acknowledged: $purchaseToken")
                } else {
                    log("Failed to acknowledge purchase: ${billingResult.debugMessage}")
                }
            }
        } catch (e: Exception) {
            log("Exception acknowledging purchase: ${e.message}")
        }
    }

    /**
     * 查询现有购买记录
     */
    private suspend fun queryExistingPurchases() {
        try {
            // 查询内购
            queryPurchases(BillingUtils.ProductType.INAPP)

            // 查询订阅
            queryPurchases(BillingUtils.ProductType.SUBS)

        } catch (e: Exception) {
            log("Failed to query existing purchases: ${e.message}")
        }
    }

    /**
     * 查询指定类型的购买记录
     */
    private suspend fun queryPurchases(productType: String) {
        try {
            val params = QueryPurchasesParams.newBuilder()
                .setProductType(productType)
                .build()

            val result = billingClient?.queryPurchasesAsync(params) { billingResult, purchases ->
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    scope.launch {
                        handlePurchases(purchases)
                    }
                }
            }
        } catch (e: Exception) {
            log("Failed to query purchases for type $productType: ${e.message}")
        }
    }

    /**
     * 确定产品类型
     * 根据产品ID判断是订阅还是内购
     */
    private fun determineProductType(productId: String): String {
        // 根据产品ID的命名规则来判断类型
        // 这里提供一个简单的实现，实际使用时需要根据业务逻辑调整
        return when {
            productId.contains("sub") || productId.contains("subscription") -> BillingUtils.ProductType.SUBS
            productId.contains("monthly") || productId.contains("yearly") -> BillingUtils.ProductType.SUBS
            else -> BillingUtils.ProductType.INAPP
        }
    }

    /**
     * 使用处理器验证购买
     */
    private suspend fun verifyPurchaseWithHandler(purchase: PurchaseEntity) {
        purchaseHandler?.let { handler ->
            try {
                val isVerified = handler.verifyPurchase(purchase)
                if (isVerified) {
                    // 验证成功，从数据库删除记录
                    purchaseDao.deletePurchaseByToken(purchase.purchaseToken)
                    handler.onVerificationSuccess(purchase)
                    log("Purchase verified and removed from database: ${purchase.purchaseToken}")
                } else {
                    // 验证失败，更新重试次数
                    updateRetryCount(purchase)
                    handler.onVerificationFailed(purchase, null)
                    log("Purchase verification failed: ${purchase.purchaseToken}")
                }
            } catch (e: Exception) {
                // 验证异常，更新重试次数
                updateRetryCount(purchase)
                handler.onVerificationFailed(purchase, e)
                log("Purchase verification exception: ${e.message}")
            }
        }
    }

    /**
     * 更新重试次数
     */
    private suspend fun updateRetryCount(purchase: PurchaseEntity) {
        val newRetryCount = purchase.retryCount + 1
        if (newRetryCount >= config.retryConfig.maxRetryCount) {
            // 达到最大重试次数，删除记录
            purchaseDao.deletePurchaseByToken(purchase.purchaseToken)
            log("Purchase removed after max retries: ${purchase.purchaseToken}")
        } else {
            // 更新重试次数
            purchaseDao.updateRetryCount(purchase.purchaseToken, newRetryCount)
            log("Purchase retry count updated: ${purchase.purchaseToken}, count: $newRetryCount")
        }
    }

    /**
     * 重试验证所有未验证的购买记录
     */
    suspend fun retryPendingPurchases() {
        try {
            val pendingPurchases = purchaseDao.getPurchasesForRetry(config.retryConfig.maxRetryCount)
            log("Retrying ${pendingPurchases.size} pending purchases")

            pendingPurchases.forEach { purchase ->
                delay(config.retryConfig.retryDelayMs)
                verifyPurchaseWithHandler(purchase)
            }
        } catch (e: Exception) {
            log("Failed to retry pending purchases: ${e.message}")
        }
    }

    /**
     * 获取所有未验证的购买记录
     */
    suspend fun getPendingPurchases(): List<PurchaseEntity> {
        return try {
            purchaseDao.getAllPurchases()
        } catch (e: Exception) {
            log("Failed to get pending purchases: ${e.message}")
            emptyList()
        }
    }

    /**
     * 获取未验证购买记录的数量
     */
    suspend fun getPendingPurchaseCount(): Int {
        return try {
            purchaseDao.getCount()
        } catch (e: Exception) {
            log("Failed to get pending purchase count: ${e.message}")
            0
        }
    }

    /**
     * 清空所有未验证的购买记录
     */
    suspend fun clearAllPendingPurchases() {
        try {
            purchaseDao.clearAll()
            log("All pending purchases cleared")
        } catch (e: Exception) {
            log("Failed to clear pending purchases: ${e.message}")
        }
    }

    /**
     * 手动验证指定的购买记录
     */
    suspend fun verifyPurchase(purchaseToken: String): Boolean {
        return try {
            val purchase = purchaseDao.getPurchaseByToken(purchaseToken)
            if (purchase != null) {
                verifyPurchaseWithHandler(purchase)
                true
            } else {
                log("Purchase not found for token: $purchaseToken")
                false
            }
        } catch (e: Exception) {
            log("Failed to verify purchase: ${e.message}")
            false
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        scope.cancel()
        disconnect()
    }

    private fun log(message: String) {
        if (config.enableLogging) {
            Timber.d("BillingHelper", message)
        }
    }
}
