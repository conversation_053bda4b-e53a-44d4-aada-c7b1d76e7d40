# BillingHelper 使用指南

BillingHelper 是一个完整的 Google Play Billing 封装库，支持订阅和内购，包含本地存储和重试机制。

## 功能特性

- ✅ 支持订阅和内购两种模式
- ✅ 支付成功后自动记录到本地数据库
- ✅ 可配置的购买验证处理器
- ✅ 自动重试机制（最多3次）
- ✅ 支持 obfuscatedAccountId 和 obfuscatedProfileId
- ✅ 完整的错误处理和日志记录
- ✅ 基于 Hilt 的依赖注入

## 快速开始

### 1. 依赖注入配置

在你的应用模块中提供 `PurchaseHandler` 实现：

```kotlin
@Module
@InstallIn(SingletonComponent::class)
object AppBillingModule {
    
    @Provides
    @Singleton
    fun providePurchaseHandler(
        apiService: YourApiService
    ): PurchaseHandler {
        return YourPurchaseHandlerImpl(apiService)
    }
}
```

### 2. 实现 PurchaseHandler

```kotlin
@Singleton
class YourPurchaseHandlerImpl @Inject constructor(
    private val apiService: YourApiService
) : PurchaseHandler {
    
    override suspend fun verifyPurchase(purchase: PurchaseEntity): Boolean {
        return try {
            val response = apiService.verifyPurchase(
                purchaseToken = purchase.purchaseToken,
                productId = purchase.productId,
                userId = purchase.userId,
                orderId = purchase.backendOrderId
            )
            response.isSuccess
        } catch (e: Exception) {
            false
        }
    }
    
    override suspend fun onVerificationFailed(purchase: PurchaseEntity, error: Throwable?) {
        // 处理验证失败
        Timber.w("Purchase verification failed: ${purchase.purchaseToken}")
    }
    
    override suspend fun onVerificationSuccess(purchase: PurchaseEntity) {
        // 处理验证成功
        Timber.d("Purchase verification succeeded: ${purchase.purchaseToken}")
    }
}
```

### 3. 在 Activity/Fragment 中使用

```kotlin
@AndroidEntryPoint
class PaymentActivity : AppCompatActivity() {
    
    @Inject
    lateinit var billingHelper: BillingHelper
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 连接到 Billing 服务
        billingHelper.connect()
        
        // 监听购买结果
        lifecycleScope.launch {
            billingHelper.purchaseFlow.collect { result ->
                when (result) {
                    is BillingResult.Success -> {
                        // 购买成功
                        showToast("购买成功")
                    }
                    is BillingResult.UserCanceled -> {
                        // 用户取消
                        showToast("购买已取消")
                    }
                    is BillingResult.Error -> {
                        // 购买失败
                        showToast("购买失败: ${result.debugMessage}")
                    }
                    else -> {
                        // 其他情况
                    }
                }
            }
        }
    }
    
    private fun startPurchase(productId: String, userId: String, backendOrderId: String) {
        lifecycleScope.launch {
            // 1. 查询产品详情
            val productDetailsResult = billingHelper.queryProductDetails(
                listOf(productId),
                BillingUtils.ProductType.INAPP // 或 BillingUtils.ProductType.SUBS
            )
            
            when (productDetailsResult) {
                is BillingResult.Success -> {
                    val productDetails = productDetailsResult.data.productDetails.firstOrNull()
                    if (productDetails != null) {
                        // 2. 启动购买流程
                        billingHelper.launchBillingFlow(
                            activity = this@PaymentActivity,
                            productDetails = productDetails,
                            userId = userId,
                            backendOrderId = backendOrderId
                        )
                    }
                }
                is BillingResult.Error -> {
                    showToast("查询产品失败: ${productDetailsResult.debugMessage}")
                }
                else -> {
                    showToast("查询产品失败")
                }
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        billingHelper.disconnect()
    }
}
```

### 4. 重试未验证的购买

```kotlin
// 手动重试所有未验证的购买
lifecycleScope.launch {
    billingHelper.retryPendingPurchases()
}

// 获取未验证购买的数量
lifecycleScope.launch {
    val count = billingHelper.getPendingPurchaseCount()
    Timber.d("Pending purchases: $count")
}
```

## 配置选项

```kotlin
// 自定义配置
val config = BillingConfig(
    enableLogging = true,
    retryConfig = RetryConfig(
        maxRetryCount = 3,
        retryDelayMs = 1000L,
        backoffMultiplier = 2.0f
    ),
    autoReconnect = true,
    connectionTimeoutMs = 30000L
)

billingHelper.setConfig(config)
```

## 数据库结构

购买记录存储在 `purchase_records` 表中，包含以下字段：

- `purchaseToken`: 购买令牌（主键）
- `productId`: 产品ID
- `productType`: 产品类型（INAPP/SUBS）
- `orderId`: 订单ID
- `purchaseTime`: 购买时间
- `purchaseState`: 购买状态
- `userId`: 用户ID（obfuscatedAccountId）
- `backendOrderId`: 后端订单号（obfuscatedProfileId）
- `originalJson`: 原始购买数据
- `signature`: 签名
- `retryCount`: 重试次数
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

## 注意事项

1. **PurchaseHandler 是必需的**：你必须在应用模块中提供 `PurchaseHandler` 的实现
2. **网络权限**：确保应用有网络权限用于后端验证
3. **错误处理**：建议在 `PurchaseHandler` 中添加完善的错误处理逻辑
4. **测试**：在发布前务必在测试环境中充分测试购买流程
5. **安全性**：购买验证应该在服务端进行，不要在客户端进行敏感验证

## 故障排除

### 常见问题

1. **连接失败**：检查 Google Play 服务是否可用
2. **产品查询失败**：确认产品ID在 Google Play Console 中已配置
3. **购买失败**：检查应用签名和 Google Play Console 配置
4. **验证失败**：检查后端API是否正常工作

### 调试日志

启用日志记录可以帮助调试问题：

```kotlin
val config = BillingConfig(enableLogging = true)
billingHelper.setConfig(config)
```
