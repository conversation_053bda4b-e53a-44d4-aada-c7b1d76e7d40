package com.flutterup.chat.message.content

import android.os.Parcel
import android.os.Parcelable
import io.rong.imlib.MessageTag
import kotlin.Any


//用户上线、下线、等级变更
@MessageTag(value = SystemNoticeMessageContent.VALUE, flag = MessageTag.STATUS)
class SystemNoticeMessageContent : BaseCustomMessageContent, BaseMessageType.Normal {

    constructor() : super()
    constructor(content: String?) : super(content)
    constructor(data: ByteArray?) : super(data)
    constructor(source: Parcel?) : super(source)

    companion object {
        const val VALUE = "connectfriends:sysnotice"

        @JvmField
        val CREATOR: Parcelable.Creator<SystemNoticeMessageContent> = object : Parcelable.Creator<SystemNoticeMessageContent> {
            override fun createFromParcel(source: Parcel?): SystemNoticeMessageContent? {
                return SystemNoticeMessageContent(source)
            }

            override fun newArray(size: Int): Array<out SystemNoticeMessageContent?>? {
                return arrayOfNulls(size)
            }
        }
    }
}