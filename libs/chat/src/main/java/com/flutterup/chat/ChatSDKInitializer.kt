package com.flutterup.chat

import android.content.Context
import com.flutterup.chat.message.content.ConnectBaseMessageContent
import com.flutterup.chat.message.content.DataSyncBaseMessageContent
import com.flutterup.chat.message.content.GiftMessageContent
import com.flutterup.chat.message.content.MultiPrivateMessageContent
import com.flutterup.chat.message.content.PrivateMessageContent
import com.flutterup.chat.message.content.PublicMessageContent
import com.flutterup.chat.message.content.SystemActionMessageContent
import com.flutterup.chat.message.content.SystemNoticeMessageContent
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.InitOption
import io.rong.message.SightMessage

object ChatSDKInitializer {

    private val options = InitOption.Builder()
        .setAreaCode(InitOption.AreaCode.SG)
        .build()

    private val supportedMessages = mutableListOf(
        ConnectBaseMessageContent::class.java,
        DataSyncBaseMessageContent::class.java,
        MultiPrivateMessageContent::class.java,
        PrivateMessageContent::class.java,
        PublicMessageContent::class.java,
        SystemActionMessageContent::class.java,
        SystemNoticeMessageContent::class.java,
        GiftMessageContent::class.java,
        SightMessage::class.java
    )

    fun init(context: Context, key: String) {

        //如果重连时发现已有别的移动端设备在线，将不再重连，不影响已正常登录的移动端设备。
        RongIMClient.getInstance().setReconnectKickEnable(true)
        //设置单进程模式为关闭
        RongCoreClient.getInstance().enableSingleProcess(true)

        RongCoreClient.init(context, key, options)

        RongIMClient.registerMessageType(supportedMessages)
    }
}