package com.flutterup.chat.message.content

import android.os.Parcel
import com.flutterup.base.utils.JsonUtils
import com.flutterup.base.utils.Timber
import com.flutterup.base.utils.applicationEntryPoint
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.rong.common.ParcelUtils
import io.rong.imlib.model.MessageContent
import org.json.JSONException
import org.json.JSONObject
import java.io.UnsupportedEncodingException

abstract class BaseCustomMessageContent() : MessageContent() {

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface BaseCustomMessageContentEntryPoint {
        fun jsonUtils(): JsonUtils
    }

    private val cache = mutableMapOf<Class<*>, Any>()

    private val jsonUtils: JsonUtils by lazy {
        applicationEntryPoint<BaseCustomMessageContentEntryPoint>().jsonUtils()
    }

    /**
     * 设置文字消息的内容。
     * Params: content – 文字消息的内容。
     */
    var content: String? = null
        protected set

    constructor(content: String?) : this() {
        this.content = content
    }

    constructor(data: ByteArray?) : this() {
        if (data == null) {
            return
        }
        var jsonStr: String? = null
        try {
            jsonStr = String(data, Charsets.UTF_8)
        } catch (e: UnsupportedEncodingException ) {
            Timber.e(tag, "UnsupportedEncodingException ", e)
        }
        if (jsonStr == null) {
            Timber.e(tag, "jsonStr is null")
            return
        }
        try {
            val jsonObj = JSONObject(jsonStr)
            // 消息携带用户信息时, 自定义消息需添加下面代码
            if (jsonObj.has("user")) {
                userInfo = parseJsonToUserInfo(jsonObj.getJSONObject("user"))
            }
            // 用于群组聊天, 消息携带 @ 人信息时, 自定义消息需添加下面代码
            if (jsonObj.has("mentionedInfo")) {
                mentionedInfo = parseJsonToMentionInfo(jsonObj.getJSONObject("mentionedInfo"))
            }
            // 将所有自定义变量从收到的 json 解析并赋值
            if (jsonObj.has("content")) {
                content = jsonObj.optString("content")
            }
        } catch (e: JSONException) {
            Timber.e(tag, "JSONException ", e)
        }
    }

    constructor(source: Parcel?) : this() {
        extra = ParcelUtils.readFromParcel(source)
        content = ParcelUtils.readFromParcel(source)
    }

    override fun encode(): ByteArray? {
        val jsonObj = JSONObject()
        try {
            // 消息携带用户信息时, 自定义消息需添加下面代码
            if (jsonUserInfo != null) {
                jsonObj.putOpt("user", jsonUserInfo);
            }
            // 用于群组聊天, 消息携带 @ 人信息时, 自定义消息需添加下面代码
            if (jsonMentionInfo != null) {
                jsonObj.putOpt("mentionedInfo", jsonMentionInfo);
            }
            //  将所有自定义消息的内容，都序列化至 json 对象中
            jsonObj.put("content", this.content);
        } catch (e: JSONException) {
            Timber.e(tag, "JSONException ", e)
        }

        try {
            return jsonObj.toString().toByteArray(Charsets.UTF_8)
        } catch (e: UnsupportedEncodingException) {
            Timber.e(tag, "UnsupportedEncodingException ", e)
        }
        return null
    }

    override fun describeContents(): Int = 0

    override fun writeToParcel(dest: Parcel, i: Int) {
        // 对消息属性进行序列化，将类的数据写入外部提供的 Parcel 中
        ParcelUtils.writeToParcel(dest, extra)
        ParcelUtils.writeToParcel(dest, content)
    }


    @Suppress("UNCHECKED_CAST")
    fun <T> get(classType: Class<T>): T? {
        if (cache.contains(classType)) {
            return cache[classType] as? T
        }

        content?.let {
            val value = jsonUtils.fromJson(it, classType)
            if (value != null) {
                cache[classType] = value
            }
            return value
        }

        return null
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as BaseCustomMessageContent

        return content == other.content
    }

    override fun hashCode(): Int {
        return content?.hashCode() ?: 0
    }

    override fun toString(): String {
        return "$tag(content=$content)"
    }


    private val tag = this::class.java.simpleName
}