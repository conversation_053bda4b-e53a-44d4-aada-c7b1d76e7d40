package com.flutterup.chat.message.content

import android.os.Parcel
import android.os.Parcelable
import io.rong.imlib.MessageTag

@MessageTag(value = ConnectBaseMessageContent.VALUE, flag = MessageTag.ISPERSISTED)
class ConnectBaseMessageContent : BaseCustomMessageContent, BaseMessageType.Normal {


    constructor() : super()
    constructor(content: String?) : super(content)
    constructor(data: ByteArray?) : super(data)
    constructor(source: Parcel?) : super(source)

    companion object {
        const val VALUE = "connectfriends:connected"

        @JvmField
        val CREATOR: Parcelable.Creator<ConnectBaseMessageContent> = object : Parcelable.Creator<ConnectBaseMessageContent> {
            override fun createFromParcel(source: Parcel?): ConnectBaseMessageContent {
                return ConnectBaseMessageContent(source)
            }

            override fun newArray(size: Int): Array<ConnectBaseMessageContent?> {
                return arrayOfNulls(size)
            }
        }

        @JvmStatic
        fun obtain(content: String?) = ConnectBaseMessageContent(content)
    }
}