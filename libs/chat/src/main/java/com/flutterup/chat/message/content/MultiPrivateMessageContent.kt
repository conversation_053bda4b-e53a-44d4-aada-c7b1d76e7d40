package com.flutterup.chat.message.content

import android.os.Parcel
import android.os.Parcelable
import io.rong.imlib.MessageTag

@MessageTag(value = MultiPrivateMessageContent.VALUE, flag = MessageTag.ISCOUNTED)
class MultiPrivateMessageContent : BaseCustomMessageContent, BaseMessageType.Private {

    constructor() : super()
    constructor(content: String?) : super(content)
    constructor(data: ByteArray?) : super(data)
    constructor(source: Parcel?) : super(source)

    companion object {

        const val VALUE = "connectfriends:privacy_multiple"

        @JvmField
        val CREATOR: Parcelable.Creator<MultiPrivateMessageContent> = object : Parcelable.Creator<MultiPrivateMessageContent> {
            override fun createFromParcel(source: Parcel?): MultiPrivateMessageContent? {
                return MultiPrivateMessageContent(source)
            }

            override fun newArray(size: Int): Array<out MultiPrivateMessageContent?>? {
                return arrayOfNulls<MultiPrivateMessageContent>(size)
            }
        }

        @JvmStatic
        fun obtain(content: String?) = MultiPrivateMessageContent(content)
    }
}