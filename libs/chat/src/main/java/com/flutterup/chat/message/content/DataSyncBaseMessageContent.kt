package com.flutterup.chat.message.content

import android.os.Parcel
import android.os.Parcelable
import io.rong.imlib.MessageTag

@MessageTag(value = DataSyncBaseMessageContent.VALUE, flag = MessageTag.STATUS)
class DataSyncBaseMessageContent : BaseCustomMessageContent, BaseMessageType.Normal {

    constructor() : super()
    constructor(content: String?) : super(content)
    constructor(data: ByteArray?) : super(data)
    constructor(`in`: Parcel?) : super(`in`)

    companion object {
        const val VALUE = "connectfriends:datasyn"

        @JvmField
        val CREATOR: Parcelable.Creator<DataSyncBaseMessageContent> = object : Parcelable.Creator<DataSyncBaseMessageContent> {
            override fun createFromParcel(source: Parcel): DataSyncBaseMessageContent {
                return DataSyncBaseMessageContent(source)
            }

            override fun newArray(size: Int): Array<DataSyncBaseMessageContent?> {
                return arrayOfNulls(size)
            }
        }
    }
}