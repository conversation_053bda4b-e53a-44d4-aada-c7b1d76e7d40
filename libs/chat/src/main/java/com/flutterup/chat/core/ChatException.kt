package com.flutterup.chat.core

import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.RongIMClient

class ChatException : RuntimeException {

    val code: RongIMClient.ErrorCode?
    val coreCode: IRongCoreEnum.CoreErrorCode?

    constructor(code: IRongCoreEnum.CoreErrorCode?) : super(code?.message) {
        this.code = null
        this.coreCode = code
    }

    constructor(code: RongIMClient.ErrorCode?) : super(code?.message) {
        this.code = code
        this.coreCode = null
    }

    constructor(message: String?) : super(message) {
        this.code = null
        this.coreCode = null
    }

    override fun toString(): String {
        return "ChatCoreException(code=$code, coreCode=$coreCode, message=${super.message})"
    }
}