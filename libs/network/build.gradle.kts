plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.parcelize)
}

apply(from = "${rootDir}/gradles/buildBase.gradle")
apply(from = "${rootDir}/gradles/buildHilt.gradle")


android {
    namespace = "com.flutterup.network"
}

dependencies {
    implementation(project(":libs:base"))

    api(libs.retrofit)
    implementation(libs.retrofit.moshi)
    implementation(libs.retrofit.scalars)
    api(libs.okhttp)
    api(libs.okhttp.logging)
}