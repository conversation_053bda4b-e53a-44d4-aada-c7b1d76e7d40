package com.flutterup.network

import com.flutterup.base.utils.Timber
import com.flutterup.network.impl.NetworkServiceProvider
import com.squareup.moshi.Moshi
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class NetworkService @Inject constructor(
    params: Params,
    moshi: <PERSON><PERSON>
) : NetworkServiceProvider(params, moshi) {
    companion object {
        private const val TAG = "NetworkService"
    }

    override fun okHttpClient(): OkHttpClient {
        val loggingInterceptor = HttpLoggingInterceptor(logger = Logger()).apply {
            setLevel(params.level)
        }

        return OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .addInterceptor(ResponseSuccessfulInterceptor())
            .apply {
                if (params.connectTimeout > 0) {
                    connectTimeout(params.connectTimeout, TimeUnit.MILLISECONDS)
                }
                if (params.readTimeout > 0) {
                    readTimeout(params.readTimeout, TimeUnit.MILLISECONDS)
                }
                if (params.writeTimeout > 0) {
                    writeTimeout(params.writeTimeout, TimeUnit.MILLISECONDS)
                }
                if (params.retryOnConnectionFailure) {
                    retryOnConnectionFailure(true)
                }
                if (params.interceptors.isNotEmpty()) {
                    for (interceptor in params.interceptors) {
                        addInterceptor(interceptor)
                    }
                }
            }
            .build()
    }

    private class Logger : HttpLoggingInterceptor.Logger {
        override fun log(message: String) {
            Timber.i(TAG, message)
        }
    }
}