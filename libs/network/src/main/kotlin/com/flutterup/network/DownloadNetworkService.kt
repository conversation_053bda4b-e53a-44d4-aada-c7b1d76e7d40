package com.flutterup.network

import com.flutterup.network.impl.NetworkServiceProvider
import com.squareup.moshi.Moshi
import okhttp3.OkHttpClient
import javax.inject.Inject

class DownloadNetworkService @Inject constructor(
    params: Params,
    moshi: <PERSON><PERSON>
) : NetworkServiceProvider(params, moshi) {
    override fun okHttpClient(): OkHttpClient = OkHttpClient.Builder()
        .addInterceptor { chain ->
            val newRequest = chain.request().newBuilder()
                .addHeader("Accept", "image/webp,image/apng,*/*;")
                .build()
            chain.proceed(newRequest)
        }
        .build()
}