package com.flutterup.network.impl

import com.squareup.moshi.Moshi
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.moshi.MoshiConverterFactory
import retrofit2.converter.scalars.ScalarsConverterFactory
import java.util.concurrent.ConcurrentHashMap

abstract class NetworkServiceProvider(
    protected val params: Params,
    protected val moshi: <PERSON>shi
) {
    companion object {
        private const val PREFIX = "https://"

        private const val DEFAULT_TIME_OUT = 30_000L
    }

    private val retrofit: Retrofit

    private val serviceHolder: ConcurrentHashMap<Class<*>, Any>

    init {
        val baseUrl = PREFIX + params.host

        this.retrofit = Retrofit.Builder()
            .baseUrl(baseUrl)
            .addConverterFactory(MoshiConverterFactory.create(moshi))
            .addConverterFactory(ScalarsConverterFactory.create())
            .client(okHttpClient())
            .build()

        this.serviceHolder = ConcurrentHashMap()
    }

    @Suppress("UNCHECKED_CAST")
    operator fun <T : Any> get(clazz: Class<T>): T =
        serviceHolder.getOrPut(clazz) { retrofit.create<T>(clazz) } as T


    abstract fun okHttpClient(): OkHttpClient

    data class Params(
        val host: String,

        val connectTimeout: Long = DEFAULT_TIME_OUT,

        val readTimeout: Long = DEFAULT_TIME_OUT,

        val writeTimeout: Long = DEFAULT_TIME_OUT,

        val retryOnConnectionFailure: Boolean = false,

        val interceptors: List<Interceptor> = emptyList(),

        val level: HttpLoggingInterceptor.Level = HttpLoggingInterceptor.Level.BODY,
    )
}