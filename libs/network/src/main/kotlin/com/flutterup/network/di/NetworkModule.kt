package com.flutterup.network.di

import com.flutterup.network.DownloadApiService
import com.flutterup.network.AppClient
import com.flutterup.network.AppClients
import com.flutterup.network.NetworkService
import com.flutterup.network.ConnectivityManagerNetworkMonitor
import com.flutterup.network.DownloadNetworkService
import com.flutterup.network.impl.NetworkMonitor
import com.flutterup.network.impl.NetworkServiceProvider
import com.squareup.moshi.Moshi
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    @Provides
    @Singleton
    @AppClient(AppClients.DOWNLOAD)
    fun provideDownloadNetworkService(
        params: NetworkServiceProvider.Params,
        moshi: Moshi
    ): NetworkServiceProvider {
        return DownloadNetworkService(params, moshi)
    }

    @Provides
    @Singleton
    @AppClient(AppClients.NORMAL)
    fun provideNetworkService(
        params: NetworkServiceProvider.Params,
        moshi: <PERSON><PERSON>
    ): NetworkServiceProvider {
        return NetworkService(params, moshi)
    }
}

@Module
@InstallIn(SingletonComponent::class)
abstract class NetworkMonitorModule {

    @Binds
    internal abstract fun bindNetworkMonitor(networkMonitor: ConnectivityManagerNetworkMonitor): NetworkMonitor
}