package com.flutterup.network

import com.flutterup.base.utils.JsonUtils
import com.flutterup.base.utils.applicationEntryPoint
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Types
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.Interceptor
import okhttp3.Response
import okio.Buffer
import okio.GzipSource
import java.nio.charset.StandardCharsets

class ResponseSuccessfulInterceptor() : Interceptor {

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface ResponseSuccessfulEntryPoint {
        fun jsonUtils(): JsonUtils
    }

    private val jsonUtils: JsonUtils by lazy {
        applicationEntryPoint<ResponseSuccessfulEntryPoint>().jsonUtils()
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)

        if (!response.isSuccessful) {
            return response
        }

        val responseBody = response.body ?: return response

        val contentType = responseBody.contentType()
        val source = responseBody.source()
        source.request(Long.MAX_VALUE)
        var buffer = source.buffer.clone()

        // 处理gzip压缩
        if ("gzip".equals(response.headers["Content-Encoding"], ignoreCase = true)) {
            GzipSource(buffer.clone()).use { gzippedResponseBody ->
                buffer = Buffer()
                buffer.writeAll(gzippedResponseBody)
            }
        }

        val charset = contentType?.charset(StandardCharsets.UTF_8) ?: return response
        val responseBodyString = buffer.readString(charset)

        try {
            // 解析响应体
            // 解析NetworkResponse
            val type = Types.newParameterizedType(BaseResponse::class.java, Any::class.java)
            val adapter: JsonAdapter<BaseResponse<String>> = jsonUtils.moshi.adapter(type)
            val baseResponse = jsonUtils.fromJson(responseBodyString, adapter) ?: return response

            // 如果code不为0，抛出NetworkException
            if (!baseResponse.isSuccess) {
                throw ApiException(baseResponse, request.url.toString())
            }

            // 重新创建ResponseBody并返回原始响应
            return response
        } catch (e: Exception) {
            if (e is ApiException) {
                throw e
            }
            // 解析失败时返回原始响应
            return response
        }
    }
}